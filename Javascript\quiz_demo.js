/**
 * JavaScript Manual Quiz Demo
 * Demonstrates how to use the quiz system
 */

// Import the quiz system (if using Node.js modules)
// const { printQuiz, listTopics, randomQuiz, getQuizStats, searchQuestions } = require('./javascript_manual_quiz.js');

// For browser usage, just include the script and use the functions directly

console.log('🎯 JavaScript Manual Quiz Demo');
console.log('='.repeat(40));

// Demo 1: Show a specific topic quiz
console.log('\n📚 Demo 1: Basic Syntax Quiz (with answers)');
// Uncomment to run: printQuiz("Basic Syntax and Data Types", true);

// Demo 2: Random quiz
console.log('\n🎲 Demo 2: Random Quiz (5 questions)');
// Uncomment to run: randomQuiz(5, true);

// Demo 3: Search functionality
console.log('\n🔍 Demo 3: Search for "function" related questions');
// Uncomment to run: searchQuestions("function", true);

// Demo 4: Quiz statistics
console.log('\n📊 Demo 4: Quiz Statistics');
// Uncomment to run: getQuizStats();

console.log('\n💡 To run these demos:');
console.log('1. Uncomment the function calls above');
console.log('2. Run: node quiz_demo.js');
console.log('3. Or load in browser and use browser console');

console.log('\n🚀 Interactive Usage:');
console.log('Load the quiz file and try these commands:');
console.log('• listTopics()');
console.log('• printQuiz("Arrays and Array Methods")');
console.log('• randomQuiz(3, true)');
console.log('• searchQuestions("promise")');
console.log('• getQuizStats()');
