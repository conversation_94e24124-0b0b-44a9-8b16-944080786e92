"""
Comprehensive Python Programming Manual Quiz
============================================

This file contains quiz questions and answers for all 21 sections of the 
Comprehensive Python Programming Manual. Each section has 5-10 targeted 
questions covering key concepts.

Usage:
    python python_manual_quiz.py
    
    # Or import and use programmatically:
    from python_manual_quiz import print_quiz, quizzes
    print_quiz("Basic Syntax and Data Types")
    print_quiz("Control Flow", with_answers=True)
"""

quizzes = {
    "Basic Syntax and Data Types": [
        {
            "question": "What is dynamic typing in Python and how does it differ from static typing?",
            "answer": "Python determines variable types at runtime rather than compile-time. Variables can change types during execution, unlike statically typed languages where types are fixed at compile time."
        },
        {
            "question": "How do you create and use f-strings for string formatting?",
            "answer": "Use f-string syntax: f'Hello, {name}!' where variables are embedded in curly braces within a string prefixed with 'f'."
        },
        {
            "question": "What are the main numeric data types in Python and their differences?",
            "answer": "int (integers), float (decimal numbers), complex (complex numbers with real and imaginary parts). Integers have unlimited precision, floats are double-precision."
        },
        {
            "question": "How do you perform floor division and what's the difference from regular division?",
            "answer": "Floor division uses // operator and returns the largest integer less than or equal to the result. Regular division (/) always returns a float."
        },
        {
            "question": "What is the difference between '==' and 'is' operators?",
            "answer": "'==' compares values for equality, 'is' compares object identity (whether two variables point to the same object in memory)."
        },
        {
            "question": "How do you convert between different data types in Python?",
            "answer": "Use built-in functions: int(), float(), str(), bool(), list(), tuple(), etc. For example: int('123') converts string to integer."
        },
        {
            "question": "What are the boolean values in Python and what evaluates to False?",
            "answer": "Boolean values are True and False. Values that evaluate to False: False, None, 0, 0.0, empty containers ([], {}, '', etc.)."
        }
    ],
    
    "Control Flow": [
        {
            "question": "What is the syntax for if/elif/else statements in Python?",
            "answer": "if condition: block elif condition: block else: block. Note the colons and indentation for code blocks."
        },
        {
            "question": "How do for loops work with different iterable types?",
            "answer": "for item in iterable: block. Works with lists, strings, ranges, dictionaries, etc. Use range() for numeric sequences."
        },
        {
            "question": "What's the difference between break and continue statements?",
            "answer": "break exits the entire loop immediately. continue skips the rest of the current iteration and moves to the next iteration."
        },
        {
            "question": "How do you use enumerate() and what does it return?",
            "answer": "enumerate(iterable) returns tuples of (index, value). Useful for getting both index and value in loops: for i, item in enumerate(list):"
        },
        {
            "question": "What is list comprehension and how does it work?",
            "answer": "Concise way to create lists: [expression for item in iterable if condition]. Example: [x**2 for x in range(10) if x % 2 == 0]"
        },
        {
            "question": "How do while loops work and when should you use them?",
            "answer": "while condition: block. Continues until condition becomes False. Use when you don't know the exact number of iterations needed."
        },
        {
            "question": "What is the purpose of the pass statement?",
            "answer": "pass is a null operation - does nothing when executed. Used as a placeholder where syntax requires a statement but no action is needed."
        }
    ],
    
    "Functions and Lambda Expressions": [
        {
            "question": "How do you define a function with default parameters?",
            "answer": "def function_name(param1, param2=default_value): return result. Default parameters must come after positional parameters."
        },
        {
            "question": "What are *args and **kwargs and how are they used?",
            "answer": "*args collects extra positional arguments into a tuple. **kwargs collects extra keyword arguments into a dictionary. Used for variable-length argument lists."
        },
        {
            "question": "How do you create and use lambda functions?",
            "answer": "lambda arguments: expression. Anonymous functions for simple operations: square = lambda x: x**2. Often used with map(), filter(), sort()."
        },
        {
            "question": "What is the difference between return and yield in functions?",
            "answer": "return exits function and returns a value. yield creates a generator function that can be paused and resumed, yielding values one at a time."
        },
        {
            "question": "How do you document functions properly?",
            "answer": "Use docstrings with triple quotes immediately after function definition. Include description, Args, Returns, and Examples sections."
        },
        {
            "question": "What are higher-order functions and how do you create them?",
            "answer": "Functions that take other functions as arguments or return functions. Example: def create_multiplier(n): return lambda x: x * n"
        },
        {
            "question": "How do you handle multiple return values from a function?",
            "answer": "Return a tuple: return value1, value2. Unpack when calling: a, b = function(). Python automatically packs/unpacks tuples."
        }
    ],

    "Data Structures": [
        {
            "question": "What are the main differences between lists and tuples?",
            "answer": "Lists are mutable (can be changed), use square brackets [], and have methods like append(). Tuples are immutable, use parentheses (), and are hashable (can be dict keys)."
        },
        {
            "question": "How do you slice lists and what does the syntax mean?",
            "answer": "list[start:end:step]. start is inclusive, end is exclusive. Negative indices count from end. Examples: list[:3] (first 3), list[-3:] (last 3), list[::2] (every 2nd)."
        },
        {
            "question": "What are sets and when should you use them?",
            "answer": "Sets are unordered collections of unique elements. Use for removing duplicates, membership testing, and set operations (union, intersection, difference)."
        },
        {
            "question": "How do dictionary comprehensions work?",
            "answer": "{key_expr: value_expr for item in iterable if condition}. Example: {x: x**2 for x in range(5)} creates {0: 0, 1: 1, 2: 4, 3: 9, 4: 16}."
        },
        {
            "question": "What is the difference between dict.get() and dict[key]?",
            "answer": "dict[key] raises KeyError if key doesn't exist. dict.get(key, default) returns default value (None if not specified) if key doesn't exist."
        },
        {
            "question": "How do you use defaultdict and when is it useful?",
            "answer": "from collections import defaultdict. Automatically creates missing values with a factory function: defaultdict(list) creates empty lists for new keys."
        },
        {
            "question": "What are named tuples and their advantages?",
            "answer": "Tuples with named fields: Point = namedtuple('Point', ['x', 'y']). Combine tuple immutability with attribute access: point.x instead of point[0]."
        }
    ],

    "File Operations": [
        {
            "question": "What is the recommended way to open and read files in Python?",
            "answer": "Use context managers: with open('file.txt', 'r') as f: content = f.read(). Automatically closes file even if exceptions occur."
        },
        {
            "question": "What are the different file modes and their purposes?",
            "answer": "'r' (read), 'w' (write, overwrites), 'a' (append), 'x' (exclusive creation), 'b' (binary), 't' (text). Combine like 'rb' for binary read."
        },
        {
            "question": "How do you work with file paths using pathlib?",
            "answer": "from pathlib import Path. Path('file.txt').exists(), Path.cwd(), path.parent, path.name, path.suffix. More object-oriented than os.path."
        },
        {
            "question": "What's the difference between read(), readline(), and readlines()?",
            "answer": "read() reads entire file as string. readline() reads one line. readlines() reads all lines into a list. Use iteration for memory efficiency: for line in file:"
        },
        {
            "question": "How do you handle different text encodings when reading files?",
            "answer": "Specify encoding parameter: open('file.txt', 'r', encoding='utf-8'). Common encodings: utf-8, ascii, latin-1. Always specify encoding explicitly."
        },
        {
            "question": "How do you copy, move, and delete files safely?",
            "answer": "Use shutil module: shutil.copy2() (preserves metadata), shutil.move(), os.remove() for files, shutil.rmtree() for directories. Check existence first."
        }
    ],

    "Exception Handling": [
        {
            "question": "What is the structure of try/except/else/finally blocks?",
            "answer": "try: risky_code except ExceptionType: handle_error else: runs_if_no_exception finally: always_runs. else and finally are optional."
        },
        {
            "question": "How do you catch multiple exception types?",
            "answer": "except (ValueError, TypeError): or separate except blocks. Use except Exception as e: to catch most exceptions and access the exception object."
        },
        {
            "question": "When should you use custom exceptions?",
            "answer": "When you need domain-specific error handling. Create by inheriting from Exception: class CustomError(Exception): pass. Helps distinguish your errors from system errors."
        },
        {
            "question": "What's the difference between except Exception and bare except?",
            "answer": "except Exception: catches most exceptions but not system-exiting ones (KeyboardInterrupt, SystemExit). Bare except: catches everything (not recommended)."
        },
        {
            "question": "How do you re-raise exceptions and why would you?",
            "answer": "Use bare raise statement in except block. Allows logging/cleanup while preserving original exception and traceback for higher-level handlers."
        },
        {
            "question": "What are the best practices for exception handling?",
            "answer": "Be specific with exception types, handle at appropriate level, log errors with context, fail fast, use finally for cleanup, don't ignore exceptions silently."
        }
    ],

    "Object-Oriented Programming": [
        {
            "question": "How do you define a class with an initializer method?",
            "answer": "class ClassName: def __init__(self, params): self.attribute = value. __init__ is called when creating instances, self refers to the instance."
        },
        {
            "question": "What is inheritance and how do you implement it?",
            "answer": "class Child(Parent): inherits Parent's methods and attributes. Use super() to call parent methods: super().__init__(). Enables code reuse and polymorphism."
        },
        {
            "question": "What are property decorators and how do you use them?",
            "answer": "@property creates getter, @property_name.setter creates setter. Allows method-like access to attributes with validation: obj.value = 10 calls setter method."
        },
        {
            "question": "What's the difference between class methods and static methods?",
            "answer": "@classmethod receives class as first parameter (cls), can access class attributes. @staticmethod has no special first parameter, just a function in class namespace."
        },
        {
            "question": "How do special methods (__str__, __repr__, etc.) work?",
            "answer": "Define object behavior: __str__ for user-friendly string, __repr__ for developer representation, __len__ for len(), __eq__ for ==, etc."
        },
        {
            "question": "What is multiple inheritance and the method resolution order?",
            "answer": "class Child(Parent1, Parent2): inherits from multiple classes. MRO determines which method is called. Use ClassName.__mro__ to see order."
        },
        {
            "question": "How do you create and use dataclasses?",
            "answer": "@dataclass decorator automatically generates __init__, __repr__, __eq__. from dataclasses import dataclass. Reduces boilerplate for simple classes."
        }
    ],

    "Modules and Packages": [
        {
            "question": "What's the difference between import, from import, and import as?",
            "answer": "import module (access as module.function), from module import function (direct access), import module as alias (custom name)."
        },
        {
            "question": "How do you create a Python package?",
            "answer": "Create directory with __init__.py file. __init__.py can be empty or contain initialization code. Subdirectories with __init__.py become subpackages."
        },
        {
            "question": "What is the purpose of __init__.py files?",
            "answer": "Marks directory as Python package, controls what's imported with 'from package import *', can contain package initialization code."
        },
        {
            "question": "How do relative imports work and when should you use them?",
            "answer": "from .module import function (same package), from ..package import module (parent package). Use in packages, not standalone scripts."
        },
        {
            "question": "What are __name__ and __main__ used for?",
            "answer": "__name__ is module name, '__main__' when run directly. if __name__ == '__main__': allows code to run only when script is executed, not imported."
        },
        {
            "question": "How does Python's module search path work?",
            "answer": "sys.path list: current directory, PYTHONPATH, standard library, site-packages. Python searches in order until module found."
        }
    ],

    "Standard Library Features": [
        {
            "question": "What are the key features of the collections module?",
            "answer": "Counter (counting), defaultdict (default values), deque (double-ended queue), namedtuple (named fields), OrderedDict (insertion order)."
        },
        {
            "question": "How do you use itertools for advanced iteration?",
            "answer": "combinations(), permutations(), cycle(), count(), chain(). Create efficient iterators for complex iteration patterns without loading all data in memory."
        },
        {
            "question": "What does functools provide for functional programming?",
            "answer": "reduce() (cumulative operations), partial() (partial function application), lru_cache() (memoization), wraps() (decorator preservation)."
        },
        {
            "question": "How do you use the statistics module?",
            "answer": "mean(), median(), mode(), stdev(), variance(). Provides statistical functions for numeric data analysis without external libraries."
        },
        {
            "question": "What are the main features of the pathlib module?",
            "answer": "Object-oriented path handling: Path.cwd(), path.exists(), path.mkdir(), path.glob(). More intuitive than os.path functions."
        },
        {
            "question": "How do you generate UUIDs and when are they useful?",
            "answer": "import uuid; uuid.uuid4() (random), uuid.uuid1() (MAC+timestamp). Useful for unique identifiers in databases, distributed systems."
        }
    ],

    "Advanced Features": [
        {
            "question": "What are iterators and how do you create custom ones?",
            "answer": "Objects implementing __iter__() and __next__(). __iter__ returns self, __next__ returns next value or raises StopIteration. Enable for-loop compatibility."
        },
        {
            "question": "How do generators work and what are their advantages?",
            "answer": "Functions using yield keyword. Lazy evaluation, memory efficient, maintain state between calls. Generator expressions: (x for x in iterable)."
        },
        {
            "question": "What are decorators and how do you create them?",
            "answer": "Functions that modify other functions. def decorator(func): def wrapper(*args, **kwargs): # modify behavior return func(*args, **kwargs) return wrapper"
        },
        {
            "question": "How do you create decorators with parameters?",
            "answer": "def decorator_with_params(param): def decorator(func): def wrapper(*args, **kwargs): # use param return func(*args, **kwargs) return wrapper return decorator"
        },
        {
            "question": "What are context managers and how do you create them?",
            "answer": "Objects with __enter__ and __exit__ methods. Use with statement. @contextmanager decorator with yield for simpler creation."
        },
        {
            "question": "How does the property decorator work internally?",
            "answer": "Creates descriptor objects with __get__, __set__, __delete__ methods. Allows method calls to appear as attribute access with validation."
        },
        {
            "question": "What are metaclasses and when might you use them?",
            "answer": "Classes that create classes. Control class creation process. Advanced feature for frameworks. 'Classes are instances of metaclasses'."
        }
    ],

    "Static Typing": [
        {
            "question": "How do you add type hints to function parameters and return values?",
            "answer": "def function(param: int, optional: str = 'default') -> bool: return True. Use typing module for complex types."
        },
        {
            "question": "What are generic types and how do you use them?",
            "answer": "from typing import List, Dict, Optional. List[int], Dict[str, int], Optional[str] (Union[str, None]). Specify container element types."
        },
        {
            "question": "How do you create generic classes with TypeVar?",
            "answer": "T = TypeVar('T'); class Stack(Generic[T]): def push(self, item: T) -> None: ... Allows type-safe generic containers."
        },
        {
            "question": "What are Protocol classes and structural subtyping?",
            "answer": "class Drawable(Protocol): def draw(self) -> str: ... Any class with draw() method satisfies Drawable protocol without explicit inheritance."
        },
        {
            "question": "How do you use Union types and when are they needed?",
            "answer": "Union[int, str] accepts either int or str. Use when function can handle multiple types. Python 3.10+ supports int | str syntax."
        },
        {
            "question": "What tools can check Python type hints?",
            "answer": "mypy (most popular), pyright, pyre. Run mypy filename.py to check types. IDEs like PyCharm, VS Code provide real-time type checking."
        }
    ],

    "Regular Expressions": [
        {
            "question": "What are the basic regex metacharacters and their meanings?",
            "answer": ". (any char), * (0+ times), + (1+ times), ? (0-1 times), ^ (start), $ (end), \\d (digit), \\w (word char), \\s (whitespace)."
        },
        {
            "question": "How do you use groups in regex and extract matched content?",
            "answer": "Parentheses create groups: r'(\\d{4})-(\\d{2})-(\\d{2})'. Access with match.groups() or match.group(1). Named groups: (?P<name>pattern)."
        },
        {
            "question": "What's the difference between greedy and non-greedy matching?",
            "answer": "Greedy (* + {}) matches as much as possible. Non-greedy (*? +? {}?) matches as little as possible. Important for HTML/XML parsing."
        },
        {
            "question": "How do you use lookahead and lookbehind assertions?",
            "answer": "(?=pattern) positive lookahead, (?!pattern) negative lookahead, (?<=pattern) positive lookbehind, (?<!pattern) negative lookbehind. Match without consuming."
        },
        {
            "question": "When should you compile regex patterns?",
            "answer": "re.compile() when using same pattern multiple times. Improves performance by avoiding recompilation. Store compiled pattern in variable."
        },
        {
            "question": "How do you use regex for text substitution?",
            "answer": "re.sub(pattern, replacement, text). Use \\1, \\2 for group references in replacement. re.subn() returns (new_string, count)."
        }
    ],

    "DateTime Operations": [
        {
            "question": "How do you create datetime objects and what's the difference between date, time, and datetime?",
            "answer": "date (year, month, day), time (hour, minute, second), datetime (both). datetime.now(), datetime(2025, 8, 9, 15, 30)."
        },
        {
            "question": "How do you parse and format datetime strings?",
            "answer": "strptime() parses strings to datetime, strftime() formats datetime to strings. Use format codes: %Y (year), %m (month), %d (day), %H (hour)."
        },
        {
            "question": "How do you perform date arithmetic with timedelta?",
            "answer": "timedelta(days=1, hours=2, minutes=30). Add/subtract from datetime: tomorrow = datetime.now() + timedelta(days=1)."
        },
        {
            "question": "How do you work with timezones in Python?",
            "answer": "Use zoneinfo (Python 3.9+): ZoneInfo('UTC'), ZoneInfo('America/New_York'). datetime.now(ZoneInfo('UTC')) for timezone-aware datetime."
        },
        {
            "question": "What's the difference between naive and aware datetime objects?",
            "answer": "Naive datetimes have no timezone info (.tzinfo is None). Aware datetimes include timezone. Always use aware datetimes for production applications."
        },
        {
            "question": "How do you convert between different timezones?",
            "answer": "Use .astimezone(): utc_time.astimezone(ZoneInfo('America/New_York')). Only works with timezone-aware datetime objects."
        }
    ],

    "Context Managers": [
        {
            "question": "What are context managers and what problem do they solve?",
            "answer": "Objects that define runtime context for executing code blocks. Ensure proper resource cleanup (files, locks, connections) even if exceptions occur."
        },
        {
            "question": "How do you create custom context managers using classes?",
            "answer": "Implement __enter__ and __exit__ methods. __enter__ returns resource, __exit__ handles cleanup and receives exception info."
        },
        {
            "question": "How do you create context managers using the @contextmanager decorator?",
            "answer": "from contextlib import contextmanager. @contextmanager def my_context(): setup; yield resource; cleanup. Use try/finally for guaranteed cleanup."
        },
        {
            "question": "What parameters does the __exit__ method receive?",
            "answer": "__exit__(self, exc_type, exc_value, traceback). If no exception: all None. Return True to suppress exceptions, False to propagate."
        },
        {
            "question": "How do you use multiple context managers in one with statement?",
            "answer": "with open('file1') as f1, open('file2') as f2: or with ExitStack() for dynamic number of context managers."
        },
        {
            "question": "What are some built-in context managers in Python?",
            "answer": "open() (files), threading.Lock(), decimal.localcontext(), tempfile.TemporaryDirectory(), unittest.mock.patch()."
        }
    ],

    "Virtual Environments": [
        {
            "question": "Why should you use virtual environments?",
            "answer": "Isolate project dependencies, avoid version conflicts, ensure reproducible environments, prevent system Python pollution, enable per-project package versions."
        },
        {
            "question": "How do you create and activate a virtual environment?",
            "answer": "python -m venv myenv (create), source myenv/bin/activate (Linux/Mac) or myenv\\Scripts\\activate (Windows). Deactivate with 'deactivate'."
        },
        {
            "question": "What is requirements.txt and how do you use it?",
            "answer": "Lists project dependencies. Generate: pip freeze > requirements.txt. Install: pip install -r requirements.txt. Pin versions for reproducibility."
        },
        {
            "question": "What's the difference between pip freeze and pip list?",
            "answer": "pip freeze shows installed packages in requirements format. pip list shows all packages with versions in readable format."
        },
        {
            "question": "How do you manage different Python versions for projects?",
            "answer": "Use pyenv (version management), python3.x -m venv (specific version), or conda environments. Specify Python version when creating venv."
        },
        {
            "question": "What are the best practices for virtual environment management?",
            "answer": "One venv per project, keep requirements.txt updated, use version pinning, don't commit venv to version control, document Python version requirements."
        }
    ],

    "Testing": [
        {
            "question": "What are the main types of tests and their purposes?",
            "answer": "Unit tests (individual functions), integration tests (component interaction), functional tests (user scenarios), performance tests (speed/memory)."
        },
        {
            "question": "How do you write basic unit tests with unittest?",
            "answer": "import unittest; class TestClass(unittest.TestCase): def test_method(self): self.assertEqual(actual, expected). Run with python -m unittest."
        },
        {
            "question": "What are pytest fixtures and how do you use them?",
            "answer": "@pytest.fixture def setup_data(): return data. Use as function parameters. Scope options: function, class, module, session. Automatic cleanup."
        },
        {
            "question": "How do you test exceptions in your code?",
            "answer": "unittest: with self.assertRaises(ValueError): function(). pytest: with pytest.raises(ValueError): function(). Test both that exception is raised and message."
        },
        {
            "question": "What is test-driven development (TDD)?",
            "answer": "Write tests before code: Red (failing test) → Green (minimal code to pass) → Refactor (improve code). Ensures testable, focused code."
        },
        {
            "question": "How do you mock dependencies in tests?",
            "answer": "from unittest.mock import Mock, patch. Mock objects simulate dependencies. @patch decorator replaces imports. Verify calls with assert_called_with()."
        },
        {
            "question": "What is test coverage and how do you measure it?",
            "answer": "Percentage of code executed by tests. Use coverage.py: pip install coverage, coverage run -m pytest, coverage report. Aim for >80% but focus on critical paths."
        }
    ],

    "Logging": [
        {
            "question": "What are the different logging levels and when to use them?",
            "answer": "DEBUG (detailed info), INFO (general info), WARNING (potential issues), ERROR (errors), CRITICAL (serious errors). Set level to filter messages."
        },
        {
            "question": "How do you configure logging with basicConfig?",
            "answer": "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', filename='app.log')."
        },
        {
            "question": "What's the difference between loggers, handlers, and formatters?",
            "answer": "Loggers create log records, handlers send records to destinations (file, console), formatters control output format. Hierarchical logger names."
        },
        {
            "question": "How do you create and use custom loggers?",
            "answer": "logger = logging.getLogger(__name__). Use module name for hierarchical organization. Configure handlers and formatters separately."
        },
        {
            "question": "How do you log exceptions with full tracebacks?",
            "answer": "logger.exception('message') in except block, or logger.error('message', exc_info=True). Automatically includes traceback information."
        },
        {
            "question": "What are the best practices for logging in applications?",
            "answer": "Use appropriate levels, include context, don't log sensitive data, use structured logging (JSON), configure via files, rotate log files, test logging."
        }
    ],

    "External Libraries": [
        {
            "question": "How do you install and manage external packages?",
            "answer": "pip install package_name, pip install -r requirements.txt, pip freeze > requirements.txt. Use virtual environments to isolate dependencies."
        },
        {
            "question": "What are the key features of NumPy for numerical computing?",
            "answer": "N-dimensional arrays, vectorized operations, broadcasting, mathematical functions, linear algebra. Foundation for scientific Python ecosystem."
        },
        {
            "question": "How do you perform basic data manipulation with Pandas?",
            "answer": "DataFrame for tabular data, Series for 1D data. df.groupby(), df.merge(), df.pivot_table(), df.describe(). Read/write CSV, JSON, Excel."
        },
        {
            "question": "What are the main plotting libraries and their use cases?",
            "answer": "Matplotlib (basic plots), Seaborn (statistical plots), Plotly (interactive), Bokeh (web-based). Choose based on complexity and interactivity needs."
        },
        {
            "question": "How do you make HTTP requests with the requests library?",
            "answer": "requests.get(url), requests.post(url, data=data), requests.put(), requests.delete(). Handle responses: .json(), .text, .status_code."
        },
        {
            "question": "What are some popular libraries for different domains?",
            "answer": "Web: Django, Flask, FastAPI. ML: scikit-learn, TensorFlow, PyTorch. Data: pandas, NumPy. GUI: tkinter, PyQt. Testing: pytest, unittest."
        }
    ],

    "Data Science Basics": [
        {
            "question": "What is the typical data science workflow?",
            "answer": "Data collection → Cleaning → Exploration (EDA) → Feature engineering → Modeling → Evaluation → Deployment. Iterative process with feedback loops."
        },
        {
            "question": "How do you perform exploratory data analysis (EDA)?",
            "answer": "df.describe(), df.info(), df.head(), value_counts(), correlation matrices, histograms, box plots, scatter plots. Understand data before modeling."
        },
        {
            "question": "What are common data cleaning tasks?",
            "answer": "Handle missing values (fillna, dropna), remove duplicates, fix data types, standardize formats, validate ranges, handle outliers."
        },
        {
            "question": "How do you split data for machine learning?",
            "answer": "train_test_split() from sklearn. Typically 80/20 or 70/30 split. Use stratify for classification, random_state for reproducibility."
        },
        {
            "question": "What are the basic steps in a machine learning pipeline?",
            "answer": "Data preprocessing → Feature selection → Model training → Validation → Hyperparameter tuning → Testing → Evaluation metrics."
        },
        {
            "question": "How do you handle categorical variables in machine learning?",
            "answer": "One-hot encoding (pd.get_dummies), label encoding, target encoding. Choose based on cardinality and relationship to target variable."
        },
        {
            "question": "What are common evaluation metrics for classification and regression?",
            "answer": "Classification: accuracy, precision, recall, F1-score, ROC-AUC. Regression: MSE, RMSE, MAE, R². Choose based on problem requirements."
        }
    ],

    "Best Practices": [
        {
            "question": "What are the key principles of PEP 8 style guide?",
            "answer": "4 spaces for indentation, snake_case for variables/functions, PascalCase for classes, line length ≤79 chars, meaningful names, docstrings."
        },
        {
            "question": "How should you structure error handling in applications?",
            "answer": "Be specific with exceptions, handle at appropriate level, log with context, fail fast, use finally for cleanup, don't ignore exceptions."
        },
        {
            "question": "What are the SOLID principles as applied to Python?",
            "answer": "Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion. Guide object-oriented design decisions."
        },
        {
            "question": "How do you write maintainable code?",
            "answer": "Clear naming, small functions, avoid deep nesting, write tests, document complex logic, refactor regularly, follow consistent patterns."
        },
        {
            "question": "What are security best practices for Python applications?",
            "answer": "Validate input, use environment variables for secrets, sanitize database queries, keep dependencies updated, follow principle of least privilege."
        },
        {
            "question": "How should you optimize Python code performance?",
            "answer": "Profile first, use appropriate data structures, list comprehensions, avoid premature optimization, consider NumPy for numerical work, cache expensive operations."
        },
        {
            "question": "What are the best practices for code documentation?",
            "answer": "Write docstrings for all public functions/classes, use type hints, document complex algorithms, maintain README files, include examples."
        }
    ],

    "CLI Tools": [
        {
            "question": "How do you create command-line interfaces with argparse?",
            "answer": "parser = argparse.ArgumentParser(), add_argument() for options, parse_args() to process. Support positional, optional, and subcommands."
        },
        {
            "question": "What are the different types of command-line arguments?",
            "answer": "Positional (required), optional (--flag), boolean flags (store_true), choices (limited options), multiple values (nargs)."
        },
        {
            "question": "How do you implement subcommands in CLI applications?",
            "answer": "Use add_subparsers(), create parser for each subcommand, set dest to identify which subcommand was used. Like git add, git commit."
        },
        {
            "question": "How should you handle configuration in CLI tools?",
            "answer": "Support config files, environment variables, command-line overrides. Priority: CLI args > env vars > config file > defaults."
        },
        {
            "question": "What are appropriate exit codes for CLI applications?",
            "answer": "0 for success, 1 for general errors, 2 for misuse, specific codes for different error types. Follow Unix conventions."
        },
        {
            "question": "How do you make CLI tools user-friendly?",
            "answer": "Clear help messages, progress bars for long operations, meaningful error messages, support --verbose/--quiet, follow Unix conventions."
        },
        {
            "question": "How do you distribute CLI tools as packages?",
            "answer": "Use setup.py or pyproject.toml, define console_scripts entry points, include dependencies, test installation, provide documentation."
        }
    ]
}


def print_quiz(topic, with_answers=False):
    """
    Print quiz questions for a given topic.
    
    Args:
        topic (str): The topic name (must match a key in quizzes dictionary)
        with_answers (bool): If True, print answers along with questions
    """
    if topic not in quizzes:
        print(f"Topic '{topic}' not found. Available topics:")
        for available_topic in quizzes.keys():
            print(f"  - {available_topic}")
        return
    
    print(f"\n{'='*60}")
    print(f"QUIZ: {topic}")
    print(f"{'='*60}")
    
    questions = quizzes[topic]
    
    for i, qa in enumerate(questions, 1):
        print(f"\nQuestion {i}:")
        print(f"  {qa['question']}")
        
        if with_answers:
            print(f"\nAnswer {i}:")
            print(f"  {qa['answer']}")
            print("-" * 40)
    
    if not with_answers:
        print(f"\nTotal questions: {len(questions)}")
        print("Use print_quiz(topic, with_answers=True) to see answers.")


def list_all_topics():
    """List all available quiz topics."""
    print("Available Quiz Topics:")
    print("=" * 30)
    for i, topic in enumerate(quizzes.keys(), 1):
        print(f"{i:2d}. {topic}")


def print_all_quizzes(with_answers=False):
    """Print all quizzes for all topics."""
    for topic in quizzes.keys():
        print_quiz(topic, with_answers)
        print("\n" + "="*80 + "\n")


if __name__ == "__main__":
    print(__doc__)
    
    # Interactive menu
    while True:
        print("\nPython Manual Quiz System")
        print("1. List all topics")
        print("2. Take a quiz (questions only)")
        print("3. Review a quiz (with answers)")
        print("4. Print all quizzes")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            list_all_topics()
        
        elif choice == "2":
            list_all_topics()
            topic_num = input("\nEnter topic number: ").strip()
            try:
                topic_list = list(quizzes.keys())
                topic = topic_list[int(topic_num) - 1]
                print_quiz(topic, with_answers=False)
            except (ValueError, IndexError):
                print("Invalid topic number.")
        
        elif choice == "3":
            list_all_topics()
            topic_num = input("\nEnter topic number: ").strip()
            try:
                topic_list = list(quizzes.keys())
                topic = topic_list[int(topic_num) - 1]
                print_quiz(topic, with_answers=True)
            except (ValueError, IndexError):
                print("Invalid topic number.")
        
        elif choice == "4":
            answer_choice = input("Include answers? (y/n): ").strip().lower()
            print_all_quizzes(with_answers=(answer_choice == 'y'))
        
        elif choice == "5":
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice. Please enter 1-5.")
