{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive Front-End Essentials Manual\n", "\n", "This manual covers the fundamental technologies for front-end web development: HTML5, CSS3, responsive design, and modern web standards.\n", "\n", "## Table of Contents\n", "1. [HTML5 Fundamentals](#html5)\n", "2. [Semantic HTML and Accessibility](#semantic)\n", "3. [CSS3 Fundamentals](#css3)\n", "4. [CSS Layout Systems](#layout)\n", "5. [Responsive Design](#responsive)\n", "6. [CSS Animations and Transitions](#animations)\n", "7. [CSS Preprocessors](#preprocessors)\n", "8. [Modern CSS Features](#modern-css)\n", "9. [Web Performance](#performance)\n", "10. [Browser Compatibility](#compatibility)\n", "11. [Development Tools](#tools)\n", "12. [Best Practices](#best-practices)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# HTML5 Fundamentals {#html5}\n", "\n", "HTML5 is the latest version of HTML, providing new semantic elements, APIs, and improved functionality for modern web applications."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["// HTML5 Fundamentals\n", "// Note: This demonstrates HTML5 concepts through JavaScript\n", "\n", "console.log('=== HTML5 Fundamentals ===');\n", "\n", "// HTML5 Document Structure\n", "console.log('HTML5 Document Structure:');\n", "const html5Structure = `\n", "<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>HTML5 Document</title>\n", "</head>\n", "<body>\n", "    <header>\n", "        <nav>Navigation</nav>\n", "    </header>\n", "    <main>\n", "        <article>\n", "            <section>Content</section>\n", "        </article>\n", "        <aside>Sidebar</aside>\n", "    </main>\n", "    <footer>Footer</footer>\n", "</body>\n", "</html>\n", "`;\n", "\n", "console.log(html5Structure);\n", "\n", "// HTML5 Semantic Elements\n", "console.log('\\n=== HTML5 Semantic Elements ===');\n", "const semanticElements = {\n", "    'header': 'Introductory content or navigation',\n", "    'nav': 'Navigation links',\n", "    'main': 'Main content of document',\n", "    'article': 'Independent, self-contained content',\n", "    'section': 'Thematic grouping of content',\n", "    'aside': 'Content aside from main content',\n", "    'footer': 'Footer information',\n", "    'figure': 'Self-contained content with caption',\n", "    'figcaption': 'Caption for figure element',\n", "    'time': 'Date/time information',\n", "    'mark': 'Highlighted text',\n", "    'details': 'Disclosure widget',\n", "    'summary': 'Summary for details element'\n", "};\n", "\n", "Object.entries(semanticElements).forEach(([element, description]) => {\n", "    console.log(`<${element}>: ${description}`);\n", "});\n", "\n", "// HTML5 Form Elements\n", "console.log('\\n=== HTML5 Form Elements ===');\n", "const formElements = {\n", "    'email': 'Email input with validation',\n", "    'url': 'URL input with validation',\n", "    'tel': 'Telephone number input',\n", "    'number': 'Numeric input with min/max',\n", "    'range': 'Slider input',\n", "    'date': 'Date picker',\n", "    'time': 'Time picker',\n", "    'color': 'Color picker',\n", "    'search': 'Search input',\n", "    'datalist': 'Predefined options for input'\n", "};\n", "\n", "Object.entries(formElements).forEach(([type, description]) => {\n", "    console.log(`input[type=\"${type}\"]: ${description}`);\n", "});\n", "\n", "// HTML5 APIs simulation\n", "console.log('\\n=== HTML5 APIs ===');\n", "\n", "// Local Storage API\n", "function demonstrateLocalStorage() {\n", "    console.log('\\nLocal Storage API:');\n", "    \n", "    // Simulate localStorage (in browser environment)\n", "    const mockStorage = {\n", "        data: {},\n", "        setItem(key, value) {\n", "            this.data[key] = String(value);\n", "            console.log(`Stored: ${key} = ${value}`);\n", "        },\n", "        getItem(key) {\n", "            const value = this.data[key] || null;\n", "            console.log(`Retrieved: ${key} = ${value}`);\n", "            return value;\n", "        },\n", "        removeItem(key) {\n", "            delete this.data[key];\n", "            console.log(`Removed: ${key}`);\n", "        },\n", "        clear() {\n", "            this.data = {};\n", "            console.log('Storage cleared');\n", "        }\n", "    };\n", "    \n", "    // Test localStorage\n", "    mockStorage.setItem('username', 'alice');\n", "    mockStorage.setItem('preferences', JSON.stringify({ theme: 'dark' }));\n", "    mockStorage.getItem('username');\n", "    mockStorage.removeItem('username');\n", "}\n", "\n", "demonstrateLocalStorage();\n", "\n", "// Geolocation API simulation\n", "function demonstrateGeolocation() {\n", "    console.log('\\nGeolocation API:');\n", "    \n", "    const mockGeolocation = {\n", "        getCurrentPosition(success, error, options) {\n", "            // Simulate getting position\n", "            setTimeout(() => {\n", "                const position = {\n", "                    coords: {\n", "                        latitude: 37.7749,\n", "                        longitude: -122.4194,\n", "                        accuracy: 10\n", "                    },\n", "                    timestamp: Date.now()\n", "                };\n", "                success(position);\n", "            }, 100);\n", "        }\n", "    };\n", "    \n", "    mockGeolocation.getCurrentPosition(\n", "        (position) => {\n", "            console.log('Position:', position.coords.latitude, position.coords.longitude);\n", "        },\n", "        (error) => {\n", "            console.error('Geolocation error:', error);\n", "        }\n", "    );\n", "}\n", "\n", "demonstrateGeolocation();\n", "\n", "// Canvas API simulation\n", "function demonstrateCanvas() {\n", "    console.log('\\nCanvas API:');\n", "    \n", "    // Simulate canvas operations\n", "    const mockCanvas = {\n", "        width: 800,\n", "        height: 600,\n", "        getContext(type) {\n", "            if (type === '2d') {\n", "                return {\n", "                    fillStyle: '#000000',\n", "                    strokeStyle: '#000000',\n", "                    lineWidth: 1,\n", "                    \n", "                    fillRect(x, y, width, height) {\n", "                        console.log(`Fill rectangle: (${x}, ${y}) ${width}x${height}`);\n", "                    },\n", "                    strokeRect(x, y, width, height) {\n", "                        console.log(`Stroke rectangle: (${x}, ${y}) ${width}x${height}`);\n", "                    },\n", "                    beginPath() {\n", "                        console.log('Begin path');\n", "                    },\n", "                    moveTo(x, y) {\n", "                        console.log(`Move to: (${x}, ${y})`);\n", "                    },\n", "                    lineTo(x, y) {\n", "                        console.log(`Line to: (${x}, ${y})`);\n", "                    },\n", "                    stroke() {\n", "                        console.log('Stroke path');\n", "                    }\n", "                };\n", "            }\n", "        }\n", "    };\n", "    \n", "    const ctx = mockCanvas.getContext('2d');\n", "    ctx.fillStyle = '#ff0000';\n", "    ctx.fillRect(10, 10, 100, 50);\n", "    \n", "    ctx.beginPath();\n", "    ctx.moveTo(0, 0);\n", "    ctx.lineTo(100, 100);\n", "    ctx.stroke();\n", "}\n", "\n", "demonstrateCanvas();\n", "\n", "// HTML5 Media Elements\n", "console.log('\\n=== HTML5 Media Elements ===');\n", "\n", "const mediaElements = {\n", "    audio: {\n", "        attributes: ['controls', 'autoplay', 'loop', 'muted', 'preload'],\n", "        formats: ['MP3', 'WAV', 'OGG'],\n", "        example: '<audio controls><source src=\"audio.mp3\" type=\"audio/mpeg\"></audio>'\n", "    },\n", "    video: {\n", "        attributes: ['controls', 'autoplay', 'loop', 'muted', 'poster', 'width', 'height'],\n", "        formats: ['MP4', 'WebM', 'OGG'],\n", "        example: '<video controls width=\"640\" height=\"480\"><source src=\"video.mp4\" type=\"video/mp4\"></video>'\n", "    }\n", "};\n", "\n", "Object.entries(mediaElements).forEach(([element, info]) => {\n", "    console.log(`\\n${element.toUpperCase()} Element:`);\n", "    console.log('Attributes:', info.attributes.join(', '));\n", "    console.log('Formats:', info.formats.join(', '));\n", "    console.log('Example:', info.example);\n", "});\n", "\n", "// HTML5 Validation\n", "console.log('\\n=== HTML5 Form Validation ===');\n", "\n", "function simulateFormValidation() {\n", "    const validationRules = {\n", "        required: (value) => value.trim() !== '',\n", "        email: (value) => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value),\n", "        url: (value) => /^https?:\\/\\/.+/.test(value),\n", "        pattern: (value, pattern) => new RegExp(pattern).test(value),\n", "        min: (value, min) => Number(value) >= min,\n", "        max: (value, max) => Number(value) <= max,\n", "        minlength: (value, length) => value.length >= length,\n", "        maxlength: (value, length) => value.length <= length\n", "    };\n", "    \n", "    function validateField(value, rules) {\n", "        const errors = [];\n", "        \n", "        for (const [rule, ruleValue] of Object.entries(rules)) {\n", "            if (validationRules[rule]) {\n", "                const isValid = typeof ruleValue === 'boolean' ? \n", "                    validationRules[rule](value) : \n", "                    validationRules[rule](value, ruleValue);\n", "                \n", "                if (!isValid) {\n", "                    errors.push(`${rule} validation failed`);\n", "                }\n", "            }\n", "        }\n", "        \n", "        return errors;\n", "    }\n", "    \n", "    // Test validation\n", "    const testCases = [\n", "        { value: '', rules: { required: true }, field: 'name' },\n", "        { value: 'invalid-email', rules: { email: true }, field: 'email' },\n", "        { value: '<EMAIL>', rules: { email: true }, field: 'email' },\n", "        { value: '5', rules: { min: 10, max: 100 }, field: 'age' },\n", "        { value: '25', rules: { min: 10, max: 100 }, field: 'age' }\n", "    ];\n", "    \n", "    testCases.forEach(({ value, rules, field }) => {\n", "        const errors = validateField(value, rules);\n", "        console.log(`${field}: \"${value}\" - ${errors.length ? errors.join(', ') : 'Valid'}`);\n", "    });\n", "}\n", "\n", "simulateFormValidation();\n", "\n", "console.log('\\nHTML5 provides powerful semantic elements and APIs for modern web development!');"]}], "metadata": {"kernelspec": {"display_name": "JavaScript (Node.js)", "language": "javascript", "name": "javascript"}, "language_info": {"file_extension": ".js", "mimetype": "application/javascript", "name": "javascript", "version": "18.0.0"}}, "nbformat": 4, "nbformat_minor": 5}