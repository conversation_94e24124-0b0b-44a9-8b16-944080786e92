{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive Python Data Science Libraries Manual\n", "\n", "This manual covers essential Python libraries for data science, from data manipulation and analysis to machine learning and visualization. Master the core tools that power modern data science workflows.\n", "\n", "## Table of Contents\n", "1. [Data Science Ecosystem Overview](#overview)\n", "2. [NumPy - Numerical Computing](#numpy)\n", "3. [Pandas - Data Manipulation](#pandas)\n", "4. [Matplotlib - Data Visualization](#matplotlib)\n", "5. [Seaborn - Statistical Visualization](#seaborn)\n", "6. [Scikit-learn - Machine Learning](#sklearn)\n", "7. [Jupyter Notebooks](#jupyter)\n", "8. [Data Cleaning and Preprocessing](#preprocessing)\n", "9. [Exploratory Data Analysis](#eda)\n", "10. [Model Training and Evaluation](#modeling)\n", "11. [Performance and Optimization](#performance)\n", "12. [Best Practices](#best-practices)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# Data Science Ecosystem Overview {#overview}\n", "\n", "Understanding the Python data science ecosystem and how different libraries work together to create powerful data analysis workflows."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["# Data Science Ecosystem Overview\n", "print('=== Python Data Science Ecosystem ===')\n", "\n", "# Core libraries and their purposes\n", "libraries = {\n", "    'NumPy': {\n", "        'purpose': 'Numerical computing with arrays',\n", "        'key_features': ['N-dimensional arrays', 'Mathematical functions', 'Broadcasting', 'Linear algebra'],\n", "        'install': 'pip install numpy',\n", "        'import': 'import numpy as np'\n", "    },\n", "    'Pandas': {\n", "        'purpose': 'Data manipulation and analysis',\n", "        'key_features': ['DataFrames', 'Data cleaning', 'File I/O', 'Time series'],\n", "        'install': 'pip install pandas',\n", "        'import': 'import pandas as pd'\n", "    },\n", "    'Mat<PERSON>lotlib': {\n", "        'purpose': 'Data visualization and plotting',\n", "        'key_features': ['2D plotting', 'Customizable charts', 'Publication quality', 'Multiple backends'],\n", "        'install': 'pip install matplotlib',\n", "        'import': 'import matplotlib.pyplot as plt'\n", "    },\n", "    'Seaborn': {\n", "        'purpose': 'Statistical data visualization',\n", "        'key_features': ['Statistical plots', 'Beautiful defaults', 'Pandas integration', 'Complex visualizations'],\n", "        'install': 'pip install seaborn',\n", "        'import': 'import seaborn as sns'\n", "    },\n", "    'Scikit-learn': {\n", "        'purpose': 'Machine learning algorithms',\n", "        'key_features': ['Classification', 'Regression', 'Clustering', 'Model evaluation'],\n", "        'install': 'pip install scikit-learn',\n", "        'import': 'from sklearn import ...'\n", "    }\n", "}\n", "\n", "for lib_name, details in libraries.items():\n", "    print(f'\\n{lib_name}:')\n", "    print(f'  Purpose: {details[\"purpose\"]}')\n", "    print(f'  Install: {details[\"install\"]}')\n", "    print(f'  Import: {details[\"import\"]}')\n", "    print(f'  Key Features: {\", \".join(details[\"key_features\"])}')\n", "\n", "# Data science workflow\n", "print('\\n=== Typical Data Science Workflow ===')\n", "workflow_steps = [\n", "    '1. Data Collection (APIs, databases, files)',\n", "    '2. Data Loading (Pandas)',\n", "    '3. Data Exploration (<PERSON><PERSON>, <PERSON>, Seaborn)',\n", "    '4. Data Cleaning (Pandas, NumPy)',\n", "    '5. Feature Engineering (Pandas, NumPy)',\n", "    '6. Model Training (Scikit-learn)',\n", "    '7. Model Evaluation (<PERSON><PERSON>t-learn, Mat<PERSON>lotlib)',\n", "    '8. Model Deployment (Various tools)',\n", "    '9. Monitoring and Iteration'\n", "]\n", "\n", "for step in workflow_steps:\n", "    print(step)\n", "\n", "# Common data formats\n", "print('\\n=== Common Data Formats ===')\n", "data_formats = {\n", "    'CSV': 'Comma-separated values - most common',\n", "    'JSON': 'JavaScript Object Notation - web APIs',\n", "    'Excel': 'Microsoft Excel files - business data',\n", "    'Parquet': 'Columnar storage - big data',\n", "    'HDF5': 'Hierarchical data format - scientific data',\n", "    'SQL': 'Database tables - structured data',\n", "    'XML': 'Extensible markup language - structured documents'\n", "}\n", "\n", "for format_name, description in data_formats.items():\n", "    print(f'{format_name}: {description}')\n", "\n", "# Environment setup\n", "print('\\n=== Environment Setup ===')\n", "setup_commands = [\n", "    '# Create virtual environment',\n", "    'python -m venv data_science_env',\n", "    '',\n", "    '# Activate environment (Windows)',\n", "    'data_science_env\\\\Scripts\\\\activate',\n", "    '',\n", "    '# Activate environment (macOS/Linux)',\n", "    'source data_science_env/bin/activate',\n", "    '',\n", "    '# Install core packages',\n", "    'pip install numpy pandas mat<PERSON><PERSON><PERSON>b seaborn scikit-learn jupyter',\n", "    '',\n", "    '# Or install with conda',\n", "    'conda install numpy pandas mat<PERSON><PERSON><PERSON>b seaborn scikit-learn jupyter'\n", "]\n", "\n", "for command in setup_commands:\n", "    print(command)\n", "\n", "print('\\nThe Python data science ecosystem provides powerful tools for every step of analysis!')"]}, {"cell_type": "markdown", "id": "71c71fe2", "metadata": {}, "source": ["# NumPy - Numerical Computing {#numpy}\n", "\n", "NumPy is the foundation of the Python data science stack, providing efficient operations on large, multi-dimensional arrays and matrices."]}, {"cell_type": "code", "execution_count": null, "id": "ff42471f", "metadata": {}, "outputs": [], "source": ["# NumPy Fundamentals\n", "print('=== NumPy Fundamentals ===')\n", "\n", "# Note: This demonstrates NumPy concepts without importing (for compatibility)\n", "# In practice: import numpy as np\n", "\n", "print('<PERSON>umPy Array Creation:')\n", "array_creation = '''\n", "import numpy as np\n", "\n", "# Create arrays from lists\n", "arr1d = np.array([1, 2, 3, 4, 5])\n", "arr2d = np.array([[1, 2, 3], [4, 5, 6]])\n", "arr3d = np.array([[[1, 2], [3, 4]], [[5, 6], [7, 8]]])\n", "\n", "# Create arrays with specific values\n", "zeros = np.zeros((3, 4))          # 3x4 array of zeros\n", "ones = np.ones((2, 3, 4))         # 2x3x4 array of ones\n", "full = np.full((3, 3), 7)         # 3x3 array filled with 7\n", "identity = np.eye(4)              # 4x4 identity matrix\n", "\n", "# Create arrays with ranges\n", "range_arr = np.arange(0, 10, 2)   # [0, 2, 4, 6, 8]\n", "linspace = np.linspace(0, 1, 5)   # 5 evenly spaced values from 0 to 1\n", "\n", "# Random arrays\n", "random_uniform = np.random.random((3, 3))     # Uniform [0, 1)\n", "random_normal = np.random.normal(0, 1, (3, 3)) # Normal distribution\n", "random_int = np.random.randint(0, 10, (3, 3))  # Random integers\n", "'''\n", "print(array_creation)\n", "\n", "print('\\n=== Array Properties and Operations ===')\n", "array_operations = '''\n", "# Array properties\n", "arr = np.array([[1, 2, 3], [4, 5, 6]])\n", "print(f\"Shape: {arr.shape}\")        # (2, 3)\n", "print(f\"Size: {arr.size}\")          # 6\n", "print(f\"Dimensions: {arr.ndim}\")    # 2\n", "print(f\"Data type: {arr.dtype}\")    # int64 (or similar)\n", "\n", "# Reshaping arrays\n", "reshaped = arr.reshape(3, 2)        # Change shape to 3x2\n", "flattened = arr.flatten()           # Convert to 1D array\n", "transposed = arr.T                  # Transpose\n", "\n", "# Array indexing and slicing\n", "element = arr[0, 1]                 # Element at row 0, column 1\n", "row = arr[0, :]                     # First row\n", "column = arr[:, 1]                  # Second column\n", "subarray = arr[0:2, 1:3]           # Subarray\n", "\n", "# Boolean indexing\n", "mask = arr > 3                      # Boolean array\n", "filtered = arr[mask]                # Elements greater than 3\n", "arr[arr > 3] = 0                   # Set elements > 3 to 0\n", "'''\n", "print(array_operations)\n", "\n", "print('\\n=== Mathematical Operations ===')\n", "math_operations = '''\n", "# Element-wise operations\n", "a = np.array([1, 2, 3, 4])\n", "b = np.array([5, 6, 7, 8])\n", "\n", "# Arithmetic operations\n", "addition = a + b                    # [6, 8, 10, 12]\n", "subtraction = a - b                 # [-4, -4, -4, -4]\n", "multiplication = a * b              # [5, 12, 21, 32]\n", "division = a / b                    # [0.2, 0.33, 0.43, 0.5]\n", "power = a ** 2                      # [1, 4, 9, 16]\n", "\n", "# Mathematical functions\n", "sqrt_vals = np.sqrt(a)              # Square root\n", "exp_vals = np.exp(a)                # Exponential\n", "log_vals = np.log(a)                # Natural logarithm\n", "sin_vals = np.sin(a)                # Sine\n", "\n", "# Aggregation functions\n", "total = np.sum(a)                   # Sum of all elements\n", "mean_val = np.mean(a)               # Mean\n", "std_val = np.std(a)                 # Standard deviation\n", "min_val = np.min(a)                 # Minimum\n", "max_val = np.max(a)                 # Maximum\n", "\n", "# Axis-specific operations\n", "matrix = np.array([[1, 2, 3], [4, 5, 6]])\n", "row_sums = np.sum(matrix, axis=1)   # Sum along rows: [6, 15]\n", "col_sums = np.sum(matrix, axis=0)   # Sum along columns: [5, 7, 9]\n", "'''\n", "print(math_operations)\n", "\n", "print('\\n=== Broadcasting ===')\n", "broadcasting_example = '''\n", "# Broadcasting allows operations between arrays of different shapes\n", "a = np.array([[1, 2, 3],\n", "              [4, 5, 6]])           # Shape: (2, 3)\n", "b = np.array([10, 20, 30])          # Shape: (3,)\n", "\n", "# Broadcasting automatically expands b to match a's shape\n", "result = a + b                      # [[11, 22, 33],\n", "                                    #  [14, 25, 36]]\n", "\n", "# Scalar broadcasting\n", "scaled = a * 2                      # Multiply all elements by 2\n", "\n", "# Broadcasting rules:\n", "# 1. Arrays are aligned from the rightmost dimension\n", "# 2. Dimensions of size 1 can be stretched\n", "# 3. Missing dimensions are assumed to be size 1\n", "'''\n", "print(broadcasting_example)\n", "\n", "print('\\n=== Linear Algebra ===')\n", "linear_algebra = '''\n", "# Matrix operations\n", "A = np.array([[1, 2], [3, 4]])\n", "B = np.array([[5, 6], [7, 8]])\n", "\n", "# Matrix multiplication\n", "matrix_mult = np.dot(A, B)          # or A @ B\n", "element_mult = A * B                # Element-wise multiplication\n", "\n", "# Linear algebra functions\n", "determinant = np.linalg.det(A)      # Determinant\n", "inverse = np.linalg.inv(A)          # Matrix inverse\n", "eigenvals, eigenvecs = np.linalg.eig(A)  # Eigenvalues and eigenvectors\n", "\n", "# Solving linear systems (Ax = b)\n", "b = np.array([1, 2])\n", "x = np.linalg.solve(A, b)           # Solve for x\n", "'''\n", "print(linear_algebra)\n", "\n", "print('\\nNumPy provides the foundation for efficient numerical computing in Python!')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"file_extension": ".py", "mimetype": "text/x-python", "name": "python", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 5}