{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive Python Programming Manual\n", "\n", "This manual serves as a complete reference for Python programming, from basics to advanced concepts. Each section includes concise explanations and practical code examples that you can run directly in this notebook.\n", "\n", "## Table of Contents\n", "1. [Basic Syntax and Data Types](#basic-syntax)\n", "2. [Control Flow](#control-flow)\n", "3. [Functions and Lambda Expressions](#functions)\n", "4. [Data Structures](#data-structures)\n", "5. [File Operations](#file-ops)\n", "6. [Exception Handling](#exceptions)\n", "7. [Object-Oriented Programming](#oop)\n", "8. [Modules and Packages](#modules)\n", "9. [Standard Library Features](#stdlib)\n", "10. [Advanced Features](#advanced)\n", "11. [<PERSON><PERSON>](#typing)\n", "12. [Regular Expressions](#regex)\n", "13. [DateTime Operations](#datetime)\n", "14. [Context Managers](#context)\n", "15. [Virtual Environments](#venv)\n", "16. [Testing](#testing)\n", "17. [Logging](#logging)\n", "18. [External Libraries](#external)\n", "19. [Data Science Basics](#data-science)\n", "20. [Best Practices](#best-practices)\n", "21. [CL<PERSON> Tools](#cli)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# Basic Syntax and Data Types {#basic-syntax}\n", "\n", "Python's syntax is clean and readable. Let's explore the fundamental building blocks: variables, operators, and basic data types."]}, {"cell_type": "code", "execution_count": 1, "id": "e84d17fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON>!\n", "Type of x: <class 'int'>, Value: 42\n", "Type of pi: <class 'float'>, Value: 3.14159\n", "Type of name: <class 'str'>, Value: Python\n", "Type of is_awesome: <class 'bool'>, Value: True\n"]}], "source": ["# Variables and basic data types\n", "x = 42                  # Integer\n", "pi = 3.14159           # Float\n", "name = \"Python\"        # String\n", "is_awesome = True      # <PERSON><PERSON>an\n", "\n", "# Basic operators\n", "sum_result = 10 + 5    # Addition\n", "diff = 10 - 5          # Subtraction\n", "product = 10 * 5       # Multiplication\n", "quotient = 10 / 5      # Division (returns float)\n", "floor_div = 10 // 3    # Floor division\n", "remainder = 10 % 3     # <PERSON><PERSON>lo\n", "power = 2 ** 3         # Exponentiation\n", "\n", "# String operations\n", "greeting = \"Hello\"\n", "target = \"World\"\n", "message = f\"{greeting}, {target}!\"  # f-strings\n", "print(message)\n", "\n", "# Type conversion\n", "str_num = \"123\"\n", "num = int(str_num)     # String to integer\n", "float_num = float(num) # Integer to float\n", "str_back = str(num)    # Number to string\n", "\n", "# Display types and values\n", "print(f\"Type of x: {type(x)}, Value: {x}\")\n", "print(f\"Type of pi: {type(pi)}, Value: {pi}\")\n", "print(f\"Type of name: {type(name)}, Value: {name}\")\n", "print(f\"Type of is_awesome: {type(is_awesome)}, Value: {is_awesome}\")"]}, {"cell_type": "markdown", "id": "71c71fe2", "metadata": {}, "source": ["# Control Flow {#control-flow}\n", "\n", "Python provides several structures for controlling program flow: conditional statements (if/elif/else), loops (for/while), and flow control statements (break/continue/pass)."]}, {"cell_type": "code", "execution_count": null, "id": "ff42471f", "metadata": {}, "outputs": [], "source": ["# Conditional statements\n", "score = 85\n", "\n", "if score >= 90:\n", "    grade = 'A'\n", "elif score >= 80:\n", "    grade = 'B'\n", "elif score >= 70:\n", "    grade = 'C'\n", "else:\n", "    grade = 'F'\n", "\n", "print(f\"Score: {score}, Grade: {grade}\")\n", "\n", "# For loop examples\n", "print(\"\\nFor loop with range:\")\n", "for i in range(5):\n", "    print(f\"Iteration {i}\")\n", "\n", "print(\"\\nFor loop with list:\")\n", "fruits = ['apple', 'banana', 'cherry']\n", "for fruit in fruits:\n", "    print(fruit)\n", "\n", "# While loop with break and continue\n", "print(\"\\nWhile loop with break and continue:\")\n", "counter = 0\n", "while True:\n", "    counter += 1\n", "    if counter == 3:\n", "        continue  # Skip iteration when counter is 3\n", "    if counter > 5:\n", "        break    # Exit loop when counter exceeds 5\n", "    print(f\"Counter: {counter}\")\n", "\n", "# Loop with enumerate\n", "print(\"\\nEnumerate example:\")\n", "for index, fruit in enumerate(fruits):\n", "    print(f\"Index {index}: {fruit}\")\n", "\n", "# List comprehension (combining loops and conditionals)\n", "numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\n", "even_numbers = [x for x in numbers if x % 2 == 0]\n", "print(f\"\\nEven numbers using list comprehension: {even_numbers}\")"]}, {"cell_type": "markdown", "id": "b3ad4049", "metadata": {}, "source": ["# Functions and Lambda Expressions {#functions}\n", "\n", "Functions are reusable blocks of code. Python supports both regular functions and lambda expressions (anonymous functions)."]}, {"cell_type": "code", "execution_count": null, "id": "4adc818d", "metadata": {}, "outputs": [], "source": ["# Basic function definition\n", "def greet(name, greeting=\"Hello\"):\n", "    \"\"\"\n", "    A simple greeting function with a default parameter.\n", "    \n", "    Args:\n", "        name (str): The name to greet\n", "        greeting (str, optional): The greeting to use. Defaults to \"Hello\"\n", "    \n", "    Returns:\n", "        str: The complete greeting message\n", "    \"\"\"\n", "    return f\"{greeting}, {name}!\"\n", "\n", "# Function calls\n", "print(greet(\"<PERSON>\"))\n", "print(greet(\"<PERSON>\", greeting=\"Hi\"))\n", "\n", "# Function with multiple return values\n", "def divide_and_remainder(a, b):\n", "    return a // b, a % b\n", "\n", "quotient, remainder = divide_and_remainder(10, 3)\n", "print(f\"10 divided by 3: quotient = {quotient}, remainder = {remainder}\")\n", "\n", "# Args and kwargs\n", "def example_function(*args, **kwargs):\n", "    print(\"Positional arguments:\", args)\n", "    print(\"Keyword arguments:\", kwargs)\n", "\n", "example_function(1, 2, 3, name=\"<PERSON>\", age=30)\n", "\n", "# Lambda functions\n", "square = lambda x: x**2\n", "cube = lambda x: x**3\n", "\n", "numbers = [1, 2, 3, 4, 5]\n", "squares = list(map(square, numbers))\n", "cubes = list(map(cube, numbers))\n", "\n", "print(f\"Numbers: {numbers}\")\n", "print(f\"Squares: {squares}\")\n", "print(f\"Cubes: {cubes}\")\n", "\n", "# Sorting with lambda\n", "pairs = [(1, 'one'), (2, 'two'), (3, 'three')]\n", "# Sort by the second element (string)\n", "sorted_by_string = sorted(pairs, key=lambda pair: pair[1])\n", "print(f\"\\nSorted by string: {sorted_by_string}\")\n", "\n", "# Higher-order function example\n", "def create_multiplier(factor):\n", "    return lambda x: x * factor\n", "\n", "double = create_multiplier(2)\n", "triple = create_multiplier(3)\n", "\n", "print(f\"\\nDouble 5: {double(5)}\")\n", "print(f\"Triple 5: {triple(5)}\")"]}, {"cell_type": "markdown", "id": "fa36a827", "metadata": {}, "source": ["# Data Structures {#data-structures}\n", "\n", "Python provides several built-in data structures: lists, tuples, sets, and dictionaries. Each has its own characteristics and use cases."]}, {"cell_type": "code", "execution_count": null, "id": "56c306d3", "metadata": {}, "outputs": [], "source": ["# Lists\n", "print(\"=== Lists ===\")\n", "fruits = ['apple', 'banana', 'cherry']\n", "print(f\"Original list: {fruits}\")\n", "\n", "# List operations\n", "fruits.append('date')\n", "fruits.insert(1, 'blueberry')\n", "print(f\"After append and insert: {fruits}\")\n", "\n", "fruits.remove('banana')\n", "popped_fruit = fruits.pop()\n", "print(f\"After remove and pop: {fruits}\")\n", "\n", "# List slicing\n", "numbers = [0, 1, 2, 3, 4, 5]\n", "print(f\"\\nOriginal numbers: {numbers}\")\n", "print(f\"First three: {numbers[:3]}\")\n", "print(f\"Last three: {numbers[-3:]}\")\n", "print(f\"Every second number: {numbers[::2]}\")\n", "\n", "# Tuples\n", "print(\"\\n=== Tuples ===\")\n", "# Tuples are immutable\n", "coordinates = (3, 4)\n", "x, y = coordinates  # Tuple unpacking\n", "print(f\"Coordinates: {coordinates}\")\n", "print(f\"x: {x}, y: {y}\")\n", "\n", "# Named tuples\n", "from collections import namedtuple\n", "Point = namedtuple('Point', ['x', 'y'])\n", "p = Point(5, 10)\n", "print(f\"Named tuple point: {p}\")\n", "print(f\"Access by name: p.x = {p.x}, p.y = {p.y}\")\n", "\n", "# Sets\n", "print(\"\\n=== Sets ===\")\n", "fruits_set = {'apple', 'banana', 'cherry', 'apple'}  # Note: duplicates are removed\n", "print(f\"Fruits set: {fruits_set}\")\n", "\n", "# Set operations\n", "more_fruits = {'cherry', 'date', 'elderberry'}\n", "print(f\"More fruits: {more_fruits}\")\n", "print(f\"Union: {fruits_set | more_fruits}\")\n", "print(f\"Intersection: {fruits_set & more_fruits}\")\n", "print(f\"Difference: {fruits_set - more_fruits}\")\n", "\n", "# Dictionaries\n", "print(\"\\n=== Dictionaries ===\")\n", "person = {\n", "    'name': '<PERSON>',\n", "    'age': 30,\n", "    'city': 'New York'\n", "}\n", "\n", "# Dictionary operations\n", "print(f\"Original dictionary: {person}\")\n", "person['email'] = '<EMAIL>'\n", "print(f\"After adding email: {person}\")\n", "\n", "# Dictionary methods\n", "print(f\"Keys: {list(person.keys())}\")\n", "print(f\"Values: {list(person.values())}\")\n", "print(f\"Items: {list(person.items())}\")\n", "\n", "# Dictionary comprehension\n", "squares_dict = {x: x**2 for x in range(5)}\n", "print(f\"\\nSquares dictionary: {squares_dict}\")\n", "\n", "# Nested data structures\n", "print(\"\\n=== Nested Structures ===\")\n", "nested = {\n", "    'list_example': [1, 2, 3],\n", "    'dict_example': {'a': 1, 'b': 2},\n", "    'tuple_example': (4, 5, 6)\n", "}\n", "print(f\"Nested structure: {nested}\")\n", "\n", "# Using defaultdict\n", "from collections import defaultdict\n", "word_count = defaultdict(int)\n", "sentence = \"the quick brown fox jumps over the lazy dog\"\n", "for word in sentence.split():\n", "    word_count[word] += 1\n", "print(f\"\\nWord count using defaultdict: {dict(word_count)}\")"]}, {"cell_type": "markdown", "id": "dfa02e04", "metadata": {}, "source": ["# File Operations {#file-ops}\n", "\n", "Python provides comprehensive support for file operations, including reading, writing, and managing files and directories."]}, {"cell_type": "code", "execution_count": null, "id": "9440a6fd", "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "from pathlib import Path\n", "\n", "# Writing to a file\n", "with open('example.txt', 'w') as f:\n", "    f.write('Hello, <PERSON>!\\n')\n", "    f.write('This is a test file.\\n')\n", "    f.writelines(['Line 1\\n', 'Line 2\\n', 'Line 3\\n'])\n", "\n", "# Reading from a file\n", "print(\"=== Reading file contents ===\")\n", "with open('example.txt', 'r') as f:\n", "    content = f.read()\n", "    print(\"Complete file contents:\")\n", "    print(content)\n", "\n", "# Reading line by line\n", "print(\"\\nReading line by line:\")\n", "with open('example.txt', 'r') as f:\n", "    for line in f:\n", "        print(f\"Line: {line.strip()}\")\n", "\n", "# Using pathlib\n", "print(\"\\n=== Using pathlib ===\")\n", "file_path = Path('example.txt')\n", "print(f\"File exists: {file_path.exists()}\")\n", "print(f\"File size: {file_path.stat().st_size} bytes\")\n", "print(f\"File name: {file_path.name}\")\n", "print(f\"File suffix: {file_path.suffix}\")\n", "\n", "# Directory operations\n", "print(\"\\n=== Directory operations ===\")\n", "# Create a directory\n", "os.makedirs('test_dir', exist_ok=True)\n", "\n", "# List directory contents\n", "print(f\"Current directory contents: {os.listdir('.')}\")\n", "\n", "# Create some test files\n", "for i in range(3):\n", "    Path(f'test_dir/file{i}.txt').write_text(f'Content of file {i}')\n", "\n", "# Using pathlib to list directory contents\n", "test_dir = Path('test_dir')\n", "print(\"\\nFiles in test_dir:\")\n", "for file in test_dir.glob('*.txt'):\n", "    print(f\"- {file.name}: {file.read_text()}\")\n", "\n", "# File manipulation\n", "source = 'example.txt'\n", "destination = 'test_dir/example_copy.txt'\n", "shutil.copy2(source, destination)\n", "print(f\"\\nCopied {source} to {destination}\")\n", "\n", "# Clean up\n", "print(\"\\n=== Cleaning up ===\")\n", "os.remove('example.txt')\n", "shutil.rmtree('test_dir')\n", "print(\"Cleaned up test files and directories\")"]}, {"cell_type": "markdown", "id": "bc9108b2", "metadata": {}, "source": ["# Exception Handling {#exceptions}\n", "\n", "Exception handling in Python uses try/except blocks to manage errors gracefully. You can also create custom exceptions for specific error cases."]}, {"cell_type": "code", "execution_count": null, "id": "e5eb596c", "metadata": {}, "outputs": [], "source": ["# Basic exception handling\n", "def divide(a, b):\n", "    try:\n", "        result = a / b\n", "    except ZeroDivisionError:\n", "        print(\"Error: Division by zero!\")\n", "        return None\n", "    else:\n", "        print(\"Division successful!\")\n", "        return result\n", "    finally:\n", "        print(\"This code always runs\")\n", "\n", "print(\"Testing division function:\")\n", "print(f\"10 / 2 = {divide(10, 2)}\")\n", "print(f\"10 / 0 = {divide(10, 0)}\")\n", "\n", "# Multiple exception types\n", "def process_data(data):\n", "    try:\n", "        number = int(data)\n", "        result = 100 / number\n", "    except ValueError:\n", "        print(\"Error: Invalid input! Please enter a number.\")\n", "    except ZeroDivisionError:\n", "        print(\"Error: Cannot divide by zero!\")\n", "    except Exception as e:\n", "        print(f\"Unexpected error: {e}\")\n", "    else:\n", "        print(f\"Result: {result}\")\n", "\n", "print(\"\\nTesting process_data function:\")\n", "process_data(\"10\")    # Valid number\n", "process_data(\"zero\")  # Invalid input\n", "process_data(\"0\")     # Zero division\n", "\n", "# Custom exceptions\n", "class AgeError(Exception):\n", "    \"\"\"Exception raised for invalid age values.\"\"\"\n", "    pass\n", "\n", "def validate_age(age):\n", "    try:\n", "        age = int(age)\n", "        if age < 0:\n", "            raise AgeError(\"Age cannot be negative\")\n", "        if age > 150:\n", "            raise AgeError(\"Age is unrealistically high\")\n", "    except ValueError:\n", "        raise AgeError(\"Age must be a number\")\n", "    return age\n", "\n", "print(\"\\nTesting age validation:\")\n", "try:\n", "    print(f\"Age 25: {validate_age(25)}\")\n", "    print(f\"Age -5: {validate_age(-5)}\")\n", "except AgeError as e:\n", "    print(f\"Invalid age: {e}\")\n", "\n", "# Context manager with exception handling\n", "class FileManager:\n", "    def __init__(self, filename):\n", "        self.filename = filename\n", "        self.file = None\n", "        \n", "    def __enter__(self):\n", "        try:\n", "            self.file = open(self.filename, 'w')\n", "            return self.file\n", "        except IOError as e:\n", "            print(f\"Error opening file: {e}\")\n", "            return None\n", "        \n", "    def __exit__(self, exc_type, exc_value, traceback):\n", "        if self.file:\n", "            self.file.close()\n", "            print(\"File closed successfully\")\n", "        return False  # Don't suppress exceptions\n", "\n", "print(\"\\nTesting custom context manager:\")\n", "with FileManager('test.txt') as file:\n", "    if file:\n", "        file.write(\"Test content\")\n", "\n", "# Clean up\n", "import os\n", "if os.path.exists('test.txt'):\n", "    os.remove('test.txt')"]}, {"cell_type": "markdown", "id": "b1d7dad6", "metadata": {}, "source": ["# Object-Oriented Programming {#oop}\n", "\n", "Python is a powerful object-oriented programming language. Here we'll explore classes, inheritance, polymorphism, and special methods."]}, {"cell_type": "code", "execution_count": null, "id": "6dbdc6d7", "metadata": {}, "outputs": [], "source": ["# Basic class definition\n", "class Animal:\n", "    \"\"\"Base class for animals\"\"\"\n", "    \n", "    def __init__(self, name, species):\n", "        self.name = name\n", "        self.species = species\n", "        \n", "    def make_sound(self):\n", "        pass\n", "    \n", "    def __str__(self):\n", "        return f\"{self.name} is a {self.species}\"\n", "\n", "# Inheritance\n", "class Dog(Animal):\n", "    def __init__(self, name, breed):\n", "        super().__init__(name, species=\"Dog\")\n", "        self.breed = breed\n", "        \n", "    def make_sound(self):\n", "        return \"Woof!\"\n", "    \n", "    def fetch(self, item):\n", "        return f\"{self.name} is fetching the {item}\"\n", "\n", "class Cat(Animal):\n", "    def __init__(self, name, color):\n", "        super().__init__(name, species=\"Cat\")\n", "        self.color = color\n", "        \n", "    def make_sound(self):\n", "        return \"Meow!\"\n", "    \n", "    def scratch(self):\n", "        return f\"{self.name} is scratching\"\n", "\n", "# Creating and using objects\n", "dog = Dog(\"<PERSON>\", \"German Shepherd\")\n", "cat = Cat(\"<PERSON>his<PERSON>\", \"<PERSON>\")\n", "\n", "print(dog)  # Using __str__\n", "print(cat)\n", "print(f\"{dog.name} says: {dog.make_sound()}\")\n", "print(f\"{cat.name} says: {cat.make_sound()}\")\n", "print(dog.fetch(\"ball\"))\n", "print(cat.scratch())\n", "\n", "# Property decorators\n", "class Circle:\n", "    def __init__(self, radius):\n", "        self._radius = radius\n", "    \n", "    @property\n", "    def radius(self):\n", "        return self._radius\n", "    \n", "    @radius.setter\n", "    def radius(self, value):\n", "        if value < 0:\n", "            raise ValueError(\"Radius cannot be negative\")\n", "        self._radius = value\n", "    \n", "    @property\n", "    def area(self):\n", "        return 3.14159 * self._radius ** 2\n", "\n", "# Using properties\n", "circle = Circle(5)\n", "print(f\"\\nCircle with radius {circle.radius} has area {circle.area:.2f}\")\n", "try:\n", "    circle.radius = -1\n", "except ValueError as e:\n", "    print(f\"Error: {e}\")\n", "\n", "# Class and static methods\n", "class MathOperations:\n", "    @staticmethod\n", "    def add(x, y):\n", "        return x + y\n", "    \n", "    @classmethod\n", "    def multiply(cls, x, y):\n", "        return x * y\n", "    \n", "    @staticmethod\n", "    def is_even(n):\n", "        return n % 2 == 0\n", "\n", "# Using class and static methods\n", "print(f\"\\nMath Operations:\")\n", "print(f\"Add: {MathOperations.add(5, 3)}\")\n", "print(f\"Multiply: {MathOperations.multiply(4, 2)}\")\n", "print(f\"Is 6 even? {MathOperations.is_even(6)}\")\n", "\n", "# Multiple inheritance and mixins\n", "class Flyable:\n", "    def fly(self):\n", "        return \"Flying high!\"\n", "\n", "class Swimmable:\n", "    def swim(self):\n", "        return \"Swimming deep!\"\n", "\n", "class Duck(Animal, Flyable, Swimmable):\n", "    def __init__(self, name):\n", "        super().__init__(name, species=\"Duck\")\n", "    \n", "    def make_sound(self):\n", "        return \"Quack!\"\n", "\n", "# Using multiple inheritance\n", "duck = Duck(\"<PERSON>\")\n", "print(f\"\\nDuck capabilities:\")\n", "print(f\"{duck.name} says: {duck.make_sound()}\")\n", "print(f\"{duck.name}: {duck.fly()}\")\n", "print(f\"{duck.name}: {duck.swim()}\")\n", "\n", "# Data classes (Python 3.7+)\n", "from dataclasses import dataclass\n", "\n", "@dataclass\n", "class Point:\n", "    x: float\n", "    y: float\n", "    \n", "    def distance_from_origin(self):\n", "        return (self.x ** 2 + self.y ** 2) ** 0.5\n", "\n", "# Using dataclass\n", "point = Point(3, 4)\n", "print(f\"\\nPoint: ({point.x}, {point.y})\")\n", "print(f\"Distance from origin: {point.distance_from_origin()}\")"]}, {"cell_type": "markdown", "id": "09b7097e", "metadata": {}, "source": ["# Modules and Packages {#modules}\n", "\n", "Python's module system allows you to organize code into reusable components. We'll explore creating and using modules, packages, and managing imports."]}, {"cell_type": "code", "execution_count": null, "id": "bdaf0635", "metadata": {}, "outputs": [], "source": ["# Importing standard library modules\n", "import math\n", "from datetime import datetime, timedelta\n", "from collections import defaultdict, Counter\n", "import json\n", "from pprint import pprint\n", "\n", "# Using imported modules\n", "print(\"=== Standard Library Imports ===\")\n", "print(f\"Square root of 16: {math.sqrt(16)}\")\n", "print(f\"Current time: {datetime.now()}\")\n", "\n", "# Creating a Counter\n", "words = ['apple', 'banana', 'apple', 'cherry', 'date', 'apple', 'banana']\n", "word_counts = Counter(words)\n", "print(f\"\\nWord counts: {dict(word_counts)}\")\n", "\n", "# Working with JSON\n", "data = {\n", "    'name': '<PERSON>',\n", "    'age': 30,\n", "    'skills': ['Python', 'JavaScript', 'SQL']\n", "}\n", "\n", "json_str = json.dumps(data, indent=2)\n", "print(\"\\nJSON output:\")\n", "print(json_str)\n", "\n", "# Creating a module\n", "import sys\n", "from pathlib import Path\n", "\n", "# Create a temporary module file\n", "module_content = '''\n", "def greet(name):\n", "    return f\"Hello, {name}!\"\n", "\n", "def factorial(n):\n", "    if n <= 1:\n", "        return 1\n", "    return n * factorial(n - 1)\n", "\n", "PI = 3.14159\n", "'''\n", "\n", "module_path = Path('example_module.py')\n", "module_path.write_text(module_content)\n", "\n", "# Import our custom module\n", "import example_module\n", "\n", "print(\"\\n=== Custom Module Usage ===\")\n", "print(example_module.greet(\"<PERSON>\"))\n", "print(f\"Factorial of 5: {example_module.factorial(5)}\")\n", "print(f\"PI from module: {example_module.PI}\")\n", "\n", "# Creating a package\n", "package_dir = Path('example_package')\n", "package_dir.mkdir(exist_ok=True)\n", "\n", "# Create __init__.py\n", "init_content = '''\n", "from .math_utils import add, subtract\n", "from .string_utils import reverse_string\n", "\n", "__version__ = '1.0.0'\n", "'''\n", "(package_dir / '__init__.py').write_text(init_content)\n", "\n", "# Create math_utils.py\n", "math_utils_content = '''\n", "def add(a, b):\n", "    return a + b\n", "\n", "def subtract(a, b):\n", "    return a - b\n", "'''\n", "(package_dir / 'math_utils.py').write_text(math_utils_content)\n", "\n", "# Create string_utils.py\n", "string_utils_content = '''\n", "def reverse_string(s):\n", "    return s[::-1]\n", "'''\n", "(package_dir / 'string_utils.py').write_text(string_utils_content)\n", "\n", "# Add package directory to Python path\n", "sys.path.append(str(package_dir.parent))\n", "\n", "# Import and use the package\n", "from example_package import add, subtract, reverse_string\n", "\n", "print(\"\\n=== Package Usage ===\")\n", "print(f\"Add: {add(5, 3)}\")\n", "print(f\"Subtract: {subtract(10, 4)}\")\n", "print(f\"Reverse string: {reverse_string('Hello, World!')}\")\n", "\n", "# Clean up\n", "module_path.unlink()\n", "for file in package_dir.glob('*.py'):\n", "    file.unlink()\n", "package_dir.rmdir()\n", "\n", "# Demonstrate relative imports\n", "print(\"\\n=== Import System Info ===\")\n", "print(f\"Python path: {sys.path}\")\n", "print(f\"Module search path: {[p for p in sys.path if Path(p).exists()]}\")\n", "\n", "# Show module attributes\n", "print(\"\\n=== Module Attributes ===\")\n", "print(f\"Module name: {__name__}\")\n", "print(f\"Module file: {__file__}\")\n", "print(f\"Module doc: {__doc__}\")"]}, {"cell_type": "markdown", "id": "ded1ffb4", "metadata": {}, "source": ["# Standard Library Features {#stdlib}\n", "\n", "Python's standard library is extensive and includes many useful modules for common programming tasks. Here are some essential modules and their features."]}, {"cell_type": "code", "execution_count": null, "id": "b35c08d6", "metadata": {}, "outputs": [], "source": ["# collections module\n", "from collections import Counter, defaultdict, deque, namedtuple\n", "\n", "print(\"=== Collections Module ===\")\n", "# Counter for counting occurrences\n", "text = \"mississippi\"\n", "char_count = Counter(text)\n", "print(f\"Character count in 'mississippi': {dict(char_count)}\")\n", "\n", "# defaultdict for automatic default values\n", "dd = defaultdict(list)\n", "for i in range(5):\n", "    dd[i % 2].append(i)\n", "print(f\"\\nGrouped numbers by even/odd: {dict(dd)}\")\n", "\n", "# deque for efficient queue operations\n", "dq = deque([1, 2, 3, 4, 5], maxlen=5)\n", "print(f\"\\nOriginal deque: {dq}\")\n", "dq.append(6)  # Right side\n", "dq.appendleft(0)  # Left side\n", "print(f\"After append operations: {dq}\")\n", "\n", "# itertools module\n", "print(\"\\n=== Itertools Module ===\")\n", "from itertools import combinations, permutations, cycle, count, chain\n", "\n", "# Combinations and permutations\n", "items = ['A', 'B', 'C']\n", "print(f\"Combinations of 2: {list(combinations(items, 2))}\")\n", "print(f\"Permutations of 2: {list(permutations(items, 2))}\")\n", "\n", "# Infinite iterators\n", "counter = count(start=1, step=2)\n", "print(\"\\nFirst 5 odd numbers:\")\n", "for _ in range(5):\n", "    print(next(counter), end=' ')\n", "\n", "# functools module\n", "print(\"\\n\\n=== Functools Module ===\")\n", "from functools import reduce, partial, lru_cache\n", "\n", "# reduce function\n", "numbers = [1, 2, 3, 4, 5]\n", "product = reduce(lambda x, y: x * y, numbers)\n", "print(f\"\\nProduct of numbers {numbers}: {product}\")\n", "\n", "# partial function\n", "def power(base, exponent):\n", "    return base ** exponent\n", "\n", "square = partial(power, exponent=2)\n", "cube = partial(power, exponent=3)\n", "print(f\"Square of 5: {square(5)}\")\n", "print(f\"Cube of 5: {cube(5)}\")\n", "\n", "# lru_cache for memoization\n", "@lru_cache(maxsize=None)\n", "def <PERSON><PERSON><PERSON><PERSON>(n):\n", "    if n < 2:\n", "        return n\n", "    return fibon<PERSON>ci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)\n", "\n", "print(\"\\nFirst 10 Fi<PERSON>acci numbers:\")\n", "for i in range(10):\n", "    print(<PERSON><PERSON><PERSON><PERSON>(i), end=' ')\n", "\n", "# heapq module for priority queues\n", "print(\"\\n\\n=== Heapq Module ===\")\n", "import heapq\n", "\n", "numbers = [10, 5, 8, 1, 9, 3]\n", "heapq.heapify(numbers)  # Convert list to heap\n", "print(f\"\\nHeap: {numbers}\")\n", "print(f\"Pop 3 smallest numbers: {[heapq.heappop(numbers) for _ in range(3)]}\")\n", "\n", "# statistics module\n", "print(\"\\n=== Statistics Module ===\")\n", "from statistics import mean, median, mode, stdev\n", "\n", "data = [1, 2, 2, 3, 4, 4, 4, 5]\n", "print(f\"Data: {data}\")\n", "print(f\"Mean: {mean(data)}\")\n", "print(f\"Median: {median(data)}\")\n", "print(f\"Mode: {mode(data)}\")\n", "print(f\"Standard deviation: {stdev(data)}\")\n", "\n", "# urllib for HTTP requests\n", "print(\"\\n=== URL Operations ===\")\n", "from urllib import request, parse\n", "\n", "# Construct URL with parameters\n", "params = {'q': 'python programming', 'page': 1}\n", "url = 'https://example.com/search?' + parse.urlencode(params)\n", "print(f\"Encoded URL: {url}\")\n", "\n", "# uuid module for unique identifiers\n", "print(\"\\n=== UUID Module ===\")\n", "import uuid\n", "\n", "print(f\"UUID1 (MAC address + timestamp): {uuid.uuid1()}\")\n", "print(f\"UUID4 (random): {uuid.uuid4()}\")\n", "\n", "# pprint for pretty printing\n", "print(\"\\n=== Pretty Printing ===\")\n", "from pprint import pprint\n", "\n", "complex_data = {\n", "    'users': [\n", "        {'name': '<PERSON>', 'age': 30, 'roles': ['admin', 'user']},\n", "        {'name': '<PERSON>', 'age': 25, 'roles': ['user']}\n", "    ],\n", "    'settings': {\n", "        'theme': 'dark',\n", "        'notifications': True,\n", "        'language': 'en'\n", "    }\n", "}\n", "\n", "print(\"Pretty printed data:\")\n", "pprint(complex_data, width=40)"]}, {"cell_type": "markdown", "id": "70e66b75", "metadata": {}, "source": ["# Advanced Features {#advanced}\n", "\n", "Python provides powerful advanced features like iterators, generators, and decorators that enable elegant and efficient programming patterns."]}, {"cell_type": "code", "execution_count": null, "id": "109dc761", "metadata": {}, "outputs": [], "source": ["# Iterators\n", "print(\"=== Iterators ===\")\n", "\n", "class CountDown:\n", "    \"\"\"Custom iterator that counts down from n to 1\"\"\"\n", "    def __init__(self, start):\n", "        self.start = start\n", "        \n", "    def __iter__(self):\n", "        return self\n", "        \n", "    def __next__(self):\n", "        if self.start <= 0:\n", "            raise StopIteration\n", "        self.start -= 1\n", "        return self.start + 1\n", "\n", "# Using the iterator\n", "countdown = CountDown(5)\n", "print(\"Counting down:\")\n", "for num in countdown:\n", "    print(num, end=' ')\n", "\n", "# Generators\n", "print(\"\\n\\n=== Generators ===\")\n", "\n", "def fibonacci_generator(n):\n", "    \"\"\"Generator function for <PERSON><PERSON><PERSON><PERSON> sequence\"\"\"\n", "    a, b = 0, 1\n", "    for _ in range(n):\n", "        yield a\n", "        a, b = b, a + b\n", "\n", "print(\"\\nFib<PERSON>cci sequence using generator:\")\n", "for num in fibonacci_generator(10):\n", "    print(num, end=' ')\n", "\n", "# Generator expressions\n", "numbers = [1, 2, 3, 4, 5]\n", "squares_gen = (x**2 for x in numbers)\n", "print(f\"\\n\\nSquares using generator expression: {list(squares_gen)}\")\n", "\n", "# Infinite generator\n", "def infinite_counter(start=0):\n", "    while True:\n", "        yield start\n", "        start += 1\n", "\n", "print(\"\\nFirst 5 numbers from infinite counter:\")\n", "counter = infinite_counter()\n", "for _ in range(5):\n", "    print(next(counter), end=' ')\n", "\n", "# Decorators\n", "print(\"\\n\\n=== Decorators ===\")\n", "\n", "# Function decorator\n", "def timer(func):\n", "    from time import time\n", "    \n", "    def wrapper(*args, **kwargs):\n", "        start = time()\n", "        result = func(*args, **kwargs)\n", "        end = time()\n", "        print(f\"{func.__name__} took {end - start:.4f} seconds to execute\")\n", "        return result\n", "    \n", "    return wrapper\n", "\n", "@timer\n", "def slow_function():\n", "    \"\"\"Simulate a slow function\"\"\"\n", "    import time\n", "    time.sleep(1)\n", "    return \"Function completed\"\n", "\n", "print(\"\\nTesting timer decorator:\")\n", "slow_function()\n", "\n", "# Decorator with parameters\n", "def repeat(times):\n", "    def decorator(func):\n", "        def wrapper(*args, **kwargs):\n", "            results = []\n", "            for _ in range(times):\n", "                results.append(func(*args, **kwargs))\n", "            return results\n", "        return wrapper\n", "    return decorator\n", "\n", "@repeat(times=3)\n", "def greet(name):\n", "    return f\"Hello, {name}!\"\n", "\n", "print(\"\\nTesting repeat decorator:\")\n", "print(greet(\"<PERSON>\"))\n", "\n", "# Class decorator\n", "def singleton(cls):\n", "    instances = {}\n", "    \n", "    def get_instance(*args, **kwargs):\n", "        if cls not in instances:\n", "            instances[cls] = cls(*args, **kwargs)\n", "        return instances[cls]\n", "    \n", "    return get_instance\n", "\n", "@singleton\n", "class Configuration:\n", "    def __init__(self):\n", "        self.settings = {}\n", "    \n", "    def set_setting(self, key, value):\n", "        self.settings[key] = value\n", "\n", "print(\"\\nTesting singleton decorator:\")\n", "config1 = Configuration()\n", "config2 = Configuration()\n", "print(f\"Same instance? {config1 is config2}\")\n", "\n", "# Context manager using generator\n", "from contextlib import contextmanager\n", "\n", "@contextmanager\n", "def temporary_file(filename):\n", "    try:\n", "        f = open(filename, 'w')\n", "        yield f\n", "    finally:\n", "        f.close()\n", "        import os\n", "        os.remove(filename)\n", "\n", "print(\"\\nTesting context manager decorator:\")\n", "with temporary_file('test.txt') as f:\n", "    f.write('Hello, <PERSON>!')\n", "    print(\"File written and automatically cleaned up\")\n", "\n", "# Property decorator with validation\n", "class Temperature:\n", "    def __init__(self, celsius):\n", "        self._celsius = celsius\n", "    \n", "    @property\n", "    def celsius(self):\n", "        return self._celsius\n", "    \n", "    @celsius.setter\n", "    def celsius(self, value):\n", "        if value < -273.15:\n", "            raise ValueError(\"Temperature below absolute zero!\")\n", "        self._celsius = value\n", "    \n", "    @property\n", "    def fahrenheit(self):\n", "        return (self.celsius * 9/5) + 32\n", "\n", "print(\"\\nTesting property decorator:\")\n", "temp = Temperature(25)\n", "print(f\"Temperature in Celsius: {temp.celsius}°C\")\n", "print(f\"Temperature in Fahrenheit: {temp.fahrenheit}°F\")\n", "\n", "try:\n", "    temp.celsius = -300\n", "except ValueError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "markdown", "id": "3ef56820", "metadata": {}, "source": ["# Static Typing {#typing}\n", "\n", "Python 3.5+ supports type hints that enable static type checking. While Python remains dynamically typed at runtime, type hints help catch potential type-related errors during development."]}, {"cell_type": "code", "execution_count": null, "id": "b8426e5b", "metadata": {}, "outputs": [], "source": ["# Basic type hints\n", "from typing import List, Dict, Tuple, Set, Optional, Union, Any, Callable\n", "from dataclasses import dataclass\n", "\n", "# Function with type hints\n", "def greet(name: str, times: int = 1) -> str:\n", "    \"\"\"Greet someone a specified number of times.\"\"\"\n", "    return (name + \"! \") * times\n", "\n", "print(greet(\"<PERSON>\", 3))\n", "\n", "# Type hints for collections\n", "def process_numbers(numbers: List[int]) -> List[int]:\n", "    \"\"\"Double each number in the list.\"\"\"\n", "    return [n * 2 for n in numbers]\n", "\n", "print(f\"\\nProcessed numbers: {process_numbers([1, 2, 3, 4, 5])}\")\n", "\n", "# Type hints for dictionaries\n", "def count_words(text: str) -> Dict[str, int]:\n", "    \"\"\"Count word occurrences in text.\"\"\"\n", "    return {word: text.split().count(word) for word in set(text.split())}\n", "\n", "text = \"the quick brown fox jumps over the lazy dog\"\n", "print(f\"\\nWord count: {count_words(text)}\")\n", "\n", "# Optional and Union types\n", "def find_index(items: List[str], target: str) -> Optional[int]:\n", "    \"\"\"Find the index of target in items, or None if not found.\"\"\"\n", "    try:\n", "        return items.index(target)\n", "    except ValueError:\n", "        return None\n", "\n", "# Union type (multiple possible types)\n", "def process_input(value: Union[str, int]) -> str:\n", "    if isinstance(value, str):\n", "        return value.upper()\n", "    return str(value * 2)\n", "\n", "print(f\"\\nProcessing string: {process_input('hello')}\")\n", "print(f\"Processing number: {process_input(5)}\")\n", "\n", "# Type aliases\n", "from typing import TypeVar, Generic\n", "\n", "T = TypeVar('T')  # Generic type variable\n", "\n", "class Stack(Generic[T]):\n", "    def __init__(self) -> None:\n", "        self.items: List[T] = []\n", "    \n", "    def push(self, item: T) -> None:\n", "        self.items.append(item)\n", "    \n", "    def pop(self) -> Optional[T]:\n", "        return self.items.pop() if self.items else None\n", "\n", "# Using the generic stack\n", "number_stack: Stack[int] = Stack()\n", "number_stack.push(1)\n", "number_stack.push(2)\n", "print(f\"\\nPopped from stack: {number_stack.pop()}\")\n", "\n", "# Type hints with dataclasses\n", "@dataclass\n", "class Point:\n", "    x: float\n", "    y: float\n", "    label: Optional[str] = None\n", "\n", "    def distance_from_origin(self) -> float:\n", "        return (self.x ** 2 + self.y ** 2) ** 0.5\n", "\n", "point = Point(3.0, 4.0, \"A\")\n", "print(f\"\\nPoint {point.label}: ({point.x}, {point.y})\")\n", "print(f\"Distance from origin: {point.distance_from_origin()}\")\n", "\n", "# Callable type hints\n", "def apply_operation(x: int, operation: Callable[[int], int]) -> int:\n", "    return operation(x)\n", "\n", "def square(x: int) -> int:\n", "    return x ** 2\n", "\n", "print(f\"\\nApplying square operation: {apply_operation(5, square)}\")\n", "\n", "# Type hints with protocols (structural subtyping)\n", "from typing import Protocol\n", "\n", "class Drawable(Protocol):\n", "    def draw(self) -> str: ...\n", "\n", "class Circle:\n", "    def draw(self) -> str:\n", "        return \"Drawing a circle\"\n", "\n", "class Square:\n", "    def draw(self) -> str:\n", "        return \"Drawing a square\"\n", "\n", "def draw_shape(shape: Drawable) -> None:\n", "    print(shape.draw())\n", "\n", "print(\"\\nDrawing shapes:\")\n", "draw_shape(Circle())\n", "draw_shape(Square())\n", "\n", "# Type hints with context managers\n", "from typing import ContextManager\n", "from contextlib import contextmanager\n", "\n", "@contextmanager\n", "def managed_resource(name: str) -> ContextManager[str]:\n", "    print(f\"Acquiring {name}\")\n", "    try:\n", "        yield name\n", "    finally:\n", "        print(f\"Releasing {name}\")\n", "\n", "print(\"\\nUsing context manager:\")\n", "with managed_resource(\"test_resource\") as resource:\n", "    print(f\"Using {resource}\")\n", "\n", "# Type checking with mypy\n", "# To check types: mypy your_file.py\n", "def demonstrates_type_error(x: int) -> str:\n", "    # This would raise a type error when checked with mypy\n", "    # return x + 1  # Error: Incompatible return value type\n", "    return str(x + 1)  # Correct: converts int to str\n", "\n", "print(f\"\\nDemonstrates type checking: {demonstrates_type_error(5)}\")"]}, {"cell_type": "markdown", "id": "1020ac6a", "metadata": {}, "source": ["# Regular Expressions {#regex}\n", "\n", "Regular expressions (regex) provide powerful pattern matching and text manipulation capabilities in Python. The `re` module is the core tool for working with regular expressions."]}, {"cell_type": "code", "execution_count": null, "id": "d009982f", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "# Basic pattern matching\n", "text = \"The quick brown fox jumps over the lazy dog\"\n", "pattern = r\"fox\"\n", "\n", "# Simple match\n", "match = re.search(pattern, text)\n", "print(f\"Found '{pattern}' at position: {match.start() if match else 'not found'}\")\n", "\n", "# Find all occurrences\n", "text_with_repeats = \"The fox and the fox play in the forest\"\n", "all_matches = re.findall(r\"fox\", text_with_repeats)\n", "print(f\"\\nFound {len(all_matches)} occurrences of 'fox'\")\n", "\n", "# Pattern with metacharacters\n", "# \\d - digit, \\w - word character, \\s - whitespace\n", "phone_pattern = r\"\\d{3}-\\d{3}-\\d{4}\"\n", "phone_numbers = \"\"\"\n", "    ************\n", "    ************\n", "    not-a-number\n", "\"\"\"\n", "valid_numbers = re.findall(phone_pattern, phone_numbers)\n", "print(f\"\\nValid phone numbers: {valid_numbers}\")\n", "\n", "# Character classes and quantifiers\n", "# Match words that start with 'a' or 'A'\n", "text = \"Apple and banana are fruits. an APPLE a day keeps the doctor away\"\n", "a_words = re.findall(r'\\b[Aa]\\w+', text)\n", "print(f\"\\nWords starting with 'a' or 'A': {a_words}\")\n", "\n", "# Groups and capturing\n", "# Parse date in format \"YYYY-MM-DD\"\n", "date_pattern = r\"(\\d{4})-(\\d{2})-(\\d{2})\"\n", "date_text = \"Event date: 2025-08-09\"\n", "date_match = re.search(date_pattern, date_text)\n", "if date_match:\n", "    year, month, day = date_match.groups()\n", "    print(f\"\\nDate parts - Year: {year}, Month: {month}, Day: {day}\")\n", "\n", "# Named groups\n", "pattern_named = r\"(?P<year>\\d{4})-(?P<month>\\d{2})-(?P<day>\\d{2})\"\n", "date_match = re.search(pattern_named, date_text)\n", "if date_match:\n", "    print(f\"Using named groups: {date_match.groupdict()}\")\n", "\n", "# Pattern substitution\n", "text = \"I love cats, cats are great, cats are amazing\"\n", "new_text = re.sub(r\"cats\", \"dogs\", text)\n", "print(f\"\\nAfter substitution: {new_text}\")\n", "\n", "# Case-insensitive matching\n", "text = \"Python is PYTHON is Python\"\n", "matches = re.findall(r\"python\", text, re.IGNORECASE)\n", "print(f\"\\nCase-insensitive matches: {matches}\")\n", "\n", "# Word boundaries\n", "text = \"Find word in words but not in sword\"\n", "whole_word_matches = re.findall(r\"\\bword\\b\", text)\n", "print(f\"\\nWhole word matches: {whole_word_matches}\")\n", "\n", "# Email pattern matching\n", "email_pattern = r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'\n", "emails = \"\"\"\n", "    <EMAIL>\n", "    <EMAIL>\n", "    not@<EMAIL>\n", "    invalid.email@\n", "\"\"\"\n", "valid_emails = re.findall(email_pattern, emails)\n", "print(f\"\\nValid email addresses: {valid_emails}\")\n", "\n", "# Greedy vs non-greedy matching\n", "text = \"<start>content1</start><start>content2</start>\"\n", "# Greedy matching\n", "greedy = re.findall(r\"<start>.*</start>\", text)\n", "# Non-greedy matching\n", "non_greedy = re.findall(r\"<start>.*?</start>\", text)\n", "print(f\"\\nGreedy matching: {greedy}\")\n", "print(f\"Non-greedy matching: {non_greedy}\")\n", "\n", "# Compile patterns for better performance\n", "pattern = re.compile(r\"\\b\\w+@\\w+\\.\\w+\\b\")\n", "emails = [\"<EMAIL>\", \"invalid.email\", \"<EMAIL>\"]\n", "for email in emails:\n", "    if pattern.match(email):\n", "        print(f\"\\nValid email format: {email}\")\n", "    else:\n", "        print(f\"Invalid email format: {email}\")\n", "\n", "# Lookahead and lookbehind assertions\n", "# Positive lookahead\n", "text = \"<PERSON>, <PERSON>, <PERSON>\"\n", "names = re.findall(r\"\\w+(?=\\sDoe)\", text)  # Names followed by \" <PERSON>e\"\n", "print(f\"\\nNames followed by 'Doe': {names}\")\n", "\n", "# Negative lookbehind\n", "text = \"£100, $200, €300\"\n", "amounts = re.findall(r\"(?<!£)\\d+\", text)  # Numbers not preceded by £\n", "print(f\"Amounts not in GBP: {amounts}\")\n", "\n", "# Split string using regex\n", "text = \"apple,banana;cherry|date grape\"\n", "parts = re.split(r'[,;|\\s]+', text)\n", "print(f\"\\nSplit result: {parts}\")\n", "\n", "# Complex pattern example: URL parsing\n", "url_pattern = r\"\"\"\n", "    https?://                 # http:// or https://\n", "    (?:www\\.)?               # optional www.\n", "    (?P<domain>[\\w-]+)       # domain name\n", "    (?P<tld>\\.[a-z]{2,})    # top-level domain\n", "    (?P<path>/[\\w/.-]*)?     # optional path\n", "\"\"\"\n", "urls = [\n", "    \"https://www.example.com/path\",\n", "    \"http://subdomain.site.co.uk/\",\n", "    \"https://invalid\"\n", "]\n", "\n", "url_regex = re.compile(url_pattern, re.VERBOSE)\n", "for url in urls:\n", "    match = url_regex.match(url)\n", "    if match:\n", "        print(f\"\\nURL parts for {url}:\")\n", "        print(match.groupdict())"]}, {"cell_type": "markdown", "id": "a00cb0f1", "metadata": {}, "source": ["# DateTime Operations {#datetime}\n", "\n", "Python provides robust datetime handling through the `datetime` module, which includes classes for working with dates, times, timedeltas, and timezone-aware operations."]}, {"cell_type": "code", "execution_count": null, "id": "b839db85", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date, time, timedelta\n", "from zoneinfo import ZoneInfo  # Python 3.9+\n", "import calendar\n", "\n", "# Current date and time\n", "now = datetime.now()\n", "print(f\"Current datetime: {now}\")\n", "print(f\"Date: {now.date()}\")\n", "print(f\"Time: {now.time()}\")\n", "\n", "# Creating datetime objects\n", "specific_date = datetime(2025, 8, 9, 15, 30, 0)\n", "print(f\"\\nSpecific datetime: {specific_date}\")\n", "\n", "# Parsing datetime strings\n", "date_string = \"2025-08-09 15:30:00\"\n", "parsed_date = datetime.strptime(date_string, \"%Y-%m-%d %H:%M:%S\")\n", "print(f\"Parsed datetime: {parsed_date}\")\n", "\n", "# Formatting dates\n", "formatted_date = now.strftime(\"%B %d, %Y at %I:%M %p\")\n", "print(f\"\\nFormatted datetime: {formatted_date}\")\n", "\n", "# Date arithmetic\n", "tomorrow = now + <PERSON><PERSON><PERSON>(days=1)\n", "next_week = now + <PERSON><PERSON><PERSON>(weeks=1)\n", "print(f\"\\nTomorrow: {tomorrow.date()}\")\n", "print(f\"Next week: {next_week.date()}\")\n", "\n", "# Time differences\n", "time_diff = tomorrow - now\n", "print(f\"Time until tomorrow: {time_diff}\")\n", "\n", "# Working with time zones\n", "utc_now = datetime.now(ZoneInfo(\"UTC\"))\n", "ny_now = datetime.now(ZoneInfo(\"America/New_York\"))\n", "tokyo_now = datetime.now(ZoneInfo(\"Asia/Tokyo\"))\n", "\n", "print(\"\\nCurrent time in different zones:\")\n", "print(f\"UTC: {utc_now.strftime('%Y-%m-%d %H:%M %Z')}\")\n", "print(f\"New York: {ny_now.strftime('%Y-%m-%d %H:%M %Z')}\")\n", "print(f\"Tokyo: {tokyo_now.strftime('%Y-%m-%d %H:%M %Z')}\")\n", "\n", "# Converting between time zones\n", "utc_time = datetime.now(ZoneInfo(\"UTC\"))\n", "local_time = utc_time.astimezone()  # Convert to local time\n", "print(f\"\\nUTC time: {utc_time}\")\n", "print(f\"Local time: {local_time}\")\n", "\n", "# Working with calendar\n", "print(\"\\nCalendar operations:\")\n", "# Get calendar for current month\n", "cal = calendar.month(now.year, now.month)\n", "print(f\"Calendar for {calendar.month_name[now.month]} {now.year}:\")\n", "print(cal)\n", "\n", "# Check for leap year\n", "year = 2024\n", "is_leap = calendar.isleap(year)\n", "print(f\"\\nIs {year} a leap year? {is_leap}\")\n", "\n", "# Working with time deltas\n", "delta = timedelta(\n", "    days=50,\n", "    hours=8,\n", "    minutes=15\n", ")\n", "future_date = now + delta\n", "print(f\"\\nDate after {delta}: {future_date}\")\n", "\n", "# Date comparisons\n", "date1 = date(2025, 1, 1)\n", "date2 = date(2025, 12, 31)\n", "print(f\"\\nComparing dates:\")\n", "print(f\"{date1} is earlier than {date2}: {date1 < date2}\")\n", "print(f\"Days between: {(date2 - date1).days}\")\n", "\n", "# Time objects\n", "noon = time(12, 0, 0)\n", "midnight = time(0, 0, 0)\n", "print(f\"\\nTime comparisons:\")\n", "print(f\"Noon: {noon}\")\n", "print(f\"Midnight: {midnight}\")\n", "print(f\"Noon is later than midnight: {noon > midnight}\")\n", "\n", "# Working with timestamps\n", "timestamp = datetime.timestamp(now)\n", "from_timestamp = datetime.fromtimestamp(timestamp)\n", "print(f\"\\nTimestamp: {timestamp}\")\n", "print(f\"Datetime from timestamp: {from_timestamp}\")\n", "\n", "# Date components\n", "current_date = date.today()\n", "print(f\"\\nDate components:\")\n", "print(f\"Year: {current_date.year}\")\n", "print(f\"Month: {current_date.month}\")\n", "print(f\"Day: {current_date.day}\")\n", "print(f\"Weekday: {current_date.strftime('%A')}\")\n", "\n", "# First and last day of month\n", "first_day = date(current_date.year, current_date.month, 1)\n", "_, last_day = calendar.monthrange(current_date.year, current_date.month)\n", "last_date = date(current_date.year, current_date.month, last_day)\n", "print(f\"\\nFirst day of month: {first_day}\")\n", "print(f\"Last day of month: {last_date}\")\n", "\n", "# Business day calculations\n", "def is_business_day(day):\n", "    return day.weekday() < 5  # Monday = 0, Sunday = 6\n", "\n", "test_date = date(2025, 8, 9)\n", "print(f\"\\nIs {test_date} a business day? {is_business_day(test_date)}\")\n", "\n", "# Add business days\n", "def add_business_days(start_date, days):\n", "    current_date = start_date\n", "    remaining_days = days\n", "    while remaining_days > 0:\n", "        current_date += <PERSON><PERSON><PERSON>(days=1)\n", "        if is_business_day(current_date):\n", "            remaining_days -= 1\n", "    return current_date\n", "\n", "start = date.today()\n", "business_days = 5\n", "end_date = add_business_days(start, business_days)\n", "print(f\"\\nDate after {business_days} business days from {start}: {end_date}\")"]}, {"cell_type": "markdown", "id": "71f73ac0", "metadata": {}, "source": ["# Context Managers {#context}\n", "\n", "Context managers in Python provide a clean way to handle resource acquisition and release. They ensure proper cleanup of resources like files, network connections, and database transactions."]}, {"cell_type": "code", "execution_count": null, "id": "f7bb1dc4", "metadata": {}, "outputs": [], "source": ["from contextlib import contextmanager\n", "import time\n", "from typing import Generator\n", "\n", "# Basic context manager using class\n", "class FileHandler:\n", "    def __init__(self, filename: str, mode: str) -> None:\n", "        self.filename = filename\n", "        self.mode = mode\n", "        self.file = None\n", "\n", "    def __enter__(self):\n", "        self.file = open(self.filename, self.mode)\n", "        return self.file\n", "\n", "    def __exit__(self, exc_type, exc_val, exc_tb):\n", "        if self.file:\n", "            self.file.close()\n", "        # Return True to suppress exceptions, False to propagate them\n", "        return False\n", "\n", "# Using the class-based context manager\n", "print(\"=== Class-based Context Manager ===\")\n", "with <PERSON><PERSON><PERSON><PERSON>('test.txt', 'w') as f:\n", "    f.write('Hello from class-based context manager!')\n", "\n", "# Read the file to verify\n", "with <PERSON><PERSON><PERSON><PERSON>('test.txt', 'r') as f:\n", "    content = f.read()\n", "    print(f\"File content: {content}\")\n", "\n", "# Context manager using decorator\n", "@contextmanager\n", "def timer() -> Generator[None, None, None]:\n", "    \"\"\"Measure execution time of a code block.\"\"\"\n", "    start = time.time()\n", "    try:\n", "        yield\n", "    finally:\n", "        end = time.time()\n", "        print(f\"Execution time: {end - start:.4f} seconds\")\n", "\n", "# Using the timer context manager\n", "print(\"\\n=== Timer Context Manager ===\")\n", "with timer():\n", "    # Simulate some work\n", "    time.sleep(1)\n", "    print(\"Work completed!\")\n", "\n", "# Context manager for temporary value change\n", "@contextmanager\n", "def temporary_attribute(obj: object, name: str, value: any) -> Generator[None, None, None]:\n", "    \"\"\"Temporarily change an object's attribute.\"\"\"\n", "    original = getattr(obj, name)\n", "    setattr(obj, name, value)\n", "    try:\n", "        yield\n", "    finally:\n", "        setattr(obj, name, original)\n", "\n", "# Example class for demonstration\n", "class Configuration:\n", "    def __init__(self):\n", "        self.debug = False\n", "\n", "    def show_status(self):\n", "        print(f\"Debug mode: {self.debug}\")\n", "\n", "# Using temporary attribute context manager\n", "print(\"\\n=== Temporary Attribute Context Manager ===\")\n", "config = Configuration()\n", "print(\"Initial status:\")\n", "config.show_status()\n", "\n", "print(\"\\nInside context manager:\")\n", "with temporary_attribute(config, 'debug', True):\n", "    config.show_status()\n", "\n", "print(\"\\nAfter context manager:\")\n", "config.show_status()\n", "\n", "# Nested context managers\n", "@contextmanager\n", "def indented_print(level: int = 1) -> Generator[None, None, None]:\n", "    \"\"\"Print with indentation.\"\"\"\n", "    prefix = \"    \" * level\n", "    try:\n", "        yield lambda x: print(f\"{prefix}{x}\")\n", "    finally:\n", "        pass\n", "\n", "# Using nested context managers\n", "print(\"\\n=== Nested Context Managers ===\")\n", "with indented_print(1) as print1:\n", "    print1(\"Level 1\")\n", "    with indented_print(2) as print2:\n", "        print2(\"Level 2\")\n", "        with indented_print(3) as print3:\n", "            print3(\"Level 3\")\n", "\n", "# Context manager for exception handling\n", "@contextmanager\n", "def handled_errors(*exceptions):\n", "    \"\"\"<PERSON><PERSON> specified exceptions gracefully.\"\"\"\n", "    try:\n", "        yield\n", "    except exceptions as e:\n", "        print(f\"Handled error: {e}\")\n", "    else:\n", "        print(\"No errors occurred\")\n", "\n", "# Using error handler context manager\n", "print(\"\\n=== Error Handler Context Manager ===\")\n", "with handled_errors(ValueError, ZeroDivisionError):\n", "    print(\"Attempting division by zero...\")\n", "    1 / 0\n", "\n", "with handled_errors(ValueError, ZeroDivisionError):\n", "    print(\"Attempting valid operation...\")\n", "    1 + 1\n", "\n", "# Resource manager with cleanup\n", "class DatabaseConnection:\n", "    def __init__(self, db_name: str):\n", "        self.db_name = db_name\n", "        \n", "    def connect(self):\n", "        print(f\"Connecting to database '{self.db_name}'...\")\n", "        \n", "    def disconnect(self):\n", "        print(f\"Disconnecting from database '{self.db_name}'...\")\n", "        \n", "    def query(self, sql: str):\n", "        print(f\"Executing query: {sql}\")\n", "        \n", "@contextmanager\n", "def database_connection(db_name: str) -> Generator[DatabaseConnection, None, None]:\n", "    \"\"\"Manage database connection lifecycle.\"\"\"\n", "    db = DatabaseConnection(db_name)\n", "    try:\n", "        db.connect()\n", "        yield db\n", "    finally:\n", "        db.disconnect()\n", "\n", "# Using database connection context manager\n", "print(\"\\n=== Database Connection Context Manager ===\")\n", "with database_connection(\"users_db\") as db:\n", "    db.query(\"SELECT * FROM users\")\n", "\n", "# Context manager for measuring memory usage\n", "import os\n", "import psutil\n", "\n", "@contextmanager\n", "def measure_memory() -> Generator[None, None, None]:\n", "    \"\"\"Measure memory usage of a code block.\"\"\"\n", "    process = psutil.Process(os.getpid())\n", "    start_mem = process.memory_info().rss / 1024 / 1024  # MB\n", "    try:\n", "        yield\n", "    finally:\n", "        end_mem = process.memory_info().rss / 1024 / 1024  # MB\n", "        print(f\"Memory usage: {end_mem - start_mem:.2f} MB\")\n", "\n", "# Using memory measurement context manager\n", "print(\"\\n=== Memory Measurement Context Manager ===\")\n", "with measure_memory():\n", "    # Create a large list\n", "    big_list = list(range(1000000))\n", "    print(\"Created a large list\")\n", "\n", "# Clean up\n", "import os\n", "if os.path.exists('test.txt'):\n", "    os.remove('test.txt')"]}, {"cell_type": "markdown", "id": "bc594fdb", "metadata": {}, "source": ["# Context Managers {#context}\n", "\n", "Context managers in Python provide a clean way to manage resources that need setup and cleanup operations. They ensure proper handling of resources like file handles, network connections, or database transactions, automatically cleaning up resources when you're done with them.\n", "\n", "## Key Concepts\n", "\n", "1. **Protocol Implementation**\n", "   - Requires `__enter__()` and `__exit__()` methods\n", "   - `__enter__()` sets up the context and returns resources\n", "   - `__exit__()` handles cleanup and exception management\n", "\n", "2. **The `with` Statement**\n", "   - Provides syntax sugar for using context managers\n", "   - Automatically calls `__enter__()` and `__exit__()`\n", "   - Ensures cleanup even if exceptions occur\n", "\n", "3. **contextlib Decorator**\n", "   - `@contextmanager` decorator for function-based context managers\n", "   - Uses generator functions with a single `yield` statement\n", "   - Simplifies creation of context managers\n", "\n", "## Common Use Cases\n", "\n", "1. **Resource Management**\n", "   - File operations\n", "   - Database connections\n", "   - Network sockets\n", "   - Locks and synchronization primitives\n", "\n", "2. **State Management**\n", "   - Temporary settings changes\n", "   - Environment modifications\n", "   - Configuration overrides\n", "\n", "3. **Measurement and Monitoring**\n", "   - Timing code execution\n", "   - Memory usage tracking\n", "   - Performance profiling\n", "\n", "4. **E<PERSON><PERSON>**\n", "   - Exception suppression or transformation\n", "   - Cleanup on success or failure\n", "   - Transaction management\n", "\n", "Below are examples demonstrating various types of context managers, from basic file handling to more advanced use cases like resource management and state modification."]}, {"cell_type": "code", "execution_count": null, "id": "86a7c2c4", "metadata": {}, "outputs": [], "source": ["from contextlib import contextmanager\n", "import time\n", "from typing import Generator\n", "\n", "# Basic context manager using class\n", "class FileHandler:\n", "    def __init__(self, filename: str, mode: str) -> None:\n", "        self.filename = filename\n", "        self.mode = mode\n", "        self.file = None\n", "\n", "    def __enter__(self):\n", "        self.file = open(self.filename, self.mode)\n", "        return self.file\n", "\n", "    def __exit__(self, exc_type, exc_val, exc_tb):\n", "        if self.file:\n", "            self.file.close()\n", "        # Return True to suppress exceptions, False to propagate them\n", "        return False\n", "\n", "# Using the class-based context manager\n", "print(\"=== Class-based Context Manager ===\")\n", "with <PERSON><PERSON><PERSON><PERSON>('test.txt', 'w') as f:\n", "    f.write('Hello from class-based context manager!')\n", "\n", "# Read the file to verify\n", "with <PERSON><PERSON><PERSON><PERSON>('test.txt', 'r') as f:\n", "    content = f.read()\n", "    print(f\"File content: {content}\")\n", "\n", "# Context manager using decorator\n", "@contextmanager\n", "def timer() -> Generator[None, None, None]:\n", "    \"\"\"Measure execution time of a code block.\"\"\"\n", "    start = time.time()\n", "    try:\n", "        yield\n", "    finally:\n", "        end = time.time()\n", "        print(f\"Execution time: {end - start:.4f} seconds\")\n", "\n", "# Using the timer context manager\n", "print(\"\\n=== Timer Context Manager ===\")\n", "with timer():\n", "    # Simulate some work\n", "    time.sleep(1)\n", "    print(\"Work completed!\")\n", "\n", "# Context manager for temporary value change\n", "@contextmanager\n", "def temporary_attribute(obj: object, name: str, value: any) -> Generator[None, None, None]:\n", "    \"\"\"Temporarily change an object's attribute.\"\"\"\n", "    original = getattr(obj, name)\n", "    setattr(obj, name, value)\n", "    try:\n", "        yield\n", "    finally:\n", "        setattr(obj, name, original)\n", "\n", "# Example class for demonstration\n", "class Configuration:\n", "    def __init__(self):\n", "        self.debug = False\n", "\n", "    def show_status(self):\n", "        print(f\"Debug mode: {self.debug}\")\n", "\n", "# Using temporary attribute context manager\n", "print(\"\\n=== Temporary Attribute Context Manager ===\")\n", "config = Configuration()\n", "print(\"Initial status:\")\n", "config.show_status()\n", "\n", "print(\"\\nInside context manager:\")\n", "with temporary_attribute(config, 'debug', True):\n", "    config.show_status()\n", "\n", "print(\"\\nAfter context manager:\")\n", "config.show_status()\n", "\n", "# Nested context managers\n", "@contextmanager\n", "def indented_print(level: int = 1) -> Generator[None, None, None]:\n", "    \"\"\"Print with indentation.\"\"\"\n", "    prefix = \"    \" * level\n", "    try:\n", "        yield lambda x: print(f\"{prefix}{x}\")\n", "    finally:\n", "        pass\n", "\n", "# Using nested context managers\n", "print(\"\\n=== Nested Context Managers ===\")\n", "with indented_print(1) as print1:\n", "    print1(\"Level 1\")\n", "    with indented_print(2) as print2:\n", "        print2(\"Level 2\")\n", "        with indented_print(3) as print3:\n", "            print3(\"Level 3\")\n", "\n", "# Context manager for exception handling\n", "@contextmanager\n", "def handled_errors(*exceptions):\n", "    \"\"\"<PERSON><PERSON> specified exceptions gracefully.\"\"\"\n", "    try:\n", "        yield\n", "    except exceptions as e:\n", "        print(f\"Handled error: {e}\")\n", "    else:\n", "        print(\"No errors occurred\")\n", "\n", "# Using error handler context manager\n", "print(\"\\n=== Error Handler Context Manager ===\")\n", "with handled_errors(ValueError, ZeroDivisionError):\n", "    print(\"Attempting division by zero...\")\n", "    1 / 0\n", "\n", "with handled_errors(ValueError, ZeroDivisionError):\n", "    print(\"Attempting valid operation...\")\n", "    1 + 1\n", "\n", "# Resource manager with cleanup\n", "class DatabaseConnection:\n", "    def __init__(self, db_name: str):\n", "        self.db_name = db_name\n", "        \n", "    def connect(self):\n", "        print(f\"Connecting to database '{self.db_name}'...\")\n", "        \n", "    def disconnect(self):\n", "        print(f\"Disconnecting from database '{self.db_name}'...\")\n", "        \n", "    def query(self, sql: str):\n", "        print(f\"Executing query: {sql}\")\n", "        \n", "@contextmanager\n", "def database_connection(db_name: str) -> Generator[DatabaseConnection, None, None]:\n", "    \"\"\"Manage database connection lifecycle.\"\"\"\n", "    db = DatabaseConnection(db_name)\n", "    try:\n", "        db.connect()\n", "        yield db\n", "    finally:\n", "        db.disconnect()\n", "\n", "# Using database connection context manager\n", "print(\"\\n=== Database Connection Context Manager ===\")\n", "with database_connection(\"users_db\") as db:\n", "    db.query(\"SELECT * FROM users\")\n", "\n", "# Context manager for measuring memory usage\n", "import os\n", "import psutil\n", "\n", "@contextmanager\n", "def measure_memory() -> Generator[None, None, None]:\n", "    \"\"\"Measure memory usage of a code block.\"\"\"\n", "    process = psutil.Process(os.getpid())\n", "    start_mem = process.memory_info().rss / 1024 / 1024  # MB\n", "    try:\n", "        yield\n", "    finally:\n", "        end_mem = process.memory_info().rss / 1024 / 1024  # MB\n", "        print(f\"Memory usage: {end_mem - start_mem:.2f} MB\")\n", "\n", "# Using memory measurement context manager\n", "print(\"\\n=== Memory Measurement Context Manager ===\")\n", "with measure_memory():\n", "    # Create a large list\n", "    big_list = list(range(1000000))\n", "    print(\"Created a large list\")\n", "\n", "# Clean up\n", "import os\n", "if os.path.exists('test.txt'):\n", "    os.remove('test.txt')"]}, {"cell_type": "markdown", "id": "2d816175", "metadata": {}, "source": ["# Virtual Environments {#venv}\n", "\n", "Virtual environments in Python are isolated environments that allow you to manage project-specific dependencies without interfering with other projects or the system Python installation. They are essential for:\n", "\n", "1. **Dependency Isolation**\n", "   - Each project can have its own dependencies\n", "   - Avoid conflicts between different versions of packages\n", "   - Keep system Python installation clean\n", "\n", "2. **Project Portability**\n", "   - Easy to share project requirements\n", "   - Consistent development environments across team members\n", "   - Simple deployment to production\n", "\n", "3. **Version Management**\n", "   - Use different Python versions for different projects\n", "   - Test compatibility with various package versions\n", "   - Maintain multiple configurations simultaneously\n", "\n", "## Key Tools\n", "\n", "1. **venv** (built-in)\n", "   - Standard library module for creating virtual environments\n", "   - Lightweight and included with Python\n", "   - Recommended for most Python projects\n", "\n", "2. **virtualenv**\n", "   - Third-party alternative to venv\n", "   - More features than venv\n", "   - Better support for older Python versions\n", "\n", "3. **conda**\n", "   - Package and environment management system\n", "   - Popular in data science\n", "   - Handles both Python and non-Python dependencies\n", "\n", "Below are examples of creating and managing virtual environments using different tools, along with best practices for dependency management."]}, {"cell_type": "code", "execution_count": null, "id": "c5100628", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import subprocess\n", "from pathlib import Path\n", "\n", "def run_command(cmd):\n", "    \"\"\"Run a command and return its output\"\"\"\n", "    try:\n", "        result = subprocess.run(\n", "            cmd,\n", "            shell=True,\n", "            check=True,\n", "            capture_output=True,\n", "            text=True\n", "        )\n", "        return result.stdout\n", "    except subprocess.CalledProcessError as e:\n", "        return f\"Error: {e.stderr}\"\n", "\n", "# Show current Python information\n", "print(\"=== Current Python Environment ===\")\n", "print(f\"Python Version: {sys.version}\")\n", "print(f\"Python Executable: {sys.executable}\")\n", "print(f\"Python Path: {sys.path[0]}\")\n", "\n", "# Create a virtual environment using venv\n", "print(\"\\n=== Creating Virtual Environment with venv ===\")\n", "venv_path = Path(\"example_venv\")\n", "if not venv_path.exists():\n", "    import venv\n", "    print(\"Creating new virtual environment...\")\n", "    venv.create(venv_path, with_pip=True)\n", "    print(f\"Virtual environment created at: {venv_path.absolute()}\")\n", "else:\n", "    print(f\"Virtual environment already exists at: {venv_path.absolute()}\")\n", "\n", "# Show venv structure\n", "print(\"\\nVirtual Environment Structure:\")\n", "venv_files = list(venv_path.glob(\"**/*\"))\n", "for file in sorted(venv_files):\n", "    if file.is_file():\n", "        print(f\"- {file.relative_to(venv_path)}\")\n", "\n", "# Demonstrate activation and package installation (commands only)\n", "print(\"\\n=== Virtual Environment Usage Examples ===\")\n", "print(\"Activation Commands:\")\n", "print(\"Windows CMD: .\\\\example_venv\\\\Scripts\\\\activate.bat\")\n", "print(\"Windows PowerShell: .\\\\example_venv\\\\Scripts\\\\Activate.ps1\")\n", "print(\"Unix/MacOS: source example_venv/bin/activate\")\n", "\n", "# Create requirements.txt\n", "requirements = \"\"\"\n", "# Web Framework\n", "flask==2.0.1\n", "\n", "# Data Processing\n", "pandas==1.3.0\n", "numpy==1.21.0\n", "\n", "# Testing\n", "pytest==6.2.5\n", "pytest-cov==2.12.1\n", "\n", "# Documentation\n", "sphinx==4.0.2\n", "\"\"\"\n", "\n", "req_file = Path(\"requirements.txt\")\n", "req_file.write_text(requirements)\n", "print(\"\\n=== Package Management ===\")\n", "print(\"Created requirements.txt with contents:\")\n", "print(requirements)\n", "\n", "# Show common pip commands\n", "print(\"\\nCommon pip commands:\")\n", "pip_commands = \"\"\"\n", "# Install packages\n", "pip install package_name\n", "pip install -r requirements.txt\n", "\n", "# Show installed packages\n", "pip list\n", "pip freeze\n", "\n", "# Upgrade packages\n", "pip install --upgrade package_name\n", "\n", "# Uninstall packages\n", "pip uninstall package_name\n", "\n", "# Show package info\n", "pip show package_name\n", "\"\"\"\n", "print(pip_commands)\n", "\n", "# Environment variables in virtual environments\n", "print(\"\\n=== Environment Variables ===\")\n", "env_vars = {\n", "    \"PYTHONPATH\": \"Add custom module directories\",\n", "    \"PYTHONHOME\": \"Python installation directory\",\n", "    \"VIRTUAL_ENV\": \"Current virtual environment path\",\n", "    \"PATH\": \"System path (modified during activation)\"\n", "}\n", "\n", "for var, description in env_vars.items():\n", "    value = os.environ.get(var, \"Not set\")\n", "    print(f\"{var}: {value}\")\n", "    print(f\"Purpose: {description}\\n\")\n", "\n", "# Best practices\n", "print(\"=== Virtual Environment Best Practices ===\")\n", "best_practices = \"\"\"\n", "1. Always use virtual environments for projects\n", "2. Keep requirements.txt up to date\n", "3. Use version pinning for reproducibility\n", "4. Include only direct dependencies\n", "5. Separate development and production dependencies\n", "6. Use .gitignore to exclude virtual environment\n", "7. Document environment setup in README\n", "8. Regular dependency updates and security checks\n", "\"\"\"\n", "print(best_practices)\n", "\n", "# Clean up\n", "print(\"\\n=== Cleaning Up ===\")\n", "if venv_path.exists():\n", "    import shutil\n", "    shutil.rmtree(venv_path)\n", "    print(f\"Removed virtual environment: {venv_path}\")\n", "\n", "if req_file.exists():\n", "    req_file.unlink()\n", "    print(f\"Removed {req_file}\")\n", "\n", "# Additional tools and alternatives\n", "print(\"\\n=== Additional Tools ===\")\n", "tools = {\n", "    \"Poetry\": \"Modern dependency management and packaging\",\n", "    \"Pipenv\": \"Combines pip and virtualenv, with lock files\",\n", "    \"conda\": \"Package and environment management (popular in data science)\",\n", "    \"pyenv\": \"Python version management\",\n", "    \"tox\": \"Test automation and virtual environment management\",\n", "    \"virtualenvwrapper\": \"Wrapper for easier virtualenv management\"\n", "}\n", "\n", "for tool, description in tools.items():\n", "    print(f\"{tool:15} - {description}\")"]}, {"cell_type": "markdown", "id": "f68c4323", "metadata": {}, "source": ["# Testing {#testing}\n", "\n", "Testing is a crucial part of software development that helps ensure code reliability and maintainability. Python provides several tools and frameworks for testing, with pytest being the most popular choice.\n", "\n", "## Testing Fundamentals\n", "\n", "1. **Test Types**\n", "   - Unit Tests: Test individual components in isolation\n", "   - Integration Tests: Test component interactions\n", "   - Functional Tests: Test complete features\n", "   - End-to-End Tests: Test entire system workflows\n", "\n", "2. **Testing Best Practices**\n", "   - Write tests before or alongside code (TDD)\n", "   - Keep tests simple and focused\n", "   - Use descriptive test names\n", "   - Follow AAA pattern: <PERSON><PERSON><PERSON>, <PERSON>, Assert\n", "   - Maintain test independence\n", "\n", "3. **Key Testing Tools**\n", "   - pytest: Modern test framework\n", "   - unittest: Standard library test framework\n", "   - doctest: Test examples in docstrings\n", "   - mock: <PERSON><PERSON> objects for testing\n", "   - coverage: Measure code coverage\n", "\n", "## pytest Features\n", "\n", "1. **Fixtures**\n", "   - Setup and teardown\n", "   - Dependency injection\n", "   - Scope control (function, class, module, session)\n", "\n", "2. **Assertions**\n", "   - Rich comparison details\n", "   - Built-in assertion rewriting\n", "   - Custom assertion messages\n", "\n", "3. **Parameterization**\n", "   - Test multiple inputs\n", "   - Data-driven testing\n", "   - Reusable test logic\n", "\n", "Below are examples demonstrating various testing approaches and best practices using pytest."]}, {"cell_type": "code", "execution_count": null, "id": "72558c13", "metadata": {}, "outputs": [], "source": ["# Sample code to test\n", "class Calculator:\n", "    def add(self, a: float, b: float) -> float:\n", "        \"\"\"Add two numbers.\"\"\"\n", "        return a + b\n", "    \n", "    def subtract(self, a: float, b: float) -> float:\n", "        \"\"\"Subtract b from a.\"\"\"\n", "        return a - b\n", "    \n", "    def multiply(self, a: float, b: float) -> float:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        return a * b\n", "    \n", "    def divide(self, a: float, b: float) -> float:\n", "        \"\"\"Divide a by b.\"\"\"\n", "        if b == 0:\n", "            raise ValueError(\"Cannot divide by zero\")\n", "        return a / b\n", "\n", "# Basic unit tests\n", "def test_calculator_add():\n", "    calc = Calculator()\n", "    assert calc.add(2, 3) == 5, \"Addition failed\"\n", "    assert calc.add(-1, 1) == 0, \"Addition with negative numbers failed\"\n", "    assert calc.add(0.1, 0.2) == pytest.approx(0.3), \"Float addition failed\"\n", "\n", "def test_calculator_divide():\n", "    calc = Calculator()\n", "    assert calc.divide(6, 2) == 3, \"Division failed\"\n", "    assert calc.divide(5, 2) == 2.5, \"Float division failed\"\n", "    \n", "    # Test division by zero\n", "    with pytest.raises(ValueError) as exc_info:\n", "        calc.divide(1, 0)\n", "    assert str(exc_info.value) == \"Cannot divide by zero\"\n", "\n", "# Parameterized tests\n", "@pytest.mark.parametrize(\"a, b, expected\", [\n", "    (2, 3, 5),\n", "    (-1, 1, 0),\n", "    (0, 0, 0),\n", "    (0.1, 0.2, 0.3)\n", "])\n", "def test_calculator_add_parameterized(a, b, expected):\n", "    calc = Calculator()\n", "    assert calc.add(a, b) == pytest.approx(expected)\n", "\n", "# Fixtures\n", "@pytest.fixture\n", "def calculator():\n", "    \"\"\"Provide a Calculator instance.\"\"\"\n", "    return Calculator()\n", "\n", "@pytest.fixture\n", "def complex_data():\n", "    \"\"\"Provide test data.\"\"\"\n", "    return {\n", "        'numbers': [1, 2, 3, 4, 5],\n", "        'operations': ['add', 'subtract', 'multiply', 'divide']\n", "    }\n", "\n", "def test_calculator_with_fixture(calculator):\n", "    \"\"\"Test calculator using fixture.\"\"\"\n", "    assert calculator.add(2, 3) == 5\n", "    assert calculator.subtract(5, 3) == 2\n", "    assert calculator.multiply(2, 3) == 6\n", "    assert calculator.divide(6, 2) == 3\n", "\n", "# Test classes\n", "class TestCalculator:\n", "    def setup_method(self):\n", "        \"\"\"Set up test fixtures before each test method.\"\"\"\n", "        self.calc = Calculator()\n", "    \n", "    def teardown_method(self):\n", "        \"\"\"Clean up after each test method.\"\"\"\n", "        pass\n", "    \n", "    def test_add(self):\n", "        assert self.calc.add(2, 3) == 5\n", "    \n", "    def test_subtract(self):\n", "        assert self.calc.subtract(5, 3) == 2\n", "    \n", "    def test_multiply(self):\n", "        assert self.calc.multiply(2, 3) == 6\n", "    \n", "    def test_divide(self):\n", "        assert self.calc.divide(6, 2) == 3\n", "\n", "# Mock example\n", "from unittest.mock import Mock, patch\n", "\n", "def fetch_data(url):\n", "    \"\"\"Simulate fetching data from a URL.\"\"\"\n", "    # In real code, this would make an HTTP request\n", "    pass\n", "\n", "def process_data(url):\n", "    \"\"\"Process data from URL.\"\"\"\n", "    data = fetch_data(url)\n", "    # Process the data\n", "    return data\n", "\n", "def test_process_data():\n", "    \"\"\"Test process_data using mock.\"\"\"\n", "    with patch('__main__.fetch_data') as mock_fetch:\n", "        mock_fetch.return_value = {'key': 'value'}\n", "        result = process_data('http://example.com')\n", "        assert result == {'key': 'value'}\n", "        mock_fetch.assert_called_once_with('http://example.com')\n", "\n", "# Custom fixtures with cleanup\n", "@pytest.fixture\n", "def temp_file():\n", "    \"\"\"Create a temporary file and clean it up after test.\"\"\"\n", "    file_path = 'test_data.txt'\n", "    with open(file_path, 'w') as f:\n", "        f.write('Test data')\n", "    yield file_path\n", "    # Cleanup\n", "    if os.path.exists(file_path):\n", "        os.remove(file_path)\n", "\n", "def test_with_temp_file(temp_file):\n", "    \"\"\"Test using temporary file fixture.\"\"\"\n", "    assert os.path.exists(temp_file)\n", "    with open(temp_file) as f:\n", "        content = f.read()\n", "    assert content == 'Test data'\n", "\n", "# Example of test marks\n", "@pytest.mark.slow\n", "def test_slow_operation():\n", "    \"\"\"Test marked as slow.\"\"\"\n", "    import time\n", "    time.sleep(1)\n", "    assert True\n", "\n", "@pytest.mark.skip(reason=\"Not implemented yet\")\n", "def test_future_feature():\n", "    \"\"\"Test that will be implemented later.\"\"\"\n", "    pass\n", "\n", "@pytest.mark.xfail(reason=\"Known bug\")\n", "def test_known_bug():\n", "    \"\"\"Test expected to fail.\"\"\"\n", "    assert False\n", "\n", "# Doctest example\n", "def factorial(n):\n", "    \"\"\"Calculate factorial of n.\n", "    \n", "    >>> factorial(0)\n", "    1\n", "    >>> factorial(1)\n", "    1\n", "    >>> factorial(5)\n", "    120\n", "    \"\"\"\n", "    if n < 0:\n", "        raise ValueError(\"n must be non-negative\")\n", "    return 1 if n <= 1 else n * factorial(n - 1)\n", "\n", "# Show how to run tests\n", "if __name__ == \"__main__\":\n", "    print(\"To run tests, use:\")\n", "    print(\"pytest test_file.py                     # Run all tests\")\n", "    print(\"pytest test_file.py -v                  # Verbose output\")\n", "    print(\"pytest test_file.py -k add              # Run tests matching 'add'\")\n", "    print(\"pytest test_file.py -m slow             # Run tests marked as 'slow'\")\n", "    print(\"pytest test_file.py --doctest-modules   # Run doctests\")\n", "    print(\"pytest test_file.py --cov=.             # Run with coverage\")"]}, {"cell_type": "markdown", "id": "e4dbe153", "metadata": {}, "source": ["# Logging {#logging}\n", "\n", "Logging is essential for understanding application behavior, debugging issues, and monitoring system health. Python's `logging` module provides a flexible framework for generating log messages with different severity levels and formats.\n", "\n", "## Logging Fundamentals\n", "\n", "1. **Log Levels**\n", "   - DEBUG: Detailed information for debugging\n", "   - INFO: General information about program execution\n", "   - WARNING: Indicate a potential problem\n", "   - ERROR: A more serious problem\n", "   - CRITICAL: Program may not be able to continue\n", "\n", "2. **Logging Components**\n", "   - Loggers: Interface for logging messages\n", "   - Handlers: Send logs to their destinations\n", "   - Formatters: Specify log message format\n", "   - Filters: Provide fine-grained control\n", "\n", "3. **Best Practices**\n", "   - Use appropriate log levels\n", "   - Include contextual information\n", "   - Configure logging at application startup\n", "   - Use structured logging when possible\n", "\n", "## Common Logging Patterns\n", "\n", "1. **Application Logging**\n", "   - Configuration in code or file\n", "   - Multiple handlers for different purposes\n", "   - Module-level loggers\n", "\n", "2. **System Logging**\n", "   - Rotating file handlers\n", "   - Syslog integration\n", "   - Remote logging\n", "\n", "3. **Development Logging**\n", "   - Debug output\n", "   - Performance logging\n", "   - Exception tracking\n", "\n", "Below are examples demonstrating various logging configurations and patterns."]}, {"cell_type": "code", "execution_count": null, "id": "8c34bf20", "metadata": {}, "outputs": [], "source": ["import logging\n", "import sys\n", "from pathlib import Path\n", "import json\n", "from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler\n", "import traceback\n", "import time\n", "import threading\n", "\n", "# Basic logging configuration\n", "print(\"=== Basic Logging ===\")\n", "logging.basicConfig(\n", "    level=logging.DEBUG,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "    datefmt='%Y-%m-%d %H:%M:%S'\n", ")\n", "\n", "# Create a logger\n", "logger = logging.getLogger(__name__)\n", "\n", "# Log messages at different levels\n", "logger.debug(\"Debug message\")\n", "logger.info(\"Info message\")\n", "logger.warning(\"Warning message\")\n", "logger.error(\"Error message\")\n", "logger.critical(\"Critical message\")\n", "\n", "# Logging with exception information\n", "try:\n", "    x = 1 / 0\n", "except Exception as e:\n", "    logger.exception(\"An error occurred\")\n", "\n", "# Custom logger with multiple handlers\n", "print(\"\\n=== Custom Logger Configuration ===\")\n", "\n", "def setup_logger(name, log_file, level=logging.INFO):\n", "    \"\"\"Set up a logger with file and console handlers.\"\"\"\n", "    logger = logging.getLogger(name)\n", "    logger.setLevel(level)\n", "    \n", "    # File handler\n", "    file_handler = logging.FileHandler(log_file)\n", "    file_handler.setLevel(level)\n", "    file_formatter = logging.Formatter(\n", "        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", "    )\n", "    file_handler.setFormatter(file_formatter)\n", "    \n", "    # Console handler\n", "    console_handler = logging.StreamHandler(sys.stdout)\n", "    console_handler.setLevel(level)\n", "    console_formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')\n", "    console_handler.setFormatter(console_formatter)\n", "    \n", "    # Add handlers\n", "    logger.addHandler(file_handler)\n", "    logger.addHandler(console_handler)\n", "    \n", "    return logger\n", "\n", "# Create and use custom logger\n", "app_logger = setup_logger('app_logger', 'app.log')\n", "app_logger.info(\"Application started\")\n", "app_logger.warning(\"System resources low\")\n", "\n", "# Rotating file handler\n", "print(\"\\n=== Rotating File Handler ===\")\n", "rotating_logger = logging.getLogger('rotating_logger')\n", "rotating_logger.setLevel(logging.INFO)\n", "\n", "# Create rotating handler\n", "rotating_handler = RotatingFileHandler(\n", "    'rotating.log',\n", "    maxBytes=1024,  # Rotate when file reaches 1KB\n", "    backupCount=3   # Keep up to 3 backup files\n", ")\n", "rotating_handler.setFormatter(\n", "    logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", ")\n", "rotating_logger.add<PERSON><PERSON><PERSON>(rotating_handler)\n", "\n", "# Generate some log messages\n", "for i in range(5):\n", "    rotating_logger.info(f\"This is rotating log message {i}\")\n", "\n", "# Time-based rotating handler\n", "print(\"\\n=== Time-based Rotating Handler ===\")\n", "timed_logger = logging.getLogger('timed_logger')\n", "timed_logger.setLevel(logging.INFO)\n", "\n", "# Rotate every minute (for demonstration)\n", "timed_handler = TimedRotatingFileHandler(\n", "    'timed_rotating.log',\n", "    when='M',       # Minute\n", "    interval=1,     # Every 1 minute\n", "    backupCount=3   # Keep up to 3 backup files\n", ")\n", "timed_handler.setFormatter(\n", "    logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", ")\n", "timed_logger.addHandler(timed_handler)\n", "\n", "timed_logger.info(\"This is a timed rotating log message\")\n", "\n", "# JSON logging formatter\n", "print(\"\\n=== JSON Logging ===\")\n", "class JsonFormatter(logging.Formatter):\n", "    \"\"\"Format log records as JSON.\"\"\"\n", "    def format(self, record):\n", "        log_data = {\n", "            'timestamp': self.formatTime(record),\n", "            'name': record.name,\n", "            'level': record.levelname,\n", "            'message': record.getMessage(),\n", "            'module': record.module,\n", "            'function': record.funcName\n", "        }\n", "        if record.exc_info:\n", "            log_data['exception'] = traceback.format_exception(*record.exc_info)\n", "        return json.dumps(log_data)\n", "\n", "# Create JSON logger\n", "json_logger = logging.getLogger('json_logger')\n", "json_logger.setLevel(logging.INFO)\n", "\n", "json_handler = logging.FileHandler('json_logs.json')\n", "json_handler.setFormatter(JsonFormatter())\n", "json_logger.addHandler(json_handler)\n", "\n", "json_logger.info(\"This is a JSON formatted log message\")\n", "try:\n", "    x = 1 / 0\n", "except Exception as e:\n", "    json_logger.exception(\"Error occurred in JSON logger\")\n", "\n", "# Context manager for temporary logging level\n", "print(\"\\n=== Logging Context Manager ===\")\n", "class LogLevel:\n", "    def __init__(self, logger, level):\n", "        self.logger = logger\n", "        self.level = level\n", "        self.previous_level = None\n", "\n", "    def __enter__(self):\n", "        self.previous_level = self.logger.getEffectiveLevel()\n", "        self.logger.setLevel(self.level)\n", "        return self.logger\n", "\n", "    def __exit__(self, exc_type, exc_val, exc_tb):\n", "        self.logger.setLevel(self.previous_level)\n", "\n", "# Use temporary log level\n", "debug_logger = logging.getLogger('debug_logger')\n", "debug_logger.setLevel(logging.INFO)\n", "\n", "debug_logger.debug(\"This won't show\")\n", "with LogLevel(debug_logger, logging.DEBUG):\n", "    debug_logger.debug(\"This will show\")\n", "debug_logger.debug(\"This won't show again\")\n", "\n", "# Thread-safe logging\n", "print(\"\\n=== Thread-safe Logging ===\")\n", "def worker(name):\n", "    logger = logging.getLogger(f'worker_{name}')\n", "    for i in range(3):\n", "        logger.info(f\"Worker {name} - Message {i}\")\n", "        time.sleep(0.1)\n", "\n", "# Create and start threads\n", "threads = []\n", "for i in range(3):\n", "    thread = threading.Thread(target=worker, args=(f\"Thread-{i}\",))\n", "    threads.append(thread)\n", "    thread.start()\n", "\n", "# Wait for all threads to complete\n", "for thread in threads:\n", "    thread.join()\n", "\n", "# Cleanup log files\n", "print(\"\\n=== Cleaning Up Log Files ===\")\n", "log_files = ['app.log', 'rotating.log', 'timed_rotating.log', 'json_logs.json']\n", "for log_file in log_files:\n", "    try:\n", "        Path(log_file).unlink()\n", "        print(f\"Removed {log_file}\")\n", "    except FileNotFoundError:\n", "        pass\n", "\n", "# Configuration from dictionary\n", "print(\"\\n=== Configuration from Dictionary ===\")\n", "config = {\n", "    'version': 1,\n", "    'formatters': {\n", "        'detailed': {\n", "            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", "        }\n", "    },\n", "    'handlers': {\n", "        'console': {\n", "            'class': 'logging.StreamHandler',\n", "            'formatter': 'detailed',\n", "            'level': 'INFO'\n", "        }\n", "    },\n", "    'loggers': {\n", "        'config_logger': {\n", "            'handlers': ['console'],\n", "            'level': 'INFO'\n", "        }\n", "    }\n", "}\n", "\n", "import logging.config\n", "logging.config.dictConfig(config)\n", "\n", "config_logger = logging.getLogger('config_logger')\n", "config_logger.info(\"Logger configured from dictionary\")"]}, {"cell_type": "markdown", "id": "7b6a7e49", "metadata": {}, "source": ["# External Libraries {#external}\n", "\n", "Python's ecosystem includes a vast collection of external libraries that extend its capabilities. Here we'll explore some of the most popular and useful libraries for different domains.\n", "\n", "## Essential Libraries\n", "\n", "1. **Data Processing**\n", "   - NumPy: Numerical computing and array operations\n", "   - Pandas: Data manipulation and analysis\n", "   - SciPy: Scientific computing and technical computing\n", "\n", "2. **Web Development**\n", "   - Requests: HTTP library for making requests\n", "   - FastAPI: Modern web framework\n", "   - Beautiful Soup: Web scraping and parsing\n", "\n", "3. **Data Visualization**\n", "   - Matplotlib: Basic plotting library\n", "   - Seaborn: Statistical data visualization\n", "   - Plotly: Interactive visualizations\n", "\n", "4. **Machine Learning**\n", "   - Scikit-learn: Machine learning algorithms\n", "   - TensorFlow: Deep learning framework\n", "   - PyTorch: Deep learning framework\n", "\n", "5. **Utility Libraries**\n", "   - Pillow: Image processing\n", "   - PyYAML: YAML file handling\n", "   - tqdm: Progress bars\n", "\n", "Below are examples demonstrating the use of various external libraries for common tasks."]}, {"cell_type": "code", "execution_count": null, "id": "c1d73d0c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import requests\n", "from PIL import Image\n", "from io import BytesIO\n", "import yaml\n", "from tqdm import tqdm\n", "import time\n", "\n", "# NumPy examples\n", "print(\"=== NumPy Examples ===\")\n", "# Create arrays\n", "arr1 = np.array([1, 2, 3, 4, 5])\n", "arr2 = np.array([[1, 2, 3], [4, 5, 6]])\n", "\n", "print(\"Array operations:\")\n", "print(f\"1D array: {arr1}\")\n", "print(f\"2D array:\\n{arr2}\")\n", "print(f\"Mean of arr1: {arr1.mean()}\")\n", "print(f\"Sum of arr2: {arr2.sum()}\")\n", "print(f\"Matrix multiplication:\\n{arr2 @ arr2.T}\")  # Matrix multiplication\n", "\n", "# Pandas examples\n", "print(\"\\n=== Pandas Examples ===\")\n", "# Create a DataFrame\n", "data = {\n", "    'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],\n", "    'age': [25, 30, 35, 28],\n", "    'city': ['New York', 'London', 'Paris', 'Tokyo']\n", "}\n", "df = pd.DataFrame(data)\n", "print(\"\\nDataFrame:\")\n", "print(df)\n", "\n", "# Basic operations\n", "print(\"\\nBasic DataFrame operations:\")\n", "print(\"\\nDescriptive statistics:\")\n", "print(df.describe())\n", "print(\"\\nGrouping by city:\")\n", "print(df.groupby('city').agg({'age': ['mean', 'count']}))\n", "\n", "# Data visualization with Matplotlib\n", "print(\"\\n=== Matplotlib Examples ===\")\n", "plt.figure(figsize=(10, 4))\n", "\n", "# Basic line plot\n", "plt.subplot(1, 2, 1)\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "plt.plot(x, y, label='sin(x)')\n", "plt.title('Basic Line Plot')\n", "plt.xlabel('x')\n", "plt.ylabel('sin(x)')\n", "plt.legend()\n", "\n", "# Scatter plot\n", "plt.subplot(1, 2, 2)\n", "x = np.random.normal(0, 1, 100)\n", "y = np.random.normal(0, 1, 100)\n", "plt.scatter(x, y, alpha=0.5)\n", "plt.title('Scatter Plot')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Seaborn examples\n", "print(\"\\n=== Seaborn Examples ===\")\n", "# Create sample data\n", "tips = sns.load_dataset('tips')\n", "\n", "plt.figure(figsize=(10, 4))\n", "\n", "# Box plot\n", "plt.subplot(1, 2, 1)\n", "sns.boxplot(x='day', y='total_bill', data=tips)\n", "plt.title('Box Plot')\n", "\n", "# Violin plot\n", "plt.subplot(1, 2, 2)\n", "sns.violinplot(x='day', y='total_bill', data=tips)\n", "plt.title('Violin Plot')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Requests example\n", "print(\"\\n=== Requests Example ===\")\n", "# Make a GET request\n", "response = requests.get('https://api.github.com/users/python')\n", "if response.status_code == 200:\n", "    data = response.json()\n", "    print(\"GitHub User Data:\")\n", "    print(f\"Username: {data['login']}\")\n", "    print(f\"Followers: {data['followers']}\")\n", "    print(f\"Public repos: {data['public_repos']}\")\n", "\n", "# Pillow (PIL) example\n", "print(\"\\n=== Pillow Example ===\")\n", "# Create a simple image\n", "img = Image.new('RGB', (100, 100), color='red')\n", "img_array = np.array(img)\n", "print(f\"Image shape: {img_array.shape}\")\n", "print(f\"Image mode: {img.mode}\")\n", "\n", "# Save and load image\n", "img.save('test_image.png')\n", "loaded_img = Image.open('test_image.png')\n", "print(f\"Loaded image size: {loaded_img.size}\")\n", "\n", "# YAML example\n", "print(\"\\n=== YAML Example ===\")\n", "# Create sample configuration\n", "config = {\n", "    'database': {\n", "        'host': 'localhost',\n", "        'port': 5432,\n", "        'name': 'mydb'\n", "    },\n", "    'api': {\n", "        'url': 'https://api.example.com',\n", "        'key': 'abc123'\n", "    }\n", "}\n", "\n", "# Write YAML file\n", "with open('config.yaml', 'w') as f:\n", "    yaml.dump(config, f, default_flow_style=False)\n", "\n", "# Read YAML file\n", "with open('config.yaml', 'r') as f:\n", "    loaded_config = yaml.safe_load(f)\n", "\n", "print(\"Loaded YAML configuration:\")\n", "print(yaml.dump(loaded_config, default_flow_style=False))\n", "\n", "# tqdm example\n", "print(\"\\n=== tqdm Example ===\")\n", "print(\"Processing with progress bar:\")\n", "for i in tqdm(range(10)):\n", "    time.sleep(0.1)  # Simulate work\n", "\n", "# Clean up\n", "print(\"\\n=== Cleaning Up ===\")\n", "import os\n", "files_to_remove = ['test_image.png', 'config.yaml']\n", "for file in files_to_remove:\n", "    if os.path.exists(file):\n", "        os.remove(file)\n", "        print(f\"Removed {file}\")\n", "\n", "# Additional libraries overview\n", "print(\"\\n=== Other Popular Libraries ===\")\n", "libraries = {\n", "    'Web Frameworks': ['Django', 'FastAPI', 'Flask'],\n", "    'Data Science': ['<PERSON><PERSON><PERSON><PERSON>', 'Scikit-learn', 'TensorFlow', 'PyTorch'],\n", "    'Database': ['SQLAlchemy', 'psycopg2', 'pymongo'],\n", "    'Testing': ['pytest', 'unittest', 'nose'],\n", "    'Automation': ['<PERSON><PERSON><PERSON>', '<PERSON>rap<PERSON>', 'Beautiful Soup'],\n", "    'GUI': ['tkinter', 'PyQt', 'wxPython'],\n", "    'Networking': ['socket', 'paramiko', 'twisted'],\n", "    'Security': ['cryptography', 'pycrypto', 'pyOpenSSL']\n", "}\n", "\n", "for category, libs in libraries.items():\n", "    print(f\"\\n{category}:\")\n", "    for lib in libs:\n", "        print(f\"  - {lib}\")"]}, {"cell_type": "markdown", "id": "8f9a2b3c", "metadata": {}, "source": ["# Data Science Basics {#data-science}\n", "\n", "Python is the leading language for data science, offering powerful libraries for data manipulation, analysis, and visualization. This section covers essential data science workflows and techniques."]}, {"cell_type": "code", "execution_count": null, "id": "d4e5f6a7", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "from sklearn.datasets import make_regression\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Data Creation and Manipulation\n", "print(\"=== Data Creation and Manipulation ===\")\n", "\n", "# Create sample dataset\n", "np.random.seed(42)\n", "dates = pd.date_range('2023-01-01', periods=100, freq='D')\n", "data = {\n", "    'date': dates,\n", "    'sales': np.random.normal(1000, 200, 100) + np.sin(np.arange(100) * 0.1) * 100,\n", "    'temperature': np.random.normal(20, 10, 100),\n", "    'region': np.random.choice(['North', 'South', 'East', 'West'], 100),\n", "    'product': np.random.choice(['A', 'B', 'C'], 100)\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "df['sales'] = df['sales'].round(2)\n", "df['temperature'] = df['temperature'].round(1)\n", "\n", "print(\"Dataset overview:\")\n", "print(df.head())\n", "print(f\"\\nDataset shape: {df.shape}\")\n", "print(f\"\\nData types:\\n{df.dtypes}\")\n", "\n", "# Data exploration\n", "print(\"\\n=== Data Exploration ===\")\n", "print(\"\\nDescriptive statistics:\")\n", "print(df.describe())\n", "\n", "print(\"\\nMissing values:\")\n", "print(df.isnull().sum())\n", "\n", "print(\"\\nValue counts for categorical variables:\")\n", "print(\"\\nRegion distribution:\")\n", "print(df['region'].value_counts())\n", "print(\"\\nProduct distribution:\")\n", "print(df['product'].value_counts())\n", "\n", "# Data aggregation and grouping\n", "print(\"\\n=== Data Aggregation ===\")\n", "# Group by region\n", "region_stats = df.groupby('region').agg({\n", "    'sales': ['mean', 'sum', 'count'],\n", "    'temperature': ['mean', 'std']\n", "})\n", "print(\"\\nSales and temperature by region:\")\n", "print(region_stats)\n", "\n", "# Pivot table\n", "pivot_table = df.pivot_table(\n", "    values='sales',\n", "    index='region',\n", "    columns='product',\n", "    aggfunc='mean'\n", ")\n", "print(\"\\nPivot table - Average sales by region and product:\")\n", "print(pivot_table)\n", "\n", "# Data visualization\n", "print(\"\\n=== Data Visualization ===\")\n", "plt.figure(figsize=(15, 10))\n", "\n", "# Time series plot\n", "plt.subplot(2, 3, 1)\n", "plt.plot(df['date'], df['sales'])\n", "plt.title('Sales Over Time')\n", "plt.xlabel('Date')\n", "plt.ylabel('Sales')\n", "plt.xticks(rotation=45)\n", "\n", "# Histogram\n", "plt.subplot(2, 3, 2)\n", "plt.hist(df['sales'], bins=20, alpha=0.7)\n", "plt.title('Sales Distribution')\n", "plt.xlabel('Sales')\n", "plt.ylabel('Frequency')\n", "\n", "# Box plot\n", "plt.subplot(2, 3, 3)\n", "df.boxplot(column='sales', by='region', ax=plt.gca())\n", "plt.title('Sales by Region')\n", "plt.suptitle('')  # Remove default title\n", "\n", "# Scatter plot\n", "plt.subplot(2, 3, 4)\n", "plt.scatter(df['temperature'], df['sales'], alpha=0.6)\n", "plt.title('Sales vs Temperature')\n", "plt.xlabel('Temperature')\n", "plt.ylabel('Sales')\n", "\n", "# Bar plot\n", "plt.subplot(2, 3, 5)\n", "region_means = df.groupby('region')['sales'].mean()\n", "region_means.plot(kind='bar')\n", "plt.title('Average Sales by Region')\n", "plt.ylabel('Average Sales')\n", "plt.xticks(rotation=45)\n", "\n", "# Correlation heatmap\n", "plt.subplot(2, 3, 6)\n", "numeric_cols = df.select_dtypes(include=[np.number])\n", "correlation_matrix = numeric_cols.corr()\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)\n", "plt.title('Correlation Matrix')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Basic machine learning\n", "print(\"\\n=== Basic Machine Learning ===\")\n", "\n", "# Prepare data for modeling\n", "# Create dummy variables for categorical features\n", "df_encoded = pd.get_dummies(df, columns=['region', 'product'], prefix=['region', 'product'])\n", "\n", "# Select features and target\n", "feature_cols = [col for col in df_encoded.columns if col not in ['date', 'sales']]\n", "X = df_encoded[feature_cols]\n", "y = df_encoded['sales']\n", "\n", "print(f\"Features: {feature_cols}\")\n", "print(f\"Feature matrix shape: {X.shape}\")\n", "print(f\"Target vector shape: {y.shape}\")\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"\\nTraining set size: {X_train.shape[0]}\")\n", "print(f\"Test set size: {X_test.shape[0]}\")\n", "\n", "# Train model\n", "model = LinearRegression()\n", "model.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "y_pred = model.predict(X_test)\n", "\n", "# Evaluate model\n", "mse = mean_squared_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(f\"\\nModel Performance:\")\n", "print(f\"Mean Squared Error: {mse:.2f}\")\n", "print(f\"R² Score: {r2:.3f}\")\n", "\n", "# Feature importance\n", "feature_importance = pd.DataFrame({\n", "    'feature': feature_cols,\n", "    'coefficient': model.coef_\n", "})\n", "feature_importance = feature_importance.sort_values('coefficient', key=abs, ascending=False)\n", "print(\"\\nFeature importance (coefficients):\")\n", "print(feature_importance)\n", "\n", "# Prediction vs actual plot\n", "plt.figure(figsize=(8, 6))\n", "plt.scatter(y_test, y_pred, alpha=0.6)\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "plt.xlabel('Actual Sales')\n", "plt.ylabel('Predicted Sales')\n", "plt.title('Actual vs Predicted Sales')\n", "plt.show()\n", "\n", "# Data cleaning example\n", "print(\"\\n=== Data Cleaning Example ===\")\n", "\n", "# Create data with issues\n", "dirty_data = pd.DataFrame({\n", "    'name': ['<PERSON>', '<PERSON>', '<PERSON>', 'alice', '<PERSON><PERSON><PERSON>', <PERSON>],\n", "    'age': [25, 30, None, 25, 30, 35],\n", "    'salary': [50000, 60000, 70000, 50000, '60k', 80000],\n", "    'email': ['<EMAIL>', 'bob@email', '<EMAIL>', \n", "              '<EMAIL>', '<EMAIL>', 'invalid-email']\n", "})\n", "\n", "print(\"Original dirty data:\")\n", "print(dirty_data)\n", "\n", "# Clean the data\n", "cleaned_data = dirty_data.copy()\n", "\n", "# Handle missing values\n", "cleaned_data['name'].fillna('Unknown', inplace=True)\n", "cleaned_data['age'].fillna(cleaned_data['age'].median(), inplace=True)\n", "\n", "# Standardize text\n", "cleaned_data['name'] = cleaned_data['name'].str.title()\n", "\n", "# Clean salary column\n", "cleaned_data['salary'] = cleaned_data['salary'].astype(str).str.replace('k', '000')\n", "cleaned_data['salary'] = pd.to_numeric(cleaned_data['salary'], errors='coerce')\n", "\n", "# Remove duplicates\n", "cleaned_data = cleaned_data.drop_duplicates(subset=['name', 'age'])\n", "\n", "# Validate email format\n", "import re\n", "email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'\n", "cleaned_data['valid_email'] = cleaned_data['email'].apply(\n", "    lambda x: bool(re.match(email_pattern, str(x))) if pd.notna(x) else False\n", ")\n", "\n", "print(\"\\nCleaned data:\")\n", "print(cleaned_data)\n", "\n", "print(\"\\n=== Data Science Best Practices ===\")\n", "best_practices = \"\"\"\n", "1. Always explore your data first (EDA - Exploratory Data Analysis)\n", "2. <PERSON><PERSON> missing values appropriately\n", "3. Validate data quality and consistency\n", "4. Use appropriate visualization for different data types\n", "5. Split data properly for training and testing\n", "6. Evaluate model performance with appropriate metrics\n", "7. Document your analysis and assumptions\n", "8. Version control your data and code\n", "9. Reproducible analysis with random seeds\n", "10. Consider data privacy and ethical implications\n", "\"\"\"\n", "print(best_practices)"]}, {"cell_type": "markdown", "id": "9e8b4c5d", "metadata": {}, "source": ["# Best Practices {#best-practices}\n", "\n", "Writing clean, maintainable, and efficient Python code requires following established best practices. This section covers coding standards, design patterns, and professional development practices."]}, {"cell_type": "code", "execution_count": null, "id": "a7f9c8e1", "metadata": {}, "outputs": [], "source": ["# PEP 8 Style Guide Examples\n", "print(\"=== PEP 8 Style Guide Examples ===\")\n", "\n", "# Good naming conventions\n", "class UserAccount:  # Class names in PascalCase\n", "    \"\"\"Represents a user account with basic operations.\"\"\"\n", "    \n", "    def __init__(self, user_name, email_address):  # snake_case for variables\n", "        self.user_name = user_name\n", "        self.email_address = email_address\n", "        self._account_balance = 0.0  # Private attribute with underscore\n", "        \n", "    def get_account_balance(self):  # Method names in snake_case\n", "        \"\"\"Return the current account balance.\"\"\"\n", "        return self._account_balance\n", "    \n", "    def deposit_funds(self, amount):\n", "        \"\"\"Deposit funds into the account.\"\"\"\n", "        if amount > 0:\n", "            self._account_balance += amount\n", "            return True\n", "        return False\n", "\n", "# Constants in UPPER_CASE\n", "MAX_RETRY_ATTEMPTS = 3\n", "DEFAULT_TIMEOUT = 30\n", "API_BASE_URL = \"https://api.example.com\"\n", "\n", "# Function with proper spacing and documentation\n", "def calculate_compound_interest(principal, rate, time, compound_frequency=1):\n", "    \"\"\"\n", "    Calculate compound interest.\n", "    \n", "    Args:\n", "        principal (float): Initial amount\n", "        rate (float): Annual interest rate (as decimal)\n", "        time (float): Time in years\n", "        compound_frequency (int): Number of times interest compounds per year\n", "    \n", "    Returns:\n", "        float: Final amount after compound interest\n", "    \n", "    Example:\n", "        >>> calculate_compound_interest(1000, 0.05, 2, 4)\n", "        1104.49\n", "    \"\"\"\n", "    if principal <= 0 or rate < 0 or time < 0:\n", "        raise ValueError(\"Principal must be positive, rate and time non-negative\")\n", "    \n", "    amount = principal * (1 + rate / compound_frequency) ** (compound_frequency * time)\n", "    return round(amount, 2)\n", "\n", "# Demonstrate usage\n", "account = UserAccount(\"john_doe\", \"<EMAIL>\")\n", "account.deposit_funds(1000)\n", "print(f\"Account balance: ${account.get_account_balance()}\")\n", "\n", "interest = calculate_compound_interest(1000, 0.05, 2, 4)\n", "print(f\"Compound interest result: ${interest}\")\n", "\n", "# Error handling best practices\n", "print(\"\\n=== Error Handling Best Practices ===\")\n", "\n", "def safe_divide(numerator, denominator):\n", "    \"\"\"\n", "    Safely divide two numbers with proper error handling.\n", "    \n", "    Args:\n", "        numerator (float): Number to be divided\n", "        denominator (float): Number to divide by\n", "    \n", "    Returns:\n", "        float: Result of division\n", "    \n", "    Raises:\n", "        TypeError: If inputs are not numbers\n", "        ZeroDivisionError: If denominator is zero\n", "    \"\"\"\n", "    try:\n", "        # Type checking\n", "        if not isinstance(numerator, (int, float)) or not isinstance(denominator, (int, float)):\n", "            raise TypeError(\"Both arguments must be numbers\")\n", "        \n", "        # Division by zero check\n", "        if denominator == 0:\n", "            raise ZeroDivisionError(\"Cannot divide by zero\")\n", "        \n", "        return numerator / denominator\n", "    \n", "    except (<PERSON><PERSON><PERSON><PERSON>, ZeroDivisionError) as e:\n", "        print(f\"Error in safe_divide: {e}\")\n", "        raise  # Re-raise the exception for caller to handle\n", "\n", "# Test error handling\n", "try:\n", "    result = safe_divide(10, 2)\n", "    print(f\"10 / 2 = {result}\")\n", "    \n", "    result = safe_divide(10, 0)  # This will raise an exception\n", "except ZeroDivisionError:\n", "    print(\"Handled division by zero error\")\n", "\n", "# Context managers for resource management\n", "print(\"\\n=== Resource Management Best Practices ===\")\n", "\n", "from contextlib import contextmanager\n", "import time\n", "\n", "@contextmanager\n", "def timer_context(operation_name):\n", "    \"\"\"Context manager to time operations.\"\"\"\n", "    start_time = time.time()\n", "    print(f\"Starting {operation_name}...\")\n", "    try:\n", "        yield\n", "    finally:\n", "        end_time = time.time()\n", "        print(f\"{operation_name} completed in {end_time - start_time:.4f} seconds\")\n", "\n", "# Using the context manager\n", "with timer_context(\"data processing\"):\n", "    # Simulate some work\n", "    data = [i**2 for i in range(10000)]\n", "    total = sum(data)\n", "    print(f\"Processed {len(data)} items, sum: {total}\")\n", "\n", "# Configuration management\n", "print(\"\\n=== Configuration Management ===\")\n", "\n", "import os\n", "from dataclasses import dataclass\n", "from typing import Optional\n", "\n", "@dataclass\n", "class AppConfig:\n", "    \"\"\"Application configuration with environment variable support.\"\"\"\n", "    database_url: str\n", "    api_key: str\n", "    debug_mode: bool = False\n", "    max_connections: int = 10\n", "    timeout: float = 30.0\n", "    \n", "    @classmethod\n", "    def from_environment(cls) -> 'AppConfig':\n", "        \"\"\"Create configuration from environment variables.\"\"\"\n", "        return cls(\n", "            database_url=os.getenv('DATABASE_URL', 'sqlite:///default.db'),\n", "            api_key=os.getenv('API_KEY', 'default-key'),\n", "            debug_mode=os.getenv('DEBUG', 'false').lower() == 'true',\n", "            max_connections=int(os.getenv('MAX_CONNECTIONS', '10')),\n", "            timeout=float(os.getenv('TIMEOUT', '30.0'))\n", "        )\n", "    \n", "    def validate(self) -> None:\n", "        \"\"\"Validate configuration values.\"\"\"\n", "        if not self.database_url:\n", "            raise ValueError(\"Database URL is required\")\n", "        if not self.api_key or self.api_key == 'default-key':\n", "            raise ValueError(\"Valid API key is required\")\n", "        if self.max_connections <= 0:\n", "            raise ValueError(\"Max connections must be positive\")\n", "\n", "# Example usage\n", "config = AppConfig.from_environment()\n", "print(f\"Configuration: {config}\")\n", "\n", "# Logging best practices\n", "print(\"\\n=== Logging Best Practices ===\")\n", "\n", "import logging\n", "from functools import wraps\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "def log_function_call(func):\n", "    \"\"\"Decorator to log function calls.\"\"\"\n", "    @wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        logger.info(f\"Calling {func.__name__} with args={args}, kwargs={kwargs}\")\n", "        try:\n", "            result = func(*args, **kwargs)\n", "            logger.info(f\"{func.__name__} completed successfully\")\n", "            return result\n", "        except Exception as e:\n", "            logger.error(f\"{func.__name__} failed with error: {e}\")\n", "            raise\n", "    return wrapper\n", "\n", "@log_function_call\n", "def process_data(data_list):\n", "    \"\"\"Process a list of data items.\"\"\"\n", "    if not data_list:\n", "        raise ValueError(\"Data list cannot be empty\")\n", "    \n", "    processed = [item * 2 for item in data_list if isinstance(item, (int, float))]\n", "    return processed\n", "\n", "# Test logging\n", "try:\n", "    result = process_data([1, 2, 3, 4, 5])\n", "    print(f\"Processed data: {result}\")\n", "except Exception as e:\n", "    print(f\"Error: {e}\")\n", "\n", "# Performance optimization tips\n", "print(\"\\n=== Performance Optimization Examples ===\")\n", "\n", "import timeit\n", "\n", "# List comprehension vs traditional loop\n", "def traditional_loop(n):\n", "    result = []\n", "    for i in range(n):\n", "        if i % 2 == 0:\n", "            result.append(i ** 2)\n", "    return result\n", "\n", "def list_comprehension(n):\n", "    return [i ** 2 for i in range(n) if i % 2 == 0]\n", "\n", "# Time comparison\n", "n = 10000\n", "time_loop = timeit.timeit(lambda: traditional_loop(n), number=100)\n", "time_comp = timeit.timeit(lambda: list_comprehension(n), number=100)\n", "\n", "print(f\"Traditional loop time: {time_loop:.4f} seconds\")\n", "print(f\"List comprehension time: {time_comp:.4f} seconds\")\n", "print(f\"List comprehension is {time_loop/time_comp:.2f}x faster\")\n", "\n", "# Memory-efficient generator\n", "def memory_efficient_processor(data):\n", "    \"\"\"Process data using generator for memory efficiency.\"\"\"\n", "    for item in data:\n", "        if item > 0:\n", "            yield item ** 2\n", "\n", "# Use generator\n", "large_data = range(1000000)\n", "processed_gen = memory_efficient_processor(large_data)\n", "first_10 = [next(processed_gen) for _ in range(10)]\n", "print(f\"First 10 processed items: {first_10}\")\n", "\n", "print(\"\\n=== Python Best Practices Summary ===\")\n", "best_practices_summary = \"\"\"\n", "CODE STYLE:\n", "• Follow PEP 8 style guide\n", "• Use meaningful variable and function names\n", "• Write clear docstrings for functions and classes\n", "• Keep functions small and focused (single responsibility)\n", "• Use type hints for better code documentation\n", "\n", "ERROR HANDLING:\n", "• Use specific exception types\n", "• Handle exceptions at the appropriate level\n", "• Log errors with sufficient context\n", "• Fail fast and fail clearly\n", "• Use context managers for resource management\n", "\n", "PERFORMANCE:\n", "• Use list comprehensions and generator expressions\n", "• Avoid premature optimization\n", "• Profile code to identify bottlenecks\n", "• Use appropriate data structures\n", "• Consider memory usage for large datasets\n", "\n", "TESTING:\n", "• Write unit tests for all functions\n", "• Use descriptive test names\n", "• Test edge cases and error conditions\n", "• Maintain high test coverage\n", "• Use fixtures for test data\n", "\n", "SECURITY:\n", "• Validate all input data\n", "• Use environment variables for secrets\n", "• Sanitize data before database operations\n", "• Keep dependencies updated\n", "• Follow principle of least privilege\n", "\n", "MAINTAINABILITY:\n", "• Use version control effectively\n", "• Write clear commit messages\n", "• Document complex algorithms\n", "• Refactor regularly to reduce technical debt\n", "• Use consistent coding patterns\n", "\"\"\"\n", "print(best_practices_summary)"]}, {"cell_type": "markdown", "id": "f2a8d9e2", "metadata": {}, "source": ["# CLI Tools {#cli}\n", "\n", "Python provides excellent support for creating command-line interface (CLI) tools. This section covers argument parsing, creating professional CLI applications, and distributing command-line tools."]}, {"cell_type": "code", "execution_count": null, "id": "b8f7c9d3", "metadata": {}, "outputs": [], "source": ["import argparse\n", "import sys\n", "import os\n", "from pathlib import Path\n", "import json\n", "import subprocess\n", "from typing import List, Optional\n", "\n", "# Basic argument parsing with arg<PERSON>se\n", "print(\"=== Basic Argument Parsing ===\")\n", "\n", "def create_basic_parser():\n", "    \"\"\"Create a basic argument parser.\"\"\"\n", "    parser = argparse.ArgumentParser(\n", "        description='A sample CLI tool for file operations',\n", "        epilog='Example: python script.py --input file.txt --output result.txt --verbose'\n", "    )\n", "    \n", "    # Positional arguments\n", "    parser.add_argument('command', \n", "                       choices=['count', 'copy', 'analyze'],\n", "                       help='Command to execute')\n", "    \n", "    # Optional arguments\n", "    parser.add_argument('-i', '--input',\n", "                       required=True,\n", "                       help='Input file path')\n", "    \n", "    parser.add_argument('-o', '--output',\n", "                       help='Output file path (optional)')\n", "    \n", "    parser.add_argument('-v', '--verbose',\n", "                       action='store_true',\n", "                       help='Enable verbose output')\n", "    \n", "    parser.add_argument('--format',\n", "                       choices=['json', 'csv', 'txt'],\n", "                       default='txt',\n", "                       help='Output format (default: txt)')\n", "    \n", "    parser.add_argument('--limit',\n", "                       type=int,\n", "                       default=100,\n", "                       help='Maximum number of items to process')\n", "    \n", "    return parser\n", "\n", "# Simulate command line arguments for demonstration\n", "def simulate_cli_args(args_list):\n", "    \"\"\"Simulate command line arguments for testing.\"\"\"\n", "    parser = create_basic_parser()\n", "    try:\n", "        args = parser.parse_args(args_list)\n", "        return args\n", "    except SystemExit:\n", "        return None\n", "\n", "# Test different argument combinations\n", "test_args = [\n", "    ['count', '--input', 'data.txt', '--verbose'],\n", "    ['analyze', '--input', 'data.txt', '--output', 'result.json', '--format', 'json'],\n", "    ['copy', '--input', 'source.txt', '--limit', '50']\n", "]\n", "\n", "for args_list in test_args:\n", "    args = simulate_cli_args(args_list)\n", "    if args:\n", "        print(f\"\\nParsed arguments: {args_list}\")\n", "        print(f\"Command: {args.command}\")\n", "        print(f\"Input: {args.input}\")\n", "        print(f\"Output: {args.output}\")\n", "        print(f\"Verbose: {args.verbose}\")\n", "        print(f\"Format: {args.format}\")\n", "        print(f\"Limit: {args.limit}\")\n", "\n", "# Advanced CLI with subcommands\n", "print(\"\\n=== Advanced CLI with Subcommands ===\")\n", "\n", "def create_advanced_parser():\n", "    \"\"\"Create an advanced parser with subcommands.\"\"\"\n", "    parser = argparse.ArgumentParser(\n", "        prog='mytool',\n", "        description='Advanced CLI tool with multiple commands'\n", "    )\n", "    \n", "    # Global arguments\n", "    parser.add_argument('--config',\n", "                       help='Configuration file path')\n", "    parser.add_argument('--verbose', '-v',\n", "                       action='store_true',\n", "                       help='Enable verbose output')\n", "    \n", "    # Create subparsers\n", "    subparsers = parser.add_subparsers(dest='command', help='Available commands')\n", "    \n", "    # File command\n", "    file_parser = subparsers.add_parser('file', help='File operations')\n", "    file_parser.add_argument('action', choices=['read', 'write', 'delete'])\n", "    file_parser.add_argument('path', help='File path')\n", "    file_parser.add_argument('--encoding', default='utf-8', help='File encoding')\n", "    \n", "    # Database command\n", "    db_parser = subparsers.add_parser('db', help='Database operations')\n", "    db_parser.add_argument('action', choices=['connect', 'query', 'migrate'])\n", "    db_parser.add_argument('--host', default='localhost', help='Database host')\n", "    db_parser.add_argument('--port', type=int, default=5432, help='Database port')\n", "    db_parser.add_argument('--database', required=True, help='Database name')\n", "    \n", "    # API command\n", "    api_parser = subparsers.add_parser('api', help='API operations')\n", "    api_parser.add_argument('method', choices=['GET', 'POST', 'PUT', 'DELETE'])\n", "    api_parser.add_argument('url', help='API endpoint URL')\n", "    api_parser.add_argument('--headers', action='append', help='HTTP headers (key:value)')\n", "    api_parser.add_argument('--data', help='Request data (JSON string)')\n", "    \n", "    return parser\n", "\n", "# Test subcommands\n", "advanced_parser = create_advanced_parser()\n", "test_subcommands = [\n", "    ['--verbose', 'file', 'read', 'data.txt', '--encoding', 'utf-8'],\n", "    ['db', 'connect', '--host', 'localhost', '--database', 'mydb'],\n", "    ['api', 'GET', 'https://api.example.com/users', '--headers', 'Authorization:Bearer token']\n", "]\n", "\n", "for cmd_args in test_subcommands:\n", "    try:\n", "        args = advanced_parser.parse_args(cmd_args)\n", "        print(f\"\\nSubcommand: {cmd_args}\")\n", "        print(f\"Parsed: {vars(args)}\")\n", "    except SystemExit:\n", "        print(f\"Failed to parse: {cmd_args}\")\n", "\n", "# CLI application class\n", "print(\"\\n=== Professional CLI Application ===\")\n", "\n", "class FileAnalyzerCLI:\n", "    \"\"\"Professional CLI application for file analysis.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.parser = self._create_parser()\n", "    \n", "    def _create_parser(self):\n", "        \"\"\"Create the argument parser.\"\"\"\n", "        parser = argparse.ArgumentParser(\n", "            prog='file-analyzer',\n", "            description='Analyze text files and generate reports',\n", "            formatter_class=argparse.RawDescriptionHelpFormatter,\n", "            epilog=\"\"\"\n", "Examples:\n", "  file-analyzer count document.txt\n", "  file-analyzer analyze --output report.json document.txt\n", "  file-analyzer stats --format csv *.txt\n", "            \"\"\"\n", "        )\n", "        \n", "        subparsers = parser.add_subparsers(dest='command', help='Commands')\n", "        \n", "        # Count command\n", "        count_parser = subparsers.add_parser('count', help='Count words, lines, characters')\n", "        count_parser.add_argument('files', nargs='+', help='Files to analyze')\n", "        count_parser.add_argument('--type', choices=['words', 'lines', 'chars'], \n", "                                default='words', help='What to count')\n", "        \n", "        # Analyze command\n", "        analyze_parser = subparsers.add_parser('analyze', help='Detailed analysis')\n", "        analyze_parser.add_argument('files', nargs='+', help='Files to analyze')\n", "        analyze_parser.add_argument('--output', '-o', help='Output file')\n", "        analyze_parser.add_argument('--format', choices=['json', 'yaml', 'txt'],\n", "                                  default='json', help='Output format')\n", "        \n", "        # Stats command\n", "        stats_parser = subparsers.add_parser('stats', help='Statistical analysis')\n", "        stats_parser.add_argument('pattern', help='File pattern (e.g., *.txt)')\n", "        stats_parser.add_argument('--recursive', '-r', action='store_true',\n", "                                help='Search recursively')\n", "        \n", "        return parser\n", "    \n", "    def count_content(self, files, count_type):\n", "        \"\"\"Count words, lines, or characters in files.\"\"\"\n", "        results = {}\n", "        \n", "        for file_path in files:\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                \n", "                if count_type == 'words':\n", "                    count = len(content.split())\n", "                elif count_type == 'lines':\n", "                    count = len(content.splitlines())\n", "                else:  # chars\n", "                    count = len(content)\n", "                \n", "                results[file_path] = count\n", "                \n", "            except FileNotFoundError:\n", "                results[file_path] = f\"Error: File not found\"\n", "            except Exception as e:\n", "                results[file_path] = f\"Error: {e}\"\n", "        \n", "        return results\n", "    \n", "    def analyze_files(self, files, output_format):\n", "        \"\"\"Perform detailed analysis of files.\"\"\"\n", "        analysis = {}\n", "        \n", "        for file_path in files:\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                \n", "                words = content.split()\n", "                lines = content.splitlines()\n", "                \n", "                analysis[file_path] = {\n", "                    'word_count': len(words),\n", "                    'line_count': len(lines),\n", "                    'char_count': len(content),\n", "                    'avg_words_per_line': len(words) / len(lines) if lines else 0,\n", "                    'longest_line': max(len(line) for line in lines) if lines else 0,\n", "                    'file_size': os.path.getsize(file_path)\n", "                }\n", "                \n", "            except Exception as e:\n", "                analysis[file_path] = {'error': str(e)}\n", "        \n", "        return analysis\n", "    \n", "    def run(self, args_list=None):\n", "        \"\"\"Run the CLI application.\"\"\"\n", "        try:\n", "            args = self.parser.parse_args(args_list)\n", "            \n", "            if not args.command:\n", "                self.parser.print_help()\n", "                return 1\n", "            \n", "            if args.command == 'count':\n", "                results = self.count_content(args.files, args.type)\n", "                print(f\"\\n{args.type.title()} count results:\")\n", "                for file_path, count in results.items():\n", "                    print(f\"  {file_path}: {count}\")\n", "            \n", "            elif args.command == 'analyze':\n", "                results = self.analyze_files(args.files, args.format)\n", "                \n", "                if args.output:\n", "                    with open(args.output, 'w') as f:\n", "                        if args.format == 'json':\n", "                            json.dump(results, f, indent=2)\n", "                        else:\n", "                            f.write(str(results))\n", "                    print(f\"Analysis saved to {args.output}\")\n", "                else:\n", "                    print(\"\\nFile Analysis Results:\")\n", "                    for file_path, analysis in results.items():\n", "                        print(f\"\\n{file_path}:\")\n", "                        for key, value in analysis.items():\n", "                            print(f\"  {key}: {value}\")\n", "            \n", "            elif args.command == 'stats':\n", "                print(f\"Statistical analysis for pattern: {args.pattern}\")\n", "                print(f\"Recursive: {args.recursive}\")\n", "            \n", "            return 0\n", "            \n", "        except KeyboardInterrupt:\n", "            print(\"\\nOperation cancelled by user\")\n", "            return 1\n", "        except Exception as e:\n", "            print(f\"Error: {e}\")\n", "            return 1\n", "\n", "# Create test files for demonstration\n", "test_content = \"\"\"\n", "This is a sample text file for testing.\n", "It contains multiple lines of text.\n", "We can use this to test our CLI tool.\n", "Python makes it easy to create powerful command-line tools.\n", "\"\"\"\n", "\n", "with open('sample.txt', 'w') as f:\n", "    f.write(test_content.strip())\n", "\n", "# Test the CLI application\n", "cli = FileAnalyzerCLI()\n", "\n", "# Test count command\n", "print(\"Testing count command:\")\n", "cli.run(['count', 'sample.txt', '--type', 'words'])\n", "\n", "# Test analyze command\n", "print(\"\\nTesting analyze command:\")\n", "cli.run(['analyze', 'sample.txt'])\n", "\n", "# Environment variable handling\n", "print(\"\\n=== Environment Variables in CLI ===\")\n", "\n", "def get_config_from_env():\n", "    \"\"\"Get configuration from environment variables.\"\"\"\n", "    config = {\n", "        'api_key': os.getenv('API_KEY', 'default-key'),\n", "        'debug': os.getenv('DEBUG', 'false').lower() == 'true',\n", "        'log_level': os.getenv('LOG_LEVEL', 'INFO'),\n", "        'max_retries': int(os.getenv('MAX_RETRIES', '3')),\n", "        'timeout': float(os.getenv('TIMEOUT', '30.0'))\n", "    }\n", "    return config\n", "\n", "# Set some environment variables for testing\n", "os.environ['DEBUG'] = 'true'\n", "os.environ['LOG_LEVEL'] = 'DEBUG'\n", "os.environ['MAX_RETRIES'] = '5'\n", "\n", "config = get_config_from_env()\n", "print(\"Configuration from environment:\")\n", "for key, value in config.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# Exit codes and error handling\n", "print(\"\\n=== Exit Codes and Error Handling ===\")\n", "\n", "class CLIError(Exception):\n", "    \"\"\"Custom exception for CLI errors.\"\"\"\n", "    def __init__(self, message, exit_code=1):\n", "        super().__init__(message)\n", "        self.exit_code = exit_code\n", "\n", "def safe_cli_operation(operation_name, risky_operation):\n", "    \"\"\"Safely execute a CLI operation with proper error handling.\"\"\"\n", "    try:\n", "        print(f\"Starting {operation_name}...\")\n", "        result = risky_operation()\n", "        print(f\"{operation_name} completed successfully\")\n", "        return result\n", "    \n", "    except FileNotFoundError as e:\n", "        raise CLIError(f\"File not found: {e}\", exit_code=2)\n", "    except PermissionError as e:\n", "        raise CLIError(f\"Permission denied: {e}\", exit_code=3)\n", "    except ValueError as e:\n", "        raise CLIError(f\"Invalid value: {e}\", exit_code=4)\n", "    except Exception as e:\n", "        raise CLIError(f\"Unexpected error: {e}\", exit_code=1)\n", "\n", "# Test error handling\n", "def test_operation():\n", "    # This will succeed\n", "    return \"Operation successful\"\n", "\n", "try:\n", "    result = safe_cli_operation(\"test operation\", test_operation)\n", "    print(f\"Result: {result}\")\n", "except CLIE<PERSON>r as e:\n", "    print(f\"CLI Error (exit code {e.exit_code}): {e}\")\n", "\n", "# Clean up test files\n", "if os.path.exists('sample.txt'):\n", "    os.remove('sample.txt')\n", "\n", "print(\"\\n=== CLI Development Best Practices ===\")\n", "cli_best_practices = \"\"\"\n", "ARGUMENT PARSING:\n", "• Use argparse for robust argument handling\n", "• Provide clear help messages and examples\n", "• Use subcommands for complex tools\n", "• Validate arguments early\n", "• Support both short and long option names\n", "\n", "USER EXPERIENCE:\n", "• Provide meaningful error messages\n", "• Use progress bars for long operations\n", "• Support --verbose and --quiet modes\n", "• Follow Unix conventions (exit codes, stdin/stdout)\n", "• Make the tool discoverable with good help text\n", "\n", "CONFIGURATION:\n", "• Support configuration files\n", "• Use environment variables for settings\n", "• Allow command-line overrides\n", "• Provide sensible defaults\n", "• Document all configuration options\n", "\n", "ERROR HANDLING:\n", "• Use appropriate exit codes (0=success, >0=error)\n", "• Handle KeyboardInterrupt gracefully\n", "• Log errors to stderr\n", "• Provide actionable error messages\n", "• Fail fast on invalid input\n", "\n", "DISTRIBUTION:\n", "• Use setup.py or pyproject.toml for packaging\n", "• Define console_scripts entry points\n", "• Include comprehensive documentation\n", "• Test on multiple platforms\n", "• Version your CLI tool properly\n", "\"\"\"\n", "print(cli_best_practices)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}