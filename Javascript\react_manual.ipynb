{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "c47163a8",
   "metadata": {},
   "source": [
    "# Comprehensive React Programming Manual\n",
    "\n",
    "This manual serves as a complete reference for React development, from basic components to advanced patterns. React is a JavaScript library for building user interfaces with a component-based architecture.\n",
    "\n",
    "## Table of Contents\n",
    "1. [React Fundamentals](#fundamentals)\n",
    "2. [JSX and Components](#jsx)\n",
    "3. [Props and State](#props-state)\n",
    "4. [Event Handling](#events)\n",
    "5. [Hooks](#hooks)\n",
    "6. [Component Lifecycle](#lifecycle)\n",
    "7. [State Management](#state-management)\n",
    "8. [Routing](#routing)\n",
    "9. [Forms and Validation](#forms)\n",
    "10. [Performance Optimization](#performance)\n",
    "11. [Testing](#testing)\n",
    "12. [Best Practices](#best-practices)"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "73fefecc",
   "metadata": {},
   "source": [
    "# React Fundamentals {#fundamentals}\n",
    "\n",
    "React is a declarative, efficient, and flexible JavaScript library for building user interfaces. It uses a component-based architecture and virtual DOM for optimal performance."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e84d17fb",
   "metadata": {},
   "outputs": [],
   "source": [
    "// React Fundamentals\n",
    "// Note: This shows React concepts in JavaScript since we can't run JSX directly here\n",
    "\n",
    "console.log('=== React Fundamentals ===');\n",
    "\n",
    "// React Core Concepts\n",
    "console.log('React Core Concepts:');\n",
    "console.log('1. Component-Based Architecture');\n",
    "console.log('2. Virtual DOM for performance');\n",
    "console.log('3. Unidirectional data flow');\n",
    "console.log('4. Declarative programming style');\n",
    "console.log('5. Reusable UI components');\n",
    "\n",
    "/*\n",
    "// Basic React setup\n",
    "import React from 'react';\n",
    "import ReactDOM from 'react-dom/client';\n",
    "\n",
    "// Simple functional component\n",
    "function Welcome(props) {\n",
    "  return <h1>Hello, {props.name}!</h1>;\n",
    "}\n",
    "\n",
    "// Class component (legacy but still used)\n",
    "class WelcomeClass extends React.Component {\n",
    "  render() {\n",
    "    return <h1>Hello, {this.props.name}!</h1>;\n",
    "  }\n",
    "}\n",
    "\n",
    "// Rendering components\n",
    "const root = ReactDOM.createRoot(document.getElementById('root'));\n",
    "root.render(<Welcome name=\"Alice\" />);\n",
    "*/\n",
    "\n",
    "// Virtual DOM concept simulation\n",
    "console.log('\\n=== Virtual DOM Concept ===');\n",
    "\n",
    "// Simulating virtual DOM elements\n",
    "function createElement(type, props, ...children) {\n",
    "    return {\n",
    "        type,\n",
    "        props: {\n",
    "            ...props,\n",
    "            children: children.length === 1 ? children[0] : children\n",
    "        }\n",
    "    };\n",
    "}\n",
    "\n",
    "// Virtual DOM representation\n",
    "const virtualElement = createElement(\n",
    "    'div',\n",
    "    { className: 'container' },\n",
    "    createElement('h1', null, 'Hello World'),\n",
    "    createElement('p', null, 'This is a paragraph')\n",
    ");\n",
    "\n",
    "console.log('Virtual DOM element:', JSON.stringify(virtualElement, null, 2));\n",
    "\n",
    "// Component concept simulation\n",
    "console.log('\\n=== Component Concept ===');\n",
    "\n",
    "// Simulating React component behavior\n",
    "class Component {\n",
    "    constructor(props) {\n",
    "        this.props = props;\n",
    "        this.state = {};\n",
    "    }\n",
    "    \n",
    "    setState(newState) {\n",
    "        this.state = { ...this.state, ...newState };\n",
    "        console.log('State updated:', this.state);\n",
    "        // In real React, this would trigger re-render\n",
    "    }\n",
    "    \n",
    "    render() {\n",
    "        // Override in subclasses\n",
    "        return null;\n",
    "    }\n",
    "}\n",
    "\n",
    "// Example component\n",
    "class Counter extends Component {\n",
    "    constructor(props) {\n",
    "        super(props);\n",
    "        this.state = { count: 0 };\n",
    "    }\n",
    "    \n",
    "    increment() {\n",
    "        this.setState({ count: this.state.count + 1 });\n",
    "    }\n",
    "    \n",
    "    render() {\n",
    "        return {\n",
    "            type: 'div',\n",
    "            props: {\n",
    "                children: [\n",
    "                    { type: 'p', props: { children: `Count: ${this.state.count}` } },\n",
    "                    { type: 'button', props: { onClick: () => this.increment(), children: 'Increment' } }\n",
    "                ]\n",
    "            }\n",
    "        };\n",
    "    }\n",
    "}\n",
    "\n",
    "// Test component\n",
    "const counter = new Counter({ initialCount: 0 });\n",
    "console.log('Initial render:', JSON.stringify(counter.render(), null, 2));\n",
    "\n",
    "// Simulate button click\n",
    "counter.increment();\n",
    "console.log('After increment:', JSON.stringify(counter.render(), null, 2));\n",
    "\n",
    "// React ecosystem\n",
    "console.log('\\n=== React Ecosystem ===');\n",
    "console.log('Core Libraries:');\n",
    "console.log('- React: Core library for components');\n",
    "console.log('- ReactDOM: DOM rendering');\n",
    "console.log('- React Router: Client-side routing');\n",
    "console.log('- Redux/Zustand: State management');\n",
    "console.log('- React Query: Data fetching');\n",
    "console.log('- Styled Components: CSS-in-JS');\n",
    "\n",
    "console.log('\\nDevelopment Tools:');\n",
    "console.log('- Create React App: Project scaffolding');\n",
    "console.log('- Vite: Fast build tool');\n",
    "console.log('- React DevTools: Browser extension');\n",
    "console.log('- Storybook: Component development');\n",
    "console.log('- Jest + React Testing Library: Testing');\n",
    "\n",
    "// React principles\n",
    "console.log('\\n=== React Principles ===');\n",
    "console.log('1. Components should be pure functions when possible');\n",
    "console.log('2. Props flow down, events flow up');\n",
    "console.log('3. State should be lifted up when shared');\n",
    "console.log('4. Use keys for list items');\n",
    "console.log('5. Avoid direct DOM manipulation');\n",
    "console.log('6. Think in React: break UI into components');\n",
    "\n",
    "// Modern React features\n",
    "console.log('\\n=== Modern React Features ===');\n",
    "console.log('- Hooks (useState, useEffect, useContext, etc.)');\n",
    "console.log('- Function components (preferred over classes)');\n",
    "console.log('- Concurrent features (Suspense, transitions)');\n",
    "console.log('- Server Components (React 18+)');\n",
    "console.log('- Automatic batching');\n",
    "console.log('- Strict Mode for development');\n",
    "\n",
    "console.log('\\nReact provides a powerful foundation for building modern web applications!');"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "71c71fe2",
   "metadata": {},
   "source": [
    "# JSX and Components {#jsx}\n",
    "\n",
    "JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files. Components are the building blocks of React applications."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "ff42471f",
   "metadata": {},
   "outputs": [],
   "source": [
    "// JSX and Components\n",
    "console.log('=== JSX and Components ===');\n",
    "\n",
    "/*\n",
    "// JSX Syntax Examples\n",
    "\n",
    "// Basic JSX\n",
    "const element = <h1>Hello, World!</h1>;\n",
    "\n",
    "// JSX with expressions\n",
    "const name = 'Alice';\n",
    "const greeting = <h1>Hello, {name}!</h1>;\n",
    "\n",
    "// JSX with attributes\n",
    "const image = <img src=\"photo.jpg\" alt=\"Profile\" className=\"profile-pic\" />;\n",
    "\n",
    "// JSX with children\n",
    "const card = (\n",
    "  <div className=\"card\">\n",
    "    <h2>Card Title</h2>\n",
    "    <p>Card content goes here.</p>\n",
    "    <button onClick={handleClick}>Click me</button>\n",
    "  </div>\n",
    ");\n",
    "\n",
    "// Conditional rendering\n",
    "const conditionalElement = (\n",
    "  <div>\n",
    "    {isLoggedIn ? <WelcomeMessage /> : <LoginForm />}\n",
    "    {showAlert && <Alert message=\"Important notice!\" />}\n",
    "  </div>\n",
    ");\n",
    "\n",
    "// Lists and keys\n",
    "const items = ['apple', 'banana', 'cherry'];\n",
    "const listItems = (\n",
    "  <ul>\n",
    "    {items.map((item, index) => (\n",
    "      <li key={index}>{item}</li>\n",
    "    ))}\n",
    "  </ul>\n",
    ");\n",
    "\n",
    "// Functional Components\n",
    "function Welcome(props) {\n",
    "  return <h1>Hello, {props.name}!</h1>;\n",
    "}\n",
    "\n",
    "// Arrow function component\n",
    "const Greeting = ({ name, age }) => {\n",
    "  return (\n",
    "    <div>\n",
    "      <h2>Hello, {name}!</h2>\n",
    "      {age && <p>You are {age} years old.</p>}\n",
    "    </div>\n",
    "  );\n",
    "};\n",
    "\n",
    "// Component with default props\n",
    "function Button({ children, variant = 'primary', onClick }) {\n",
    "  return (\n",
    "    <button \n",
    "      className={`btn btn-${variant}`}\n",
    "      onClick={onClick}\n",
    "    >\n",
    "      {children}\n",
    "    </button>\n",
    "  );\n",
    "}\n",
    "\n",
    "// Component composition\n",
    "function Card({ title, children }) {\n",
    "  return (\n",
    "    <div className=\"card\">\n",
    "      <div className=\"card-header\">\n",
    "        <h3>{title}</h3>\n",
    "      </div>\n",
    "      <div className=\"card-body\">\n",
    "        {children}\n",
    "      </div>\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "\n",
    "// Using composed components\n",
    "function App() {\n",
    "  return (\n",
    "    <div>\n",
    "      <Card title=\"User Profile\">\n",
    "        <Greeting name=\"Alice\" age={30} />\n",
    "        <Button onClick={() => alert('Profile updated!')}>\n",
    "          Update Profile\n",
    "        </Button>\n",
    "      </Card>\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "*/\n",
    "\n",
    "// JSX compilation simulation\n",
    "console.log('JSX compilation process:');\n",
    "console.log('1. JSX syntax: <div>Hello</div>');\n",
    "console.log('2. Compiles to: React.createElement(\"div\", null, \"Hello\")');\n",
    "console.log('3. Creates virtual DOM object');\n",
    "\n",
    "// Simulating JSX to JavaScript transformation\n",
    "function React_createElement(type, props, ...children) {\n",
    "    return {\n",
    "        type,\n",
    "        props: {\n",
    "            ...props,\n",
    "            children: children.length === 1 ? children[0] : children\n",
    "        }\n",
    "    };\n",
    "}\n",
    "\n",
    "// JSX: <div className=\"container\">Hello World</div>\n",
    "// Compiles to:\n",
    "const jsxElement = React_createElement('div', { className: 'container' }, 'Hello World');\n",
    "console.log('\\nJSX compilation result:', jsxElement);\n",
    "\n",
    "// Component patterns simulation\n",
    "console.log('\\n=== Component Patterns ===');\n",
    "\n",
    "// Functional component simulation\n",
    "function createFunctionalComponent(name, renderFn) {\n",
    "    return {\n",
    "        name,\n",
    "        type: 'functional',\n",
    "        render: renderFn\n",
    "    };\n",
    "}\n",
    "\n",
    "// Simulated functional components\n",
    "const WelcomeComponent = createFunctionalComponent('Welcome', (props) => {\n",
    "    return {\n",
    "        type: 'h1',\n",
    "        props: {\n",
    "            children: `Hello, ${props.name}!`\n",
    "        }\n",
    "    };\n",
    "});\n",
    "\n",
    "const ButtonComponent = createFunctionalComponent('Button', (props) => {\n",
    "    return {\n",
    "        type: 'button',\n",
    "        props: {\n",
    "            className: `btn btn-${props.variant || 'primary'}`,\n",
    "            onClick: props.onClick,\n",
    "            children: props.children\n",
    "        }\n",
    "    };\n",
    "});\n",
    "\n",
    "// Test components\n",
    "console.log('Welcome component:', WelcomeComponent.render({ name: 'Alice' }));\n",
    "console.log('Button component:', ButtonComponent.render({ \n",
    "    variant: 'secondary', \n",
    "    children: 'Click me',\n",
    "    onClick: () => console.log('Button clicked!')\n",
    "}));\n",
    "\n",
    "// Props validation simulation\n",
    "console.log('\\n=== Props Validation ===');\n",
    "\n",
    "function validateProps(component, props, propTypes) {\n",
    "    const errors = [];\n",
    "    \n",
    "    for (const [propName, validator] of Object.entries(propTypes)) {\n",
    "        const value = props[propName];\n",
    "        \n",
    "        if (validator.required && (value === undefined || value === null)) {\n",
    "            errors.push(`${propName} is required`);\n",
    "        }\n",
    "        \n",
    "        if (value !== undefined && validator.type && typeof value !== validator.type) {\n",
    "            errors.push(`${propName} should be ${validator.type}, got ${typeof value}`);\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    return errors;\n",
    "}\n",
    "\n",
    "// PropTypes simulation\n",
    "const UserCardPropTypes = {\n",
    "    name: { type: 'string', required: true },\n",
    "    age: { type: 'number', required: false },\n",
    "    email: { type: 'string', required: true }\n",
    "};\n",
    "\n",
    "// Valid props\n",
    "const validProps = { name: 'Alice', age: 30, email: '<EMAIL>' };\n",
    "const validationErrors1 = validateProps('UserCard', validProps, UserCardPropTypes);\n",
    "console.log('Valid props errors:', validationErrors1);\n",
    "\n",
    "// Invalid props\n",
    "const invalidProps = { name: 'Bob', age: '30', email: null };\n",
    "const validationErrors2 = validateProps('UserCard', invalidProps, UserCardPropTypes);\n",
    "console.log('Invalid props errors:', validationErrors2);\n",
    "\n",
    "// Component composition patterns\n",
    "console.log('\\n=== Component Composition ===');\n",
    "\n",
    "// Higher-Order Component (HOC) simulation\n",
    "function withLoading(WrappedComponent) {\n",
    "    return function LoadingWrapper(props) {\n",
    "        if (props.isLoading) {\n",
    "            return { type: 'div', props: { children: 'Loading...' } };\n",
    "        }\n",
    "        return WrappedComponent.render(props);\n",
    "    };\n",
    "}\n",
    "\n",
    "// Render props pattern simulation\n",
    "function DataProvider(props) {\n",
    "    const data = { users: ['Alice', 'Bob', 'Charlie'] };\n",
    "    return props.render(data);\n",
    "}\n",
    "\n",
    "// Test HOC\n",
    "const LoadingWelcome = withLoading(WelcomeComponent);\n",
    "console.log('Loading component (loading):', LoadingWelcome({ isLoading: true, name: 'Alice' }));\n",
    "console.log('Loading component (loaded):', LoadingWelcome({ isLoading: false, name: 'Alice' }));\n",
    "\n",
    "console.log('\\nJSX provides a powerful way to describe UI structure declaratively!');"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "b3ad4049",
   "metadata": {},
   "source": [
    "# Hooks {#hooks}\n",
    "\n",
    "Hooks are functions that let you use state and other React features in functional components. They provide a more direct API to React concepts."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "4adc818d",
   "metadata": {},
   "outputs": [],
   "source": [
    "// React Hooks\n",
    "console.log('=== React Hooks ===');\n",
    "\n",
    "/*\n",
    "// useState Hook\n",
    "import React, { useState } from 'react';\n",
    "\n",
    "function Counter() {\n",
    "  const [count, setCount] = useState(0);\n",
    "  \n",
    "  return (\n",
    "    <div>\n",
    "      <p>Count: {count}</p>\n",
    "      <button onClick={() => setCount(count + 1)}>Increment</button>\n",
    "      <button onClick={() => setCount(count - 1)}>Decrement</button>\n",
    "      <button onClick={() => setCount(0)}>Reset</button>\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "\n",
    "// useEffect Hook\n",
    "import { useEffect } from 'react';\n",
    "\n",
    "function UserProfile({ userId }) {\n",
    "  const [user, setUser] = useState(null);\n",
    "  const [loading, setLoading] = useState(true);\n",
    "  \n",
    "  useEffect(() => {\n",
    "    // Effect runs after render\n",
    "    async function fetchUser() {\n",
    "      setLoading(true);\n",
    "      try {\n",
    "        const response = await fetch(`/api/users/${userId}`);\n",
    "        const userData = await response.json();\n",
    "        setUser(userData);\n",
    "      } catch (error) {\n",
    "        console.error('Failed to fetch user:', error);\n",
    "      } finally {\n",
    "        setLoading(false);\n",
    "      }\n",
    "    }\n",
    "    \n",
    "    fetchUser();\n",
    "    \n",
    "    // Cleanup function (optional)\n",
    "    return () => {\n",
    "      // Cancel requests, clear timers, etc.\n",
    "    };\n",
    "  }, [userId]); // Dependency array\n",
    "  \n",
    "  if (loading) return <div>Loading...</div>;\n",
    "  if (!user) return <div>User not found</div>;\n",
    "  \n",
    "  return (\n",
    "    <div>\n",
    "      <h2>{user.name}</h2>\n",
    "      <p>{user.email}</p>\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "\n",
    "// useContext Hook\n",
    "import { useContext, createContext } from 'react';\n",
    "\n",
    "const ThemeContext = createContext();\n",
    "\n",
    "function ThemeProvider({ children }) {\n",
    "  const [theme, setTheme] = useState('light');\n",
    "  \n",
    "  return (\n",
    "    <ThemeContext.Provider value={{ theme, setTheme }}>\n",
    "      {children}\n",
    "    </ThemeContext.Provider>\n",
    "  );\n",
    "}\n",
    "\n",
    "function ThemedButton() {\n",
    "  const { theme, setTheme } = useContext(ThemeContext);\n",
    "  \n",
    "  return (\n",
    "    <button \n",
    "      className={`btn btn-${theme}`}\n",
    "      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n",
    "    >\n",
    "      Toggle Theme ({theme})\n",
    "    </button>\n",
    "  );\n",
    "}\n",
    "\n",
    "// useReducer Hook\n",
    "import { useReducer } from 'react';\n",
    "\n",
    "const initialState = { count: 0 };\n",
    "\n",
    "function reducer(state, action) {\n",
    "  switch (action.type) {\n",
    "    case 'increment':\n",
    "      return { count: state.count + 1 };\n",
    "    case 'decrement':\n",
    "      return { count: state.count - 1 };\n",
    "    case 'reset':\n",
    "      return initialState;\n",
    "    default:\n",
    "      throw new Error();\n",
    "  }\n",
    "}\n",
    "\n",
    "function CounterWithReducer() {\n",
    "  const [state, dispatch] = useReducer(reducer, initialState);\n",
    "  \n",
    "  return (\n",
    "    <div>\n",
    "      Count: {state.count}\n",
    "      <button onClick={() => dispatch({ type: 'increment' })}>+</button>\n",
    "      <button onClick={() => dispatch({ type: 'decrement' })}>-</button>\n",
    "      <button onClick={() => dispatch({ type: 'reset' })}>Reset</button>\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "\n",
    "// Custom Hooks\n",
    "function useLocalStorage(key, initialValue) {\n",
    "  const [storedValue, setStoredValue] = useState(() => {\n",
    "    try {\n",
    "      const item = window.localStorage.getItem(key);\n",
    "      return item ? JSON.parse(item) : initialValue;\n",
    "    } catch (error) {\n",
    "      return initialValue;\n",
    "    }\n",
    "  });\n",
    "  \n",
    "  const setValue = (value) => {\n",
    "    try {\n",
    "      setStoredValue(value);\n",
    "      window.localStorage.setItem(key, JSON.stringify(value));\n",
    "    } catch (error) {\n",
    "      console.error('Error saving to localStorage:', error);\n",
    "    }\n",
    "  };\n",
    "  \n",
    "  return [storedValue, setValue];\n",
    "}\n",
    "\n",
    "// Using custom hook\n",
    "function Settings() {\n",
    "  const [name, setName] = useLocalStorage('name', '');\n",
    "  const [email, setEmail] = useLocalStorage('email', '');\n",
    "  \n",
    "  return (\n",
    "    <form>\n",
    "      <input \n",
    "        value={name} \n",
    "        onChange={(e) => setName(e.target.value)}\n",
    "        placeholder=\"Name\" \n",
    "      />\n",
    "      <input \n",
    "        value={email} \n",
    "        onChange={(e) => setEmail(e.target.value)}\n",
    "        placeholder=\"Email\" \n",
    "      />\n",
    "    </form>\n",
    "  );\n",
    "}\n",
    "*/\n",
    "\n",
    "// Hooks simulation in JavaScript\n",
    "console.log('Simulating React Hooks behavior:');\n",
    "\n",
    "// useState simulation\n",
    "function createUseState() {\n",
    "    let state = null;\n",
    "    \n",
    "    return function useState(initialValue) {\n",
    "        if (state === null) {\n",
    "            state = initialValue;\n",
    "        }\n",
    "        \n",
    "        function setState(newValue) {\n",
    "            state = typeof newValue === 'function' ? newValue(state) : newValue;\n",
    "            console.log('State updated:', state);\n",
    "            // In real React, this would trigger re-render\n",
    "        }\n",
    "        \n",
    "        return [state, setState];\n",
    "    };\n",
    "}\n",
    "\n",
    "// Test useState simulation\n",
    "const useState = createUseState();\n",
    "const [count, setCount] = useState(0);\n",
    "\n",
    "console.log('\\nTesting useState simulation:');\n",
    "console.log('Initial count:', count);\n",
    "setCount(5);\n",
    "setCount(prev => prev + 1);\n",
    "\n",
    "// useEffect simulation\n",
    "function createUseEffect() {\n",
    "    let dependencies = null;\n",
    "    let cleanup = null;\n",
    "    \n",
    "    return function useEffect(effect, deps) {\n",
    "        // Check if dependencies changed\n",
    "        const depsChanged = !dependencies || \n",
    "            !deps || \n",
    "            deps.length !== dependencies.length ||\n",
    "            deps.some((dep, i) => dep !== dependencies[i]);\n",
    "        \n",
    "        if (depsChanged) {\n",
    "            // Run cleanup from previous effect\n",
    "            if (cleanup) {\n",
    "                cleanup();\n",
    "            }\n",
    "            \n",
    "            // Run new effect\n",
    "            cleanup = effect();\n",
    "            dependencies = deps ? [...deps] : null;\n",
    "        }\n",
    "    };\n",
    "}\n",
    "\n",
    "// Test useEffect simulation\n",
    "const useEffect = createUseEffect();\n",
    "\n",
    "console.log('\\nTesting useEffect simulation:');\n",
    "useEffect(() => {\n",
    "    console.log('Effect ran!');\n",
    "    return () => console.log('Cleanup ran!');\n",
    "}, [1, 2, 3]);\n",
    "\n",
    "// Custom hook simulation\n",
    "function useCounter(initialValue = 0) {\n",
    "    let value = initialValue;\n",
    "    \n",
    "    const increment = () => {\n",
    "        value += 1;\n",
    "        console.log('Counter incremented:', value);\n",
    "        return value;\n",
    "    };\n",
    "    \n",
    "    const decrement = () => {\n",
    "        value -= 1;\n",
    "        console.log('Counter decremented:', value);\n",
    "        return value;\n",
    "    };\n",
    "    \n",
    "    const reset = () => {\n",
    "        value = initialValue;\n",
    "        console.log('Counter reset:', value);\n",
    "        return value;\n",
    "    };\n",
    "    \n",
    "    return { value, increment, decrement, reset };\n",
    "}\n",
    "\n",
    "// Test custom hook\n",
    "console.log('\\nTesting custom hook:');\n",
    "const counter = useCounter(10);\n",
    "console.log('Initial value:', counter.value);\n",
    "counter.increment();\n",
    "counter.increment();\n",
    "counter.decrement();\n",
    "counter.reset();\n",
    "\n",
    "// Hook rules simulation\n",
    "console.log('\\n=== Hook Rules ===');\n",
    "console.log('1. Only call hooks at the top level');\n",
    "console.log('2. Only call hooks from React functions');\n",
    "console.log('3. Hook names should start with \"use\"');\n",
    "console.log('4. Dependencies in useEffect should be exhaustive');\n",
    "\n",
    "// Common hook patterns\n",
    "console.log('\\n=== Common Hook Patterns ===');\n",
    "\n",
    "// Data fetching pattern\n",
    "function useFetch(url) {\n",
    "    let data = null;\n",
    "    let loading = true;\n",
    "    let error = null;\n",
    "    \n",
    "    // Simulate async fetch\n",
    "    setTimeout(() => {\n",
    "        try {\n",
    "            data = { message: `Data from ${url}` };\n",
    "            loading = false;\n",
    "            console.log('Data fetched:', data);\n",
    "        } catch (err) {\n",
    "            error = err;\n",
    "            loading = false;\n",
    "            console.log('Fetch error:', error);\n",
    "        }\n",
    "    }, 1000);\n",
    "    \n",
    "    return { data, loading, error };\n",
    "}\n",
    "\n",
    "// Form handling pattern\n",
    "function useForm(initialValues) {\n",
    "    let values = { ...initialValues };\n",
    "    \n",
    "    const handleChange = (name, value) => {\n",
    "        values[name] = value;\n",
    "        console.log('Form updated:', values);\n",
    "    };\n",
    "    \n",
    "    const reset = () => {\n",
    "        values = { ...initialValues };\n",
    "        console.log('Form reset:', values);\n",
    "    };\n",
    "    \n",
    "    return { values, handleChange, reset };\n",
    "}\n",
    "\n",
    "// Test patterns\n",
    "console.log('Testing useFetch pattern:');\n",
    "const fetchResult = useFetch('/api/users');\n",
    "console.log('Fetch state:', fetchResult);\n",
    "\n",
    "console.log('\\nTesting useForm pattern:');\n",
    "const form = useForm({ name: '', email: '' });\n",
    "form.handleChange('name', 'Alice');\n",
    "form.handleChange('email', '<EMAIL>');\n",
    "form.reset();\n",
    "\n",
    "console.log('\\nHooks provide a powerful way to reuse stateful logic!');"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "fa36a827",
   "metadata": {},
   "source": [
    "# State Management and Performance {#state-management}\n",
    "\n",
    "Managing state effectively and optimizing performance are crucial for building scalable React applications."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "56c306d3",
   "metadata": {},
   "outputs": [],
   "source": [
    "// State Management and Performance\n",
    "console.log('=== State Management and Performance ===');\n",
    "\n",
    "/*\n",
    "// Context API for Global State\n",
    "import React, { createContext, useContext, useReducer } from 'react';\n",
    "\n",
    "// App state context\n",
    "const AppStateContext = createContext();\n",
    "\n",
    "const initialState = {\n",
    "  user: null,\n",
    "  theme: 'light',\n",
    "  notifications: []\n",
    "};\n",
    "\n",
    "function appReducer(state, action) {\n",
    "  switch (action.type) {\n",
    "    case 'SET_USER':\n",
    "      return { ...state, user: action.payload };\n",
    "    case 'SET_THEME':\n",
    "      return { ...state, theme: action.payload };\n",
    "    case 'ADD_NOTIFICATION':\n",
    "      return { \n",
    "        ...state, \n",
    "        notifications: [...state.notifications, action.payload] \n",
    "      };\n",
    "    case 'REMOVE_NOTIFICATION':\n",
    "      return {\n",
    "        ...state,\n",
    "        notifications: state.notifications.filter(n => n.id !== action.payload)\n",
    "      };\n",
    "    default:\n",
    "      return state;\n",
    "  }\n",
    "}\n",
    "\n",
    "function AppProvider({ children }) {\n",
    "  const [state, dispatch] = useReducer(appReducer, initialState);\n",
    "  \n",
    "  return (\n",
    "    <AppStateContext.Provider value={{ state, dispatch }}>\n",
    "      {children}\n",
    "    </AppStateContext.Provider>\n",
    "  );\n",
    "}\n",
    "\n",
    "function useAppState() {\n",
    "  const context = useContext(AppStateContext);\n",
    "  if (!context) {\n",
    "    throw new Error('useAppState must be used within AppProvider');\n",
    "  }\n",
    "  return context;\n",
    "}\n",
    "\n",
    "// Performance Optimization with React.memo\n",
    "import { memo, useMemo, useCallback } from 'react';\n",
    "\n",
    "const ExpensiveComponent = memo(function ExpensiveComponent({ data, onUpdate }) {\n",
    "  console.log('ExpensiveComponent rendered');\n",
    "  \n",
    "  const processedData = useMemo(() => {\n",
    "    // Expensive calculation\n",
    "    return data.map(item => ({ ...item, processed: true }));\n",
    "  }, [data]);\n",
    "  \n",
    "  return (\n",
    "    <div>\n",
    "      {processedData.map(item => (\n",
    "        <div key={item.id}>\n",
    "          {item.name}\n",
    "          <button onClick={() => onUpdate(item.id)}>Update</button>\n",
    "        </div>\n",
    "      ))}\n",
    "    </div>\n",
    "  );\n",
    "});\n",
    "\n",
    "function ParentComponent() {\n",
    "  const [data, setData] = useState([]);\n",
    "  const [count, setCount] = useState(0);\n",
    "  \n",
    "  // useCallback prevents function recreation on every render\n",
    "  const handleUpdate = useCallback((id) => {\n",
    "    setData(prev => prev.map(item => \n",
    "      item.id === id ? { ...item, updated: Date.now() } : item\n",
    "    ));\n",
    "  }, []);\n",
    "  \n",
    "  return (\n",
    "    <div>\n",
    "      <button onClick={() => setCount(c => c + 1)}>Count: {count}</button>\n",
    "      <ExpensiveComponent data={data} onUpdate={handleUpdate} />\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "\n",
    "// Code Splitting with React.lazy\n",
    "import { lazy, Suspense } from 'react';\n",
    "\n",
    "const LazyComponent = lazy(() => import('./LazyComponent'));\n",
    "\n",
    "function App() {\n",
    "  return (\n",
    "    <div>\n",
    "      <Suspense fallback={<div>Loading...</div>}>\n",
    "        <LazyComponent />\n",
    "      </Suspense>\n",
    "    </div>\n",
    "  );\n",
    "}\n",
    "\n",
    "// Error Boundaries\n",
    "class ErrorBoundary extends React.Component {\n",
    "  constructor(props) {\n",
    "    super(props);\n",
    "    this.state = { hasError: false, error: null };\n",
    "  }\n",
    "  \n",
    "  static getDerivedStateFromError(error) {\n",
    "    return { hasError: true, error };\n",
    "  }\n",
    "  \n",
    "  componentDidCatch(error, errorInfo) {\n",
    "    console.error('Error caught by boundary:', error, errorInfo);\n",
    "  }\n",
    "  \n",
    "  render() {\n",
    "    if (this.state.hasError) {\n",
    "      return (\n",
    "        <div>\n",
    "          <h2>Something went wrong.</h2>\n",
    "          <details>\n",
    "            {this.state.error && this.state.error.toString()}\n",
    "          </details>\n",
    "        </div>\n",
    "      );\n",
    "    }\n",
    "    \n",
    "    return this.props.children;\n",
    "  }\n",
    "}\n",
    "*/\n",
    "\n",
    "// State management patterns simulation\n",
    "console.log('State management patterns:');\n",
    "\n",
    "// Redux-like pattern simulation\n",
    "function createStore(reducer, initialState) {\n",
    "    let state = initialState;\n",
    "    let listeners = [];\n",
    "    \n",
    "    return {\n",
    "        getState: () => state,\n",
    "        dispatch: (action) => {\n",
    "            state = reducer(state, action);\n",
    "            listeners.forEach(listener => listener(state));\n",
    "        },\n",
    "        subscribe: (listener) => {\n",
    "            listeners.push(listener);\n",
    "            return () => {\n",
    "                listeners = listeners.filter(l => l !== listener);\n",
    "            };\n",
    "        }\n",
    "    };\n",
    "}\n",
    "\n",
    "// Example reducer\n",
    "function counterReducer(state = { count: 0 }, action) {\n",
    "    switch (action.type) {\n",
    "        case 'INCREMENT':\n",
    "            return { count: state.count + 1 };\n",
    "        case 'DECREMENT':\n",
    "            return { count: state.count - 1 };\n",
    "        case 'RESET':\n",
    "            return { count: 0 };\n",
    "        default:\n",
    "            return state;\n",
    "    }\n",
    "}\n",
    "\n",
    "// Test store\n",
    "const store = createStore(counterReducer, { count: 0 });\n",
    "\n",
    "store.subscribe((state) => {\n",
    "    console.log('State updated:', state);\n",
    "});\n",
    "\n",
    "console.log('\\nTesting Redux-like store:');\n",
    "console.log('Initial state:', store.getState());\n",
    "store.dispatch({ type: 'INCREMENT' });\n",
    "store.dispatch({ type: 'INCREMENT' });\n",
    "store.dispatch({ type: 'DECREMENT' });\n",
    "store.dispatch({ type: 'RESET' });\n",
    "\n",
    "// Performance optimization simulation\n",
    "console.log('\\n=== Performance Optimization ===');\n",
    "\n",
    "// Memoization simulation\n",
    "function createMemo() {\n",
    "    const cache = new Map();\n",
    "    \n",
    "    return function memo(fn, dependencies) {\n",
    "        const key = JSON.stringify(dependencies);\n",
    "        \n",
    "        if (cache.has(key)) {\n",
    "            console.log('Cache hit for:', key);\n",
    "            return cache.get(key);\n",
    "        }\n",
    "        \n",
    "        console.log('Computing for:', key);\n",
    "        const result = fn();\n",
    "        cache.set(key, result);\n",
    "        return result;\n",
    "    };\n",
    "}\n",
    "\n",
    "const memo = createMemo();\n",
    "\n",
    "function expensiveCalculation(numbers) {\n",
    "    return numbers.reduce((sum, num) => sum + num * num, 0);\n",
    "}\n",
    "\n",
    "// Test memoization\n",
    "console.log('\\nTesting memoization:');\n",
    "const result1 = memo(() => expensiveCalculation([1, 2, 3]), [1, 2, 3]);\n",
    "const result2 = memo(() => expensiveCalculation([1, 2, 3]), [1, 2, 3]); // Cache hit\n",
    "const result3 = memo(() => expensiveCalculation([1, 2, 4]), [1, 2, 4]); // New calculation\n",
    "\n",
    "console.log('Results:', result1, result2, result3);\n",
    "\n",
    "// Component optimization patterns\n",
    "console.log('\\n=== Component Optimization ===');\n",
    "\n",
    "// Shallow comparison for props\n",
    "function shallowEqual(obj1, obj2) {\n",
    "    const keys1 = Object.keys(obj1);\n",
    "    const keys2 = Object.keys(obj2);\n",
    "    \n",
    "    if (keys1.length !== keys2.length) {\n",
    "        return false;\n",
    "    }\n",
    "    \n",
    "    for (let key of keys1) {\n",
    "        if (obj1[key] !== obj2[key]) {\n",
    "            return false;\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    return true;\n",
    "}\n",
    "\n",
    "// React.memo simulation\n",
    "function createMemoComponent(Component) {\n",
    "    let lastProps = null;\n",
    "    let lastResult = null;\n",
    "    \n",
    "    return function MemoizedComponent(props) {\n",
    "        if (lastProps === null || !shallowEqual(props, lastProps)) {\n",
    "            console.log('Component re-rendered');\n",
    "            lastResult = Component(props);\n",
    "            lastProps = { ...props };\n",
    "        } else {\n",
    "            console.log('Component skipped re-render (memoized)');\n",
    "        }\n",
    "        \n",
    "        return lastResult;\n",
    "    };\n",
    "}\n",
    "\n",
    "// Test component memoization\n",
    "function UserCard(props) {\n",
    "    return {\n",
    "        type: 'div',\n",
    "        props: {\n",
    "            children: `User: ${props.name}, Age: ${props.age}`\n",
    "        }\n",
    "    };\n",
    "}\n",
    "\n",
    "const MemoizedUserCard = createMemoComponent(UserCard);\n",
    "\n",
    "console.log('\\nTesting component memoization:');\n",
    "MemoizedUserCard({ name: 'Alice', age: 30 });\n",
    "MemoizedUserCard({ name: 'Alice', age: 30 }); // Should skip\n",
    "MemoizedUserCard({ name: 'Bob', age: 25 }); // Should re-render\n",
    "\n",
    "// Best practices\n",
    "console.log('\\n=== Performance Best Practices ===');\n",
    "console.log('1. Use React.memo for expensive components');\n",
    "console.log('2. Use useMemo for expensive calculations');\n",
    "console.log('3. Use useCallback for stable function references');\n",
    "console.log('4. Avoid creating objects/functions in render');\n",
    "console.log('5. Use React.lazy for code splitting');\n",
    "console.log('6. Implement proper key props for lists');\n",
    "console.log('7. Use React DevTools Profiler');\n",
    "console.log('8. Consider virtualization for long lists');\n",
    "\n",
    "console.log('\\nEffective state management and optimization are key to scalable React apps!');"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "JavaScript (Node.js)",
   "language": "javascript",
   "name": "javascript"
  },
  "language_info": {
   "file_extension": ".js",
   "mimetype": "application/javascript",
   "name": "javascript",
   "version": "18.0.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
 "metadata": {
  "kernelspec": {
   "display_name": "JavaScript (Node.js)",
   "language": "javascript",
   "name": "javascript"
  },
  "language_info": {
   "file_extension": ".js",
   "mimetype": "application/javascript",
   "name": "javascript",
   "version": "18.0.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
