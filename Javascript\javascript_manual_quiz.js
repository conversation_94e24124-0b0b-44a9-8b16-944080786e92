/**
 * JavaScript Manual Quiz
 * Comprehensive quiz covering all 21 sections of the JavaScript programming manual
 */

const quizzes = {
  "Basic Syntax and Data Types": [
    { 
      question: "What are JavaScript's primitive data types?", 
      answer: "String, Number, Boolean, Null, Undefined, Symbol, BigInt." 
    },
    { 
      question: "How do you declare a variable in JavaScript?", 
      answer: "Using var, let, or const. Prefer const for constants and let for variables that change." 
    },
    { 
      question: "What's the difference between == and === in JavaScript?", 
      answer: "== performs type coercion (loose equality), === checks both value and type (strict equality)." 
    },
    { 
      question: "What are falsy values in JavaScript?", 
      answer: "false, 0, -0, 0n, '', null, undefined, NaN." 
    },
    { 
      question: "How do you check the type of a variable?", 
      answer: "Use the typeof operator: typeof variable. Note: typeof null returns 'object'." 
    },
    { 
      question: "What's the difference between let, const, and var?", 
      answer: "var is function-scoped and hoisted; let and const are block-scoped; const cannot be reassigned." 
    },
    { 
      question: "How do you convert a string to a number?", 
      answer: "Use Number(str), parseInt(str), parseFloat(str), or the unary + operator: +str." 
    }
  ],

  "Control Flow and Loops": [
    { 
      question: "What are the different types of loops in JavaScript?", 
      answer: "for, while, do-while, for...in (objects), for...of (iterables), forEach (arrays)." 
    },
    { 
      question: "What's the difference between for...in and for...of?", 
      answer: "for...in iterates over object keys/indices; for...of iterates over iterable values." 
    },
    { 
      question: "How do you exit a loop early?", 
      answer: "Use break to exit completely, continue to skip to next iteration." 
    },
    { 
      question: "What's the syntax for a ternary operator?", 
      answer: "condition ? valueIfTrue : valueIfFalse" 
    },
    { 
      question: "How does a switch statement work?", 
      answer: "Compares a value against multiple cases using strict equality (===). Don't forget break statements." 
    },
    { 
      question: "What happens if you omit break in a switch case?", 
      answer: "Fall-through occurs - execution continues to the next case until a break is encountered." 
    }
  ],

  "Functions and Arrow Functions": [
    { 
      question: "What are the different ways to define a function?", 
      answer: "Function declaration, function expression, arrow function, method shorthand in objects." 
    },
    { 
      question: "What's the difference between function declarations and expressions?", 
      answer: "Declarations are hoisted and can be called before definition; expressions are not hoisted." 
    },
    { 
      question: "How do arrow functions differ from regular functions?", 
      answer: "Arrow functions don't have their own 'this', 'arguments', or 'super'. They can't be constructors." 
    },
    { 
      question: "What are rest parameters?", 
      answer: "...args syntax that collects remaining arguments into an array: function fn(...args) {}" 
    },
    { 
      question: "What's a higher-order function?", 
      answer: "A function that takes other functions as arguments or returns a function." 
    },
    { 
      question: "What's an IIFE?", 
      answer: "Immediately Invoked Function Expression: (function() { /* code */ })() - runs immediately." 
    },
    { 
      question: "How do default parameters work?", 
      answer: "function fn(param = defaultValue) {} - uses default if undefined is passed or no argument." 
    }
  ],

  "Objects and Prototypes": [
    { 
      question: "How do you create an object in JavaScript?", 
      answer: "Object literal {}, Object.create(), constructor function with new, or class instantiation." 
    },
    { 
      question: "What's the prototype chain?", 
      answer: "Objects inherit properties from their prototype, which may inherit from its prototype, forming a chain." 
    },
    { 
      question: "How do you add a method to all instances of a constructor?", 
      answer: "Add it to the constructor's prototype: Constructor.prototype.methodName = function() {}" 
    },
    { 
      question: "What's the difference between Object.assign() and spread operator?", 
      answer: "Both create shallow copies. Spread is newer syntax: {...obj}. Object.assign can merge multiple objects." 
    },
    { 
      question: "How do getters and setters work?", 
      answer: "get propName() {} and set propName(value) {} - allow computed properties and validation." 
    },
    { 
      question: "What does hasOwnProperty() check?", 
      answer: "Whether an object has a property as its own (not inherited from prototype chain)." 
    },
    { 
      question: "How do you prevent object modification?", 
      answer: "Object.freeze() (immutable), Object.seal() (no add/delete), Object.preventExtensions() (no add)." 
    }
  ],

  "Arrays and Array Methods": [
    { 
      question: "What's the difference between push/pop and shift/unshift?", 
      answer: "push/pop work on the end of array; shift/unshift work on the beginning." 
    },
    { 
      question: "What do map, filter, and reduce do?", 
      answer: "map transforms elements, filter selects elements, reduce accumulates to single value." 
    },
    { 
      question: "What's the difference between slice and splice?", 
      answer: "slice returns a copy of portion (non-mutating); splice modifies original array (mutating)." 
    },
    { 
      question: "How do you check if a variable is an array?", 
      answer: "Array.isArray(variable) - most reliable method." 
    },
    { 
      question: "What does the spread operator do with arrays?", 
      answer: "Expands array elements: [...arr1, ...arr2] for concatenation, [...arr] for copying." 
    },
    { 
      question: "How do find() and findIndex() differ?", 
      answer: "find() returns the first matching element; findIndex() returns its index (-1 if not found)." 
    },
    {
      question: "What's array destructuring?",
      answer: "Extracting array elements into variables: const [first, second, ...rest] = array;"
    }
  ],

  "DOM Manipulation and Events": [
    {
      question: "How do you select elements in the DOM?",
      answer: "getElementById(), getElementsByClassName(), querySelector(), querySelectorAll()."
    },
    {
      question: "What's the difference between querySelector and querySelectorAll?",
      answer: "querySelector returns first matching element; querySelectorAll returns NodeList of all matches."
    },
    {
      question: "How do you add an event listener?",
      answer: "element.addEventListener('event', handler) or element.onclick = handler."
    },
    {
      question: "What's event delegation?",
      answer: "Attaching event listener to parent element to handle events from child elements using event bubbling."
    },
    {
      question: "How do you prevent default browser behavior?",
      answer: "event.preventDefault() in the event handler function."
    },
    {
      question: "What's the difference between innerHTML and textContent?",
      answer: "innerHTML gets/sets HTML content; textContent gets/sets only text (safer, no HTML parsing)."
    },
    {
      question: "How do you create and append new elements?",
      answer: "document.createElement('tag'), set properties, then parentElement.appendChild(newElement)."
    }
  ],

  "Asynchronous JavaScript (Promises, Async/Await)": [
    {
      question: "What's a Promise and what states can it have?",
      answer: "Object representing eventual completion/failure of async operation. States: pending, fulfilled, rejected."
    },
    {
      question: "How do you handle Promise results?",
      answer: "Use .then() for success, .catch() for errors, .finally() for cleanup."
    },
    {
      question: "What's the difference between Promise.all() and Promise.race()?",
      answer: "Promise.all() waits for all promises; Promise.race() resolves with first completed promise."
    },
    {
      question: "How does async/await work?",
      answer: "async functions return promises; await pauses execution until promise resolves."
    },
    {
      question: "How do you handle errors with async/await?",
      answer: "Use try/catch blocks around await statements."
    },
    {
      question: "What's Promise.allSettled()?",
      answer: "Waits for all promises to settle (resolve or reject) and returns results for all."
    },
    {
      question: "Can you use await outside an async function?",
      answer: "No, await can only be used inside async functions (except top-level await in modules)."
    }
  ],

  "Modules and Imports/Exports": [
    {
      question: "What's the difference between named and default exports?",
      answer: "Named: export { name }; Default: export default value. One default per module, multiple named allowed."
    },
    {
      question: "How do you import everything from a module?",
      answer: "import * as moduleName from './module.js'"
    },
    {
      question: "What's dynamic import?",
      answer: "import() function that returns a promise, allows conditional/lazy loading of modules."
    },
    {
      question: "What's the difference between ES6 modules and CommonJS?",
      answer: "ES6: import/export, static analysis; CommonJS: require/module.exports, dynamic, Node.js default."
    },
    {
      question: "How do you re-export from another module?",
      answer: "export { name } from './other-module.js' or export * from './other-module.js'"
    },
    {
      question: "What's a barrel export?",
      answer: "Index file that re-exports multiple modules, allowing clean imports from single location."
    }
  ],

  "Error Handling and Debugging": [
    {
      question: "What's the syntax for try-catch-finally?",
      answer: "try { code } catch (error) { handle } finally { cleanup } - finally always runs."
    },
    {
      question: "How do you create custom error types?",
      answer: "class CustomError extends Error { constructor(message) { super(message); this.name = 'CustomError'; } }"
    },
    {
      question: "What debugging methods does console provide?",
      answer: "log, error, warn, info, table, group, time/timeEnd, trace, assert."
    },
    {
      question: "How do you handle errors in async functions?",
      answer: "Use try-catch with await, or .catch() with promises."
    },
    {
      question: "What's the difference between throw and return?",
      answer: "throw stops execution and passes error to catch block; return exits function normally."
    },
    {
      question: "How do you debug performance issues?",
      answer: "Use console.time(), performance.now(), browser dev tools profiler, console.trace()."
    }
  ],

  "ES6+ Features (Destructuring, Spread, Rest)": [
    {
      question: "What's object destructuring?",
      answer: "Extracting object properties: const { name, age } = person; Can rename: { name: fullName }"
    },
    {
      question: "How does the spread operator work with objects?",
      answer: "Copies properties: { ...obj1, ...obj2 }. Later properties override earlier ones."
    },
    {
      question: "What's the difference between rest and spread?",
      answer: "Rest collects items (...args in function params); Spread expands items (...array in calls)."
    },
    {
      question: "What are template literals?",
      answer: "Strings with backticks allowing expressions: `Hello ${name}` and multi-line strings."
    },
    {
      question: "What's enhanced object literal syntax?",
      answer: "Shorthand properties, computed keys, method shorthand: { name, [key]: value, method() {} }"
    },
    {
      question: "How do you set default values in destructuring?",
      answer: "const { name = 'Unknown', age = 0 } = person; const [first = 'default'] = array;"
    }
  ],

  "Closures and Scope": [
    {
      question: "What's a closure?",
      answer: "Inner function that has access to outer function's variables even after outer function returns."
    },
    {
      question: "What are the different types of scope in JavaScript?",
      answer: "Global scope, function scope, block scope (let/const), module scope."
    },
    {
      question: "What's the difference between function and block scope?",
      answer: "Function scope: var, entire function; Block scope: let/const, within {} blocks."
    },
    {
      question: "What's a common closure pitfall with loops?",
      answer: "Using var in loops - all closures reference same variable. Use let or IIFE to fix."
    },
    {
      question: "How can closures be used for data privacy?",
      answer: "Create private variables that can only be accessed through returned functions (module pattern)."
    },
    {
      question: "What's the module pattern?",
      answer: "IIFE that returns object with public methods, keeping private variables in closure."
    }
  ],

  "Classes and Inheritance": [
    {
      question: "How do you define a class in JavaScript?",
      answer: "class ClassName { constructor() {} method() {} } - syntactic sugar over prototypes."
    },
    {
      question: "How does inheritance work with classes?",
      answer: "class Child extends Parent { constructor() { super(); } } - super() calls parent constructor."
    },
    {
      question: "What are private fields in classes?",
      answer: "Fields prefixed with #: #privateField. Only accessible within the class."
    },
    {
      question: "What's the difference between static and instance methods?",
      answer: "Static methods belong to class (Class.method()); instance methods belong to objects (obj.method())."
    },
    {
      question: "How do getters and setters work in classes?",
      answer: "get propName() {} and set propName(value) {} - accessed like properties: obj.propName"
    },
    {
      question: "What's method overriding?",
      answer: "Child class defining method with same name as parent - child version is used."
    }
  ],

  "Event Loop and Concurrency": [
    {
      question: "What's the JavaScript event loop?",
      answer: "Mechanism that handles async operations by managing call stack, callback queue, and microtask queue."
    },
    {
      question: "What's the difference between microtasks and macrotasks?",
      answer: "Microtasks (Promises) have higher priority and run before macrotasks (setTimeout, setInterval)."
    },
    {
      question: "Why is JavaScript called single-threaded?",
      answer: "Has one call stack, executes one thing at a time, but can handle async operations via event loop."
    },
    {
      question: "What happens when you call setTimeout with 0ms?",
      answer: "Callback goes to macrotask queue, runs after current execution and all microtasks complete."
    },
    {
      question: "How do you avoid blocking the event loop?",
      answer: "Break heavy computations into chunks, use setTimeout/setImmediate, or Web Workers."
    },
    {
      question: "What's the call stack?",
      answer: "LIFO structure tracking function calls - where JavaScript keeps track of execution context."
    }
  ],

  "Web APIs and Fetch": [
    {
      question: "What's the Fetch API?",
      answer: "Modern way to make HTTP requests, returns promises, replaces XMLHttpRequest."
    },
    {
      question: "How do you make a POST request with fetch?",
      answer: "fetch(url, { method: 'POST', headers: {...}, body: JSON.stringify(data) })"
    },
    {
      question: "How do you handle different response types?",
      answer: "response.json(), response.text(), response.blob(), response.arrayBuffer()"
    },
    {
      question: "How do you check if a fetch request was successful?",
      answer: "Check response.ok or response.status. Fetch doesn't reject on HTTP error codes."
    },
    {
      question: "How do you cancel a fetch request?",
      answer: "Use AbortController: const controller = new AbortController(); fetch(url, { signal: controller.signal })"
    },
    {
      question: "What are CORS errors and how do you handle them?",
      answer: "Cross-Origin Resource Sharing errors. Handle server-side with proper headers or use proxy."
    }
  ],

  "Local Storage and Cookies": [
    {
      question: "What's the difference between localStorage and sessionStorage?",
      answer: "localStorage persists until cleared; sessionStorage clears when tab closes."
    },
    {
      question: "How do you store objects in localStorage?",
      answer: "JSON.stringify() to store, JSON.parse() to retrieve: localStorage.setItem('key', JSON.stringify(obj))"
    },
    {
      question: "What are the storage limits for localStorage?",
      answer: "Usually 5-10MB per origin, varies by browser."
    },
    {
      question: "How do cookies differ from localStorage?",
      answer: "Cookies: 4KB limit, sent with requests, have expiration; localStorage: larger, client-only, persistent."
    },
    {
      question: "How do you set a cookie with expiration?",
      answer: "document.cookie = 'name=value; expires=date; path=/'"
    },
    {
      question: "What's the storage event?",
      answer: "Fired when localStorage changes in another tab: window.addEventListener('storage', handler)"
    }
  ],

  "Testing (Jest, Mocha)": [
    {
      question: "What's the basic structure of a Jest test?",
      answer: "describe('suite', () => { test('description', () => { expect(actual).toBe(expected); }); })"
    },
    {
      question: "What's the difference between toBe and toEqual?",
      answer: "toBe uses === (reference equality); toEqual does deep comparison (value equality)."
    },
    {
      question: "How do you test async functions?",
      answer: "Return promise, use async/await, or use resolves/rejects: expect(promise).resolves.toBe(value)"
    },
    {
      question: "What are Jest mocks?",
      answer: "jest.fn() creates mock functions; jest.mock() mocks entire modules for testing."
    },
    {
      question: "What's the purpose of beforeEach and afterEach?",
      answer: "Setup and cleanup for each test: beforeEach runs before each test, afterEach runs after."
    },
    {
      question: "How do you test for thrown errors?",
      answer: "expect(() => { throwingFunction(); }).toThrow('error message')"
    }
  ],

  "Build Tools and Bundlers (Webpack, Rollup)": [
    {
      question: "What's the purpose of a module bundler?",
      answer: "Combines multiple modules into fewer files, handles dependencies, optimizes for production."
    },
    {
      question: "What's the difference between Webpack and Rollup?",
      answer: "Webpack: full-featured, good for apps; Rollup: simpler, tree-shaking focused, good for libraries."
    },
    {
      question: "What's tree shaking?",
      answer: "Removing unused code from bundles to reduce file size."
    },
    {
      question: "What's code splitting?",
      answer: "Breaking code into chunks that can be loaded on demand to improve performance."
    },
    {
      question: "What's the difference between development and production builds?",
      answer: "Development: unminified, source maps, hot reload; Production: minified, optimized, no dev tools."
    },
    {
      question: "What are loaders in Webpack?",
      answer: "Transform files during bundling: CSS loader, Babel loader, file loader, etc."
    }
  ],

  "Frameworks Overview (React, Vue, Angular basics)": [
    {
      question: "What's the Virtual DOM in React?",
      answer: "JavaScript representation of real DOM, allows efficient updates by comparing and updating only changes."
    },
    {
      question: "What's the difference between React, Vue, and Angular?",
      answer: "React: library, JSX, component-based; Vue: progressive framework, template syntax; Angular: full framework, TypeScript."
    },
    {
      question: "What are React hooks?",
      answer: "Functions that let you use state and lifecycle in functional components: useState, useEffect, etc."
    },
    {
      question: "What's component-based architecture?",
      answer: "Building UIs as tree of reusable, self-contained components with their own state and logic."
    },
    {
      question: "What's data binding in Vue?",
      answer: "Connecting data to DOM: one-way (interpolation), two-way (v-model), event binding (v-on)."
    },
    {
      question: "What's dependency injection in Angular?",
      answer: "Design pattern where dependencies are provided to components rather than created by them."
    }
  ],

  "TypeScript Basics": [
    {
      question: "What's TypeScript?",
      answer: "Superset of JavaScript that adds static type checking and compiles to JavaScript."
    },
    {
      question: "How do you define an interface in TypeScript?",
      answer: "interface Name { property: type; method(): returnType; } - defines object shape."
    },
    {
      question: "What's the difference between interface and type?",
      answer: "Interface: extensible, for object shapes; Type: aliases, unions, more flexible but not extensible."
    },
    {
      question: "What are generics in TypeScript?",
      answer: "Type variables that make components work with multiple types: function fn<T>(arg: T): T"
    },
    {
      question: "What's type assertion?",
      answer: "Telling TypeScript a value's type: value as Type or <Type>value"
    },
    {
      question: "What are utility types?",
      answer: "Built-in type transformations: Partial<T>, Required<T>, Pick<T, K>, Omit<T, K>"
    }
  ],

  "Best Practices and Code Style": [
    {
      question: "Why should you avoid var and use let/const?",
      answer: "var has function scope and hoisting issues; let/const have block scope and prevent redeclaration."
    },
    {
      question: "What's the principle of single responsibility?",
      answer: "Each function/class should have one reason to change, do one thing well."
    },
    {
      question: "How should you handle errors in async code?",
      answer: "Use try-catch with async/await, always handle promise rejections, provide meaningful error messages."
    },
    {
      question: "What's the DRY principle?",
      answer: "Don't Repeat Yourself - avoid code duplication by extracting common functionality."
    },
    {
      question: "How should you name variables and functions?",
      answer: "Use descriptive names, camelCase for variables/functions, PascalCase for classes, UPPER_CASE for constants."
    },
    {
      question: "What's the difference between pure and impure functions?",
      answer: "Pure: same input = same output, no side effects; Impure: may have side effects or depend on external state."
    }
  ],

  "CLI and Node.js Basics": [
    {
      question: "What's Node.js?",
      answer: "JavaScript runtime built on Chrome's V8 engine, allows JavaScript to run outside browsers."
    },
    {
      question: "What's npm?",
      answer: "Node Package Manager - manages dependencies, scripts, and publishes packages."
    },
    {
      question: "What's the difference between dependencies and devDependencies?",
      answer: "dependencies: needed in production; devDependencies: only needed for development (testing, building)."
    },
    {
      question: "How do you read command line arguments in Node.js?",
      answer: "process.argv array contains command line arguments starting from index 2."
    },
    {
      question: "What's package.json?",
      answer: "Manifest file containing project metadata, dependencies, scripts, and configuration."
    },
    {
      question: "How do you handle environment variables?",
      answer: "process.env object contains environment variables; use .env files with dotenv package."
    },
    {
      question: "What's the difference between require and import?",
      answer: "require: CommonJS, synchronous, dynamic; import: ES6 modules, static analysis, tree-shaking."
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n📚 ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available Quiz Topics:');
  console.log('='.repeat(30));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];

  // Collect all questions from all topics
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  // Shuffle and select random questions
  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM JAVASCRIPT QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);

    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Gets quiz statistics
 */
function getQuizStats() {
  const stats = {};
  let totalQuestions = 0;

  Object.entries(quizzes).forEach(([topic, questions]) => {
    stats[topic] = questions.length;
    totalQuestions += questions.length;
  });

  console.log('\n📊 Quiz Statistics:');
  console.log('='.repeat(30));
  Object.entries(stats).forEach(([topic, count]) => {
    console.log(`${topic}: ${count} questions`);
  });
  console.log(`\nTotal: ${totalQuestions} questions across ${Object.keys(quizzes).length} topics`);

  return { stats, totalQuestions, topicCount: Object.keys(quizzes).length };
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();

  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) ||
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });

  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }

  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));

  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);

    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    quizzes,
    printQuiz,
    listTopics,
    randomQuiz,
    getQuizStats,
    searchQuestions
  };
}

// Example usage and help
console.log('🚀 JavaScript Manual Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('📊 getQuizStats() - Show quiz statistics');
console.log('🔍 searchQuestions("keyword") - Search for questions');
console.log('\n💡 Example: printQuiz("Basic Syntax and Data Types")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("array", true) - Find array-related questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
