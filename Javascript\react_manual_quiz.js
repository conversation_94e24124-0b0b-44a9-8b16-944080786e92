/**
 * React Manual Quiz
 * Comprehensive quiz covering all sections of the React programming manual
 */

const quizzes = {
  "React Fundamentals": [
    { 
      question: "What is React?", 
      answer: "A JavaScript library for building user interfaces with a component-based architecture and virtual DOM." 
    },
    { 
      question: "What is the Virtual DOM?", 
      answer: "A JavaScript representation of the real DOM that <PERSON><PERSON> uses to optimize updates by comparing changes and updating only what's necessary." 
    },
    { 
      question: "What are the core principles of React?", 
      answer: "Component-based architecture, unidirectional data flow, declarative programming, and reusable UI components." 
    },
    { 
      question: "What's the difference between React and ReactDOM?", 
      answer: "React is the core library for components; ReactDOM handles rendering React components to the DOM." 
    },
    { 
      question: "What is unidirectional data flow?", 
      answer: "Data flows down from parent to child components via props, and events flow up via callback functions." 
    },
    { 
      question: "Why use React over vanilla JavaScript?", 
      answer: "Better organization, reusability, performance optimization, large ecosystem, and declarative approach." 
    }
  ],

  "JSX and Components": [
    { 
      question: "What is JSX?", 
      answer: "A syntax extension for JavaScript that allows writing HTML-like code in JavaScript files." 
    },
    { 
      question: "How does JSX compile to JavaScript?", 
      answer: "JSX compiles to React.createElement() calls that create virtual DOM objects." 
    },
    { 
      question: "What's the difference between functional and class components?", 
      answer: "Functional components are simpler functions that return JSX; class components extend React.Component with lifecycle methods." 
    },
    { 
      question: "How do you handle conditional rendering in JSX?", 
      answer: "Use ternary operators, logical && operator, or if statements outside JSX: {condition ? <A /> : <B />}" 
    },
    { 
      question: "Why are keys important in React lists?", 
      answer: "Keys help React identify which items have changed, been added, or removed for efficient re-rendering." 
    },
    { 
      question: "What is component composition?", 
      answer: "Building complex UIs by combining simpler components, often using props.children for flexible layouts." 
    },
    { 
      question: "How do you pass data to child components?", 
      answer: "Through props: <ChildComponent name='Alice' age={30} />" 
    }
  ],

  "Props and State": [
    { 
      question: "What are props in React?", 
      answer: "Properties passed from parent to child components. They are read-only and help make components reusable." 
    },
    { 
      question: "What is state in React?", 
      answer: "Local data that belongs to a component and can change over time, triggering re-renders when updated." 
    },
    { 
      question: "What's the difference between props and state?", 
      answer: "Props are passed from parent (immutable); state is internal to component (mutable)." 
    },
    { 
      question: "How do you define default props?", 
      answer: "Use defaultProps property or default parameters: function Button({ variant = 'primary' }) {}" 
    },
    { 
      question: "What is prop drilling?", 
      answer: "Passing props through multiple component levels to reach deeply nested components. Solved with Context API." 
    },
    { 
      question: "How do you validate props?", 
      answer: "Use PropTypes library or TypeScript for type checking: PropTypes.string.isRequired" 
    }
  ],

  "Hooks": [
    { 
      question: "What are React Hooks?", 
      answer: "Functions that let you use state and other React features in functional components." 
    },
    { 
      question: "What does useState return?", 
      answer: "An array with current state value and a setter function: [state, setState]" 
    },
    { 
      question: "When does useEffect run?", 
      answer: "After every render by default, or when dependencies change if dependency array is provided." 
    },
    { 
      question: "How do you clean up in useEffect?", 
      answer: "Return a cleanup function from useEffect: return () => { /* cleanup */ }" 
    },
    { 
      question: "What is the dependency array in useEffect?", 
      answer: "Second argument that controls when effect runs. Empty array [] runs once, no array runs every render." 
    },
    { 
      question: "What are the rules of hooks?", 
      answer: "Only call at top level, only call from React functions, hook names start with 'use'." 
    },
    { 
      question: "When would you use useReducer over useState?", 
      answer: "For complex state logic, multiple state values, or when next state depends on previous state." 
    },
    { 
      question: "What is a custom hook?", 
      answer: "A JavaScript function that starts with 'use' and can call other hooks to share stateful logic." 
    }
  ],

  "Event Handling": [
    { 
      question: "How do you handle events in React?", 
      answer: "Use event handler props like onClick, onChange: <button onClick={handleClick}>Click</button>" 
    },
    { 
      question: "What are SyntheticEvents?", 
      answer: "React's wrapper around native events that provides consistent API across browsers." 
    },
    { 
      question: "How do you prevent default behavior?", 
      answer: "Call event.preventDefault() in the event handler function." 
    },
    { 
      question: "How do you pass parameters to event handlers?", 
      answer: "Use arrow functions or bind: onClick={() => handleClick(id)} or onClick={handleClick.bind(null, id)}" 
    },
    { 
      question: "What is event delegation in React?", 
      answer: "React automatically delegates events to the root element for performance, handling them through event bubbling." 
    },
    { 
      question: "How do you handle form submissions?", 
      answer: "Use onSubmit prop on form element and prevent default: onSubmit={(e) => { e.preventDefault(); /* handle */ }}" 
    }
  ],

  "State Management": [
    { 
      question: "What is the Context API?", 
      answer: "React's built-in solution for sharing state across components without prop drilling." 
    },
    { 
      question: "When should you use Context vs props?", 
      answer: "Use Context for global state (theme, user, language); use props for component-specific data." 
    },
    { 
      question: "What is Redux?", 
      answer: "A predictable state container for JavaScript apps with unidirectional data flow and time-travel debugging." 
    },
    { 
      question: "What are the three principles of Redux?", 
      answer: "Single source of truth, state is read-only, changes made with pure functions (reducers)." 
    },
    { 
      question: "What is the difference between local and global state?", 
      answer: "Local state belongs to one component; global state is shared across multiple components." 
    },
    { 
      question: "What is state lifting?", 
      answer: "Moving state up to the closest common ancestor when multiple components need to share state." 
    }
  ],

  "Performance Optimization": [
    { 
      question: "What is React.memo?", 
      answer: "A higher-order component that memoizes functional components, preventing re-renders when props haven't changed." 
    },
    { 
      question: "What does useMemo do?", 
      answer: "Memoizes expensive calculations, only recalculating when dependencies change." 
    },
    { 
      question: "What does useCallback do?", 
      answer: "Memoizes function references, preventing child re-renders when function props haven't changed." 
    },
    { 
      question: "What is code splitting in React?", 
      answer: "Breaking code into smaller chunks loaded on demand using React.lazy() and dynamic imports." 
    },
    { 
      question: "What is React.lazy()?", 
      answer: "Function for lazy loading components: const LazyComponent = lazy(() => import('./Component'))" 
    },
    { 
      question: "What is the React Profiler?", 
      answer: "A tool for measuring performance of React components and identifying bottlenecks." 
    },
    { 
      question: "How do you optimize list rendering?", 
      answer: "Use proper keys, virtualization for long lists, and React.memo for list items." 
    }
  ],

  "Testing": [
    { 
      question: "What is React Testing Library?", 
      answer: "A testing utility focused on testing components from user perspective rather than implementation details." 
    },
    { 
      question: "What's the difference between shallow and mount in Enzyme?", 
      answer: "Shallow renders only the component itself; mount renders the full component tree including children." 
    },
    { 
      question: "How do you test user interactions?", 
      answer: "Use fireEvent or userEvent from Testing Library: fireEvent.click(button) or user.click(button)" 
    },
    { 
      question: "How do you test async components?", 
      answer: "Use waitFor, findBy queries, or act() for async operations and state updates." 
    },
    { 
      question: "What is snapshot testing?", 
      answer: "Testing that captures component output and compares against saved snapshots to detect changes." 
    },
    { 
      question: "How do you mock dependencies in React tests?", 
      answer: "Use Jest mocks: jest.mock('./module') or mock specific functions with jest.fn()" 
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n⚛️ ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available React Quiz Topics:');
  console.log('='.repeat(33));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM REACT QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) || 
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });
  
  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }
  
  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));
  
  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });
  
  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    quizzes, 
    printQuiz, 
    listTopics, 
    randomQuiz, 
    searchQuestions 
  };
}

// Example usage and help
console.log('⚛️ React Manual Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('🔍 searchQuestions("keyword") - Search for questions');

console.log('\n💡 Example: printQuiz("React Fundamentals")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("hooks", true) - Find hooks-related questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
