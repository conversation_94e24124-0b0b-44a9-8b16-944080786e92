{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive TypeScript Programming Manual\n", "\n", "This manual serves as a complete reference for TypeScript development, from basic types to advanced patterns. TypeScript is a superset of JavaScript that adds static type checking and modern language features.\n", "\n", "## Table of Contents\n", "1. [TypeScript Fundamentals](#fundamentals)\n", "2. [Basic Types and Type Annotations](#types)\n", "3. [Interfaces and Type Aliases](#interfaces)\n", "4. [Classes and Inheritance](#classes)\n", "5. [Generics](#generics)\n", "6. [Advanced Types](#advanced)\n", "7. [Modules and Namespaces](#modules)\n", "8. [Decorators](#decorators)\n", "9. [Utility Types](#utility)\n", "10. [Configuration and Tooling](#config)\n", "11. [Integration with Frameworks](#integration)\n", "12. [Best Practices](#best-practices)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# TypeScript Fundamentals {#fundamentals}\n", "\n", "TypeScript extends JavaScript by adding type definitions. It compiles to plain JavaScript and can run anywhere JavaScript runs."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["// TypeScript Fundamentals\n", "// Note: This notebook shows TypeScript syntax as comments since it's a JS environment\n", "\n", "console.log('=== TypeScript Fundamentals ===');\n", "\n", "/*\n", "// TypeScript Installation and Setup\n", "// npm install -g typescript\n", "// npm install -D typescript @types/node\n", "\n", "// Compile TypeScript to JavaScript\n", "// tsc filename.ts\n", "// tsc --watch filename.ts\n", "\n", "// Basic TypeScript file structure\n", "// filename.ts\n", "*/\n", "\n", "// JavaScript equivalent of TypeScript concepts\n", "console.log('TypeScript provides:');\n", "console.log('1. Static type checking at compile time');\n", "console.log('2. Enhanced IDE support with autocomplete');\n", "console.log('3. Early error detection');\n", "console.log('4. Better code documentation');\n", "console.log('5. Easier refactoring');\n", "\n", "// Type checking simulation in JavaScript\n", "function validateTypes(value, expectedType) {\n", "    const actualType = typeof value;\n", "    if (actualType !== expectedType) {\n", "        throw new Error(`Expected ${expectedType}, got ${actualType}`);\n", "    }\n", "    return true;\n", "}\n", "\n", "// Example of what TypeScript prevents\n", "try {\n", "    validateTypes('hello', 'number'); // This would be caught at compile time in TypeScript\n", "} catch (error) {\n", "    console.log('Runtime error that TypeScript would catch:', error.message);\n", "}\n", "\n", "// TypeScript compilation process\n", "console.log('\\nTypeScript compilation process:');\n", "console.log('1. Write TypeScript code (.ts files)');\n", "console.log('2. TypeScript compiler (tsc) checks types');\n", "console.log('3<PERSON> <PERSON><PERSON><PERSON> generates JavaScript (.js files)');\n", "console.log('4. JavaScript runs in any JS environment');\n", "\n", "// Benefits demonstration\n", "console.log('\\n=== Benefits of TypeScript ===');\n", "\n", "// JavaScript function without types\n", "function addJS(a, b) {\n", "    return a + b; // Could be numbers, strings, or anything\n", "}\n", "\n", "console.log('JavaScript add(2, 3):', addJS(2, 3)); // 5\n", "console.log('JavaScript add(\"2\", \"3\"):', addJS('2', '3')); // \"23\" - unexpected!\n", "\n", "/*\n", "// TypeScript function with types\n", "function addTS(a: number, b: number): number {\n", "    return a + b; // Only numbers allowed\n", "}\n", "\n", "// This would cause a compile error in TypeScript:\n", "// addTS(\"2\", \"3\"); // Error: Argument of type 'string' is not assignable to parameter of type 'number'\n", "*/\n", "\n", "// Simulating TypeScript's type safety in JavaScript\n", "function addWithTypeCheck(a, b) {\n", "    if (typeof a !== 'number' || typeof b !== 'number') {\n", "        throw new Error('Both arguments must be numbers');\n", "    }\n", "    return a + b;\n", "}\n", "\n", "console.log('Type-safe add(2, 3):', addWithTypeCheck(2, 3));\n", "\n", "try {\n", "    addWithTypeCheck('2', '3');\n", "} catch (error) {\n", "    console.log('Type error caught:', error.message);\n", "}\n", "\n", "// TypeScript development workflow\n", "console.log('\\n=== Development Workflow ===');\n", "console.log('1. Install TypeScript: npm install -g typescript');\n", "console.log('2. Initialize project: tsc --init');\n", "console.log('3. Write TypeScript code with type annotations');\n", "console.log('4. Compile: tsc or tsc --watch for auto-compilation');\n", "console.log('5. Run compiled JavaScript: node dist/app.js');\n", "\n", "// Common TypeScript file extensions\n", "console.log('\\nTypeScript file extensions:');\n", "console.log('.ts - TypeScript files');\n", "console.log('.tsx - TypeScript with JSX (React)');\n", "console.log('.d.ts - Type declaration files');\n", "console.log('.js - Compiled JavaScript output');"]}, {"cell_type": "markdown", "id": "71c71fe2", "metadata": {}, "source": ["# Basic Types and Type Annotations {#types}\n", "\n", "TypeScript provides a rich type system including primitive types, arrays, objects, and more complex type constructs."]}, {"cell_type": "code", "execution_count": null, "id": "ff42471f", "metadata": {}, "outputs": [], "source": ["// Basic Types in TypeScript\n", "console.log('=== Basic Types ===');\n", "\n", "/*\n", "// Primitive Types\n", "let name: string = \"<PERSON>\";\n", "let age: number = 30;\n", "let isActive: boolean = true;\n", "let value: null = null;\n", "let data: undefined = undefined;\n", "\n", "// BigInt and Symbol (ES2020+)\n", "let bigNumber: bigint = 100n;\n", "let uniqueId: symbol = Symbol(\"id\");\n", "\n", "// <PERSON><PERSON><PERSON>\n", "let numbers: number[] = [1, 2, 3, 4, 5];\n", "let names: <PERSON><PERSON><PERSON><string> = [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\"];\n", "let mixed: (string | number)[] = [\"<PERSON>\", 30, \"<PERSON>\", 25];\n", "\n", "// Tuples - fixed length arrays with specific types\n", "let person: [string, number] = [\"<PERSON>\", 30];\n", "let coordinates: [number, number, number] = [10, 20, 30];\n", "\n", "// Object types\n", "let user: { name: string; age: number; email?: string } = {\n", "    name: \"<PERSON>\",\n", "    age: 30\n", "    // email is optional\n", "};\n", "\n", "// Function types\n", "let greet: (name: string) => string = (name) => `Hello, ${name}!`;\n", "let calculate: (a: number, b: number) => number = (a, b) => a + b;\n", "\n", "// Any type (avoid when possible)\n", "let anything: any = \"hello\";\n", "anything = 42;\n", "anything = true;\n", "\n", "// Unknown type (safer than any)\n", "let userInput: unknown = \"hello\";\n", "if (typeof userInput === \"string\") {\n", "    console.log(userInput.toUpperCase()); // Type guard required\n", "}\n", "\n", "// Void type (for functions that don't return)\n", "function logMessage(message: string): void {\n", "    console.log(message);\n", "}\n", "\n", "// Never type (for functions that never return)\n", "function throwError(message: string): never {\n", "    throw new Error(message);\n", "}\n", "*/\n", "\n", "// JavaScript equivalents with runtime type checking\n", "console.log('Demonstrating TypeScript types in JavaScript:');\n", "\n", "// Simulating typed variables\n", "const typedVariables = {\n", "    name: '<PERSON>',        // string\n", "    age: 30,              // number\n", "    isActive: true,       // boolean\n", "    scores: [95, 87, 92], // number[]\n", "    person: ['<PERSON>', 30] // tuple-like\n", "};\n", "\n", "console.log('Typed variables:', typedVariables);\n", "\n", "// Type checking functions\n", "function isString(value) {\n", "    return typeof value === 'string';\n", "}\n", "\n", "function isNumber(value) {\n", "    return typeof value === 'number' && !isNaN(value);\n", "}\n", "\n", "function isArray(value) {\n", "    return Array.isArray(value);\n", "}\n", "\n", "// Simulating TypeScript's type safety\n", "function createUser(name, age, email) {\n", "    // Runtime type validation (what TypeScript does at compile time)\n", "    if (!isString(name)) {\n", "        throw new Error('Name must be a string');\n", "    }\n", "    if (!isNumber(age) || age < 0) {\n", "        throw new Error('Age must be a positive number');\n", "    }\n", "    if (email !== undefined && !isString(email)) {\n", "        throw new Error('Email must be a string or undefined');\n", "    }\n", "    \n", "    return { name, age, email };\n", "}\n", "\n", "// Valid usage\n", "const user1 = createUser('<PERSON>', 30, '<EMAIL>');\n", "const user2 = createUser('<PERSON>', 25); // email is optional\n", "console.log('Created users:', user1, user2);\n", "\n", "// Invalid usage (would be caught by TypeScript at compile time)\n", "try {\n", "    createUser(123, 'thirty'); // Wrong types\n", "} catch (error) {\n", "    console.log('Type error:', error.message);\n", "}\n", "\n", "// Union types simulation\n", "console.log('\\n=== Union Types ===');\n", "\n", "/*\n", "// TypeScript union types\n", "let id: string | number;\n", "id = \"abc123\";\n", "id = 123;\n", "\n", "function formatId(id: string | number): string {\n", "    if (typeof id === \"string\") {\n", "        return id.toUpperCase();\n", "    } else {\n", "        return id.toString();\n", "    }\n", "}\n", "*/\n", "\n", "// JavaScript equivalent\n", "function formatId(id) {\n", "    if (typeof id === 'string') {\n", "        return id.toUpperCase();\n", "    } else if (typeof id === 'number') {\n", "        return id.toString();\n", "    } else {\n", "        throw new Error('ID must be string or number');\n", "    }\n", "}\n", "\n", "console.log('Format string ID:', formatId('abc123'));\n", "console.log('Format number ID:', formatId(123));\n", "\n", "// Literal types simulation\n", "console.log('\\n=== Literal Types ===');\n", "\n", "/*\n", "// TypeScript literal types\n", "type Status = \"pending\" | \"approved\" | \"rejected\";\n", "type Direction = \"north\" | \"south\" | \"east\" | \"west\";\n", "\n", "function updateStatus(status: Status): void {\n", "    console.log(`Status updated to: ${status}`);\n", "}\n", "*/\n", "\n", "// JavaScript equivalent with validation\n", "const VALID_STATUSES = ['pending', 'approved', 'rejected'];\n", "const VALID_DIRECTIONS = ['north', 'south', 'east', 'west'];\n", "\n", "function updateStatus(status) {\n", "    if (!VALID_STATUSES.includes(status)) {\n", "        throw new Error(`Invalid status. Must be one of: ${VALID_STATUSES.join(', ')}`);\n", "    }\n", "    console.log(`Status updated to: ${status}`);\n", "}\n", "\n", "updateStatus('approved');\n", "\n", "try {\n", "    updateStatus('invalid');\n", "} catch (error) {\n", "    console.log('Literal type error:', error.message);\n", "}\n", "\n", "console.log('\\nTypeScript provides compile-time type safety that prevents these runtime errors!');"]}, {"cell_type": "markdown", "id": "b3ad4049", "metadata": {}, "source": ["# Interfaces and Type Aliases {#interfaces}\n", "\n", "Interfaces and type aliases allow you to define custom types and contracts for objects, functions, and complex data structures."]}, {"cell_type": "code", "execution_count": null, "id": "4adc818d", "metadata": {}, "outputs": [], "source": ["// Interfaces and Type Aliases\n", "console.log('=== Interfaces and Type Aliases ===');\n", "\n", "/*\n", "// Interface definition\n", "interface User {\n", "    id: number;\n", "    name: string;\n", "    email: string;\n", "    age?: number; // Optional property\n", "    readonly createdAt: Date; // Read-only property\n", "}\n", "\n", "// Interface for functions\n", "interface CalculatorFunction {\n", "    (a: number, b: number): number;\n", "}\n", "\n", "// Interface with methods\n", "interface UserService {\n", "    getUser(id: number): User | null;\n", "    createUser(userData: Omit<User, 'id' | 'createdAt'>): User;\n", "    updateUser(id: number, updates: Partial<User>): User;\n", "    deleteUser(id: number): boolean;\n", "}\n", "\n", "// Extending interfaces\n", "interface AdminUser extends User {\n", "    permissions: string[];\n", "    lastLogin?: Date;\n", "}\n", "\n", "// Interface merging (declaration merging)\n", "interface User {\n", "    isActive: boolean; // This gets merged with the User interface above\n", "}\n", "\n", "// Type aliases\n", "type Status = 'pending' | 'approved' | 'rejected';\n", "type ID = string | number;\n", "type UserRole = 'admin' | 'user' | 'moderator';\n", "\n", "// Complex type aliases\n", "type ApiResponse<T> = {\n", "    data: T;\n", "    status: number;\n", "    message: string;\n", "    timestamp: Date;\n", "};\n", "\n", "// Function type aliases\n", "type EventHandler<T> = (event: T) => void;\n", "type AsyncFunction<T, R> = (arg: T) => Promise<R>;\n", "\n", "// Intersection types\n", "type UserWithTimestamps = User & {\n", "    createdAt: Date;\n", "    updatedAt: Date;\n", "};\n", "*/\n", "\n", "// JavaScript equivalents with runtime validation\n", "console.log('Simulating TypeScript interfaces in JavaScript:');\n", "\n", "// Interface validation functions\n", "function validateUser(obj) {\n", "    const required = ['id', 'name', 'email'];\n", "    const optional = ['age', 'createdAt', 'isActive'];\n", "    \n", "    // Check required properties\n", "    for (const prop of required) {\n", "        if (!(prop in obj)) {\n", "            throw new Error(`Missing required property: ${prop}`);\n", "        }\n", "    }\n", "    \n", "    // Type checking\n", "    if (typeof obj.id !== 'number') {\n", "        throw new Error('id must be a number');\n", "    }\n", "    if (typeof obj.name !== 'string') {\n", "        throw new Error('name must be a string');\n", "    }\n", "    if (typeof obj.email !== 'string') {\n", "        throw new Error('email must be a string');\n", "    }\n", "    if (obj.age !== undefined && typeof obj.age !== 'number') {\n", "        throw new Error('age must be a number or undefined');\n", "    }\n", "    \n", "    return true;\n", "}\n", "\n", "// User factory with interface-like validation\n", "function createUser(userData) {\n", "    const user = {\n", "        id: userData.id,\n", "        name: userData.name,\n", "        email: userData.email,\n", "        age: userData.age,\n", "        createdAt: new Date(),\n", "        isActive: true\n", "    };\n", "    \n", "    validateUser(user);\n", "    return user;\n", "}\n", "\n", "// Test user creation\n", "const user1 = createUser({\n", "    id: 1,\n", "    name: '<PERSON>',\n", "    email: '<EMAIL>',\n", "    age: 30\n", "});\n", "\n", "console.log('Created user:', user1);\n", "\n", "// Service implementation with interface-like contract\n", "class UserService {\n", "    constructor() {\n", "        this.users = new Map();\n", "        this.nextId = 1;\n", "    }\n", "    \n", "    getUser(id) {\n", "        if (typeof id !== 'number') {\n", "            throw new Error('ID must be a number');\n", "        }\n", "        return this.users.get(id) || null;\n", "    }\n", "    \n", "    createUser(userData) {\n", "        const user = {\n", "            id: this.nextId++,\n", "            ...userData,\n", "            createdAt: new Date(),\n", "            isActive: true\n", "        };\n", "        \n", "        validateUser(user);\n", "        this.users.set(user.id, user);\n", "        return user;\n", "    }\n", "    \n", "    updateUser(id, updates) {\n", "        const user = this.getUser(id);\n", "        if (!user) {\n", "            throw new Error('User not found');\n", "        }\n", "        \n", "        const updatedUser = { ...user, ...updates };\n", "        validateUser(updatedUser);\n", "        this.users.set(id, updatedUser);\n", "        return updatedUser;\n", "    }\n", "    \n", "    deleteUser(id) {\n", "        return this.users.delete(id);\n", "    }\n", "}\n", "\n", "// Test service\n", "const userService = new UserService();\n", "\n", "const newUser = userService.createUser({\n", "    name: '<PERSON>',\n", "    email: '<EMAIL>',\n", "    age: 25\n", "});\n", "\n", "console.log('Service created user:', newUser);\n", "\n", "const updatedUser = userService.updateUser(newUser.id, {\n", "    age: 26,\n", "    isActive: false\n", "});\n", "\n", "console.log('Updated user:', updatedUser);\n", "\n", "// Simulating type aliases with constants\n", "console.log('\\n=== Type Aliases Simulation ===');\n", "\n", "const STATUS_VALUES = ['pending', 'approved', 'rejected'];\n", "const USER_ROLES = ['admin', 'user', 'moderator'];\n", "\n", "function validateStatus(status) {\n", "    if (!STATUS_VALUES.includes(status)) {\n", "        throw new Error(`Invalid status. Must be one of: ${STATUS_VALUES.join(', ')}`);\n", "    }\n", "    return true;\n", "}\n", "\n", "function validateUser<PERSON><PERSON>(role) {\n", "    if (!USER_ROLES.includes(role)) {\n", "        throw new Error(`Invalid role. Must be one of: ${USER_ROLES.join(', ')}`);\n", "    }\n", "    return true;\n", "}\n", "\n", "// API Response factory\n", "function createApiResponse(data, status = 200, message = 'Success') {\n", "    return {\n", "        data,\n", "        status,\n", "        message,\n", "        timestamp: new Date()\n", "    };\n", "}\n", "\n", "const apiResponse = createApiResponse(newUser, 201, 'User created successfully');\n", "console.log('API Response:', apiResponse);\n", "\n", "// Intersection type simulation\n", "function addTimestamps(obj) {\n", "    return {\n", "        ...obj,\n", "        createdAt: new Date(),\n", "        updatedAt: new Date()\n", "    };\n", "}\n", "\n", "const userWithTimestamps = addTimestamps({\n", "    id: 3,\n", "    name: '<PERSON>',\n", "    email: '<EMAIL>'\n", "});\n", "\n", "console.log('User with timestamps:', userWithTimestamps);\n", "\n", "console.log('\\nTypeScript interfaces provide compile-time contracts and better IDE support!');"]}, {"cell_type": "markdown", "id": "fa36a827", "metadata": {}, "source": ["# Generics {#generics}\n", "\n", "Generics allow you to create reusable components that work with multiple types while maintaining type safety."]}, {"cell_type": "code", "execution_count": null, "id": "56c306d3", "metadata": {}, "outputs": [], "source": ["// Generics in TypeScript\n", "console.log('=== Generics ===');\n", "\n", "/*\n", "// Basic generic function\n", "function identity<T>(arg: T): T {\n", "    return arg;\n", "}\n", "\n", "// Usage\n", "let stringResult = identity<string>(\"hello\");\n", "let numberResult = identity<number>(42);\n", "let boolResult = identity(true); // Type inference\n", "\n", "// Generic array function\n", "function getFirstElement<T>(arr: T[]): T | undefined {\n", "    return arr[0];\n", "}\n", "\n", "let firstString = getFirstElement([\"a\", \"b\", \"c\"]); // string | undefined\n", "let firstNumber = getFirstElement([1, 2, 3]); // number | undefined\n", "\n", "// Generic interface\n", "interface Container<T> {\n", "    value: T;\n", "    getValue(): T;\n", "    setValue(value: T): void;\n", "}\n", "\n", "// Generic class\n", "class Box<T> implements Container<T> {\n", "    constructor(public value: T) {}\n", "    \n", "    getValue(): T {\n", "        return this.value;\n", "    }\n", "    \n", "    setValue(value: T): void {\n", "        this.value = value;\n", "    }\n", "}\n", "\n", "let stringBox = new Box<string>(\"hello\");\n", "let numberBox = new Box<number>(42);\n", "\n", "// Generic constraints\n", "interface Lengthwise {\n", "    length: number;\n", "}\n", "\n", "function logLength<T extends Lengthwise>(arg: T): T {\n", "    console.log(arg.length);\n", "    return arg;\n", "}\n", "\n", "// Multiple type parameters\n", "function pair<T, U>(first: T, second: U): [T, U] {\n", "    return [first, second];\n", "}\n", "\n", "let stringNumberPair = pair(\"hello\", 42);\n", "let booleanArrayPair = pair(true, [1, 2, 3]);\n", "\n", "// Generic utility functions\n", "function map<T, U>(array: T[], fn: (item: T) => U): U[] {\n", "    return array.map(fn);\n", "}\n", "\n", "function filter<T>(array: T[], predicate: (item: T) => boolean): T[] {\n", "    return array.filter(predicate);\n", "}\n", "*/\n", "\n", "// JavaScript equivalents demonstrating generic-like patterns\n", "console.log('Simulating TypeScript generics in JavaScript:');\n", "\n", "// Generic-like identity function\n", "function identity(arg) {\n", "    return arg;\n", "}\n", "\n", "console.log('Identity string:', identity('hello'));\n", "console.log('Identity number:', identity(42));\n", "console.log('Identity boolean:', identity(true));\n", "\n", "// Generic-like array utilities\n", "function getFirstElement(arr) {\n", "    if (!Array.isArray(arr)) {\n", "        throw new Error('Argument must be an array');\n", "    }\n", "    return arr[0];\n", "}\n", "\n", "console.log('First string:', getFirstElement(['a', 'b', 'c']));\n", "console.log('First number:', getFirstElement([1, 2, 3]));\n", "\n", "// Generic-like container class\n", "class Box {\n", "    constructor(value) {\n", "        this.value = value;\n", "    }\n", "    \n", "    getValue() {\n", "        return this.value;\n", "    }\n", "    \n", "    setValue(value) {\n", "        this.value = value;\n", "    }\n", "    \n", "    // Type checking method\n", "    getType() {\n", "        return typeof this.value;\n", "    }\n", "}\n", "\n", "const stringBox = new Box('hello');\n", "const numberBox = new Box(42);\n", "const arrayBox = new Box([1, 2, 3]);\n", "\n", "console.log('String box:', stringBox.getValue(), 'Type:', stringBox.getType());\n", "console.log('Number box:', numberBox.getValue(), 'Type:', numberBox.getType());\n", "console.log('Array box:', arrayBox.getValue(), 'Type:', arrayBox.getType());\n", "\n", "// Generic-like utility functions\n", "function mapArray(array, transformFn) {\n", "    if (!Array.isArray(array)) {\n", "        throw new Error('First argument must be an array');\n", "    }\n", "    if (typeof transformFn !== 'function') {\n", "        throw new Error('Second argument must be a function');\n", "    }\n", "    return array.map(transformFn);\n", "}\n", "\n", "function filterArray(array, predicateFn) {\n", "    if (!Array.isArray(array)) {\n", "        throw new Error('First argument must be an array');\n", "    }\n", "    if (typeof predicateFn !== 'function') {\n", "        throw new Error('Second argument must be a function');\n", "    }\n", "    return array.filter(predicateFn);\n", "}\n", "\n", "// Test generic-like functions\n", "const numbers = [1, 2, 3, 4, 5];\n", "const strings = ['hello', 'world', 'typescript'];\n", "\n", "const doubled = mapArray(numbers, x => x * 2);\n", "const uppercased = mapArray(strings, s => s.toUpperCase());\n", "const evenNumbers = filterArray(numbers, x => x % 2 === 0);\n", "const longStrings = filterArray(strings, s => s.length > 5);\n", "\n", "console.log('\\nGeneric-like operations:');\n", "console.log('Doubled numbers:', doubled);\n", "console.log('Uppercased strings:', uppercased);\n", "console.log('Even numbers:', evenNumbers);\n", "console.log('Long strings:', longStrings);\n", "\n", "// Pair function (multiple type parameters)\n", "function pair(first, second) {\n", "    return [first, second];\n", "}\n", "\n", "const stringNumberPair = pair('hello', 42);\n", "const booleanArrayPair = pair(true, [1, 2, 3]);\n", "\n", "console.log('\\nPairs:');\n", "console.log('String-Number pair:', stringNumberPair);\n", "console.log('Boolean-Array pair:', booleanArrayPair);\n", "\n", "// Generic-like API response wrapper\n", "function createResponse(data, success = true, message = 'Success') {\n", "    return {\n", "        data,\n", "        success,\n", "        message,\n", "        timestamp: new Date().toISOString(),\n", "        dataType: Array.isArray(data) ? 'array' : typeof data\n", "    };\n", "}\n", "\n", "const userResponse = createResponse({ id: 1, name: '<PERSON>' });\n", "const usersResponse = createResponse([{ id: 1 }, { id: 2 }]);\n", "const errorResponse = createResponse(null, false, 'User not found');\n", "\n", "console.log('\\nAPI Responses:');\n", "console.log('User response:', userResponse);\n", "console.log('Users response:', usersResponse);\n", "console.log('Error response:', errorResponse);\n", "\n", "// Constraint simulation\n", "function logLength(arg) {\n", "    if (typeof arg.length !== 'number') {\n", "        throw new Error('Argument must have a length property');\n", "    }\n", "    console.log('Length:', arg.length);\n", "    return arg;\n", "}\n", "\n", "console.log('\\nLength logging:');\n", "logLength('hello');\n", "logLength([1, 2, 3, 4]);\n", "logLength({ length: 10, data: 'custom' });\n", "\n", "console.log('\\nTypeScript generics provide type safety while maintaining flexibility!');"]}, {"cell_type": "markdown", "id": "dfa02e04", "metadata": {}, "source": ["# Advanced Types and Utility Types {#advanced}\n", "\n", "TypeScript provides powerful advanced type features including conditional types, mapped types, and built-in utility types."]}, {"cell_type": "code", "execution_count": null, "id": "9440a6fd", "metadata": {}, "outputs": [], "source": ["// Advanced Types and Utility Types\n", "console.log('=== Advanced Types and Utility Types ===');\n", "\n", "/*\n", "// Conditional Types\n", "type IsString<T> = T extends string ? true : false;\n", "type Test1 = IsString<string>; // true\n", "type Test2 = IsString<number>; // false\n", "\n", "// Mapped Types\n", "type Readonly<T> = {\n", "    readonly [P in keyof T]: T[P];\n", "};\n", "\n", "type Optional<T> = {\n", "    [P in keyof T]?: T[P];\n", "};\n", "\n", "// Built-in Utility Types\n", "interface User {\n", "    id: number;\n", "    name: string;\n", "    email: string;\n", "    age: number;\n", "    isActive: boolean;\n", "}\n", "\n", "// Partial - makes all properties optional\n", "type PartialUser = Partial<User>;\n", "// { id?: number; name?: string; email?: string; age?: number; isActive?: boolean; }\n", "\n", "// Required - makes all properties required\n", "type RequiredUser = Required<User>;\n", "\n", "// Pick - select specific properties\n", "type UserSummary = Pick<User, 'id' | 'name' | 'email'>;\n", "// { id: number; name: string; email: string; }\n", "\n", "// Omit - exclude specific properties\n", "type CreateUserData = Omit<User, 'id'>;\n", "// { name: string; email: string; age: number; isActive: boolean; }\n", "\n", "// Record - create object type with specific keys and values\n", "type UserRoles = Record<'admin' | 'user' | 'moderator', string[]>;\n", "// { admin: string[]; user: string[]; moderator: string[]; }\n", "\n", "// Exclude - exclude types from union\n", "type Status = 'pending' | 'approved' | 'rejected' | 'cancelled';\n", "type ActiveStatus = Exclude<Status, 'cancelled'>;\n", "// 'pending' | 'approved' | 'rejected'\n", "\n", "// Extract - extract types from union\n", "type CompletedStatus = Extract<Status, 'approved' | 'rejected'>;\n", "// 'approved' | 'rejected'\n", "\n", "// NonNullable - exclude null and undefined\n", "type NonNullableString = NonNullable<string | null | undefined>;\n", "// string\n", "\n", "// ReturnType - get return type of function\n", "function getUser(): User { return {} as User; }\n", "type GetUserReturn = ReturnType<typeof getUser>; // User\n", "\n", "// Parameters - get parameter types of function\n", "function updateUser(id: number, data: Partial<User>): User { return {} as User; }\n", "type UpdateUserParams = Parameters<typeof updateUser>; // [number, Partial<User>]\n", "\n", "// Template Literal Types (TypeScript 4.1+)\n", "type EventName<T extends string> = `on${Capitalize<T>}`;\n", "type ClickEvent = EventName<'click'>; // 'onClick'\n", "type HoverEvent = EventName<'hover'>; // 'onHover'\n", "\n", "// Recursive Types\n", "type DeepReadonly<T> = {\n", "    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];\n", "};\n", "*/\n", "\n", "// JavaScript equivalents and demonstrations\n", "console.log('Demonstrating utility type concepts in JavaScript:');\n", "\n", "// Sample user object\n", "const sampleUser = {\n", "    id: 1,\n", "    name: '<PERSON>',\n", "    email: '<EMAIL>',\n", "    age: 30,\n", "    isActive: true\n", "};\n", "\n", "// Partial utility simulation\n", "function createPartialUser(updates) {\n", "    // In TypeScript, Partial<User> makes all properties optional\n", "    const allowedKeys = ['id', 'name', 'email', 'age', 'isActive'];\n", "    const partial = {};\n", "    \n", "    for (const [key, value] of Object.entries(updates)) {\n", "        if (allowedKeys.includes(key)) {\n", "            partial[key] = value;\n", "        }\n", "    }\n", "    \n", "    return partial;\n", "}\n", "\n", "const userUpdate = createPartialUser({ name: '<PERSON>', age: 31 });\n", "console.log('Partial user update:', userUpdate);\n", "\n", "// Pick utility simulation\n", "function pickProperties(obj, keys) {\n", "    const picked = {};\n", "    for (const key of keys) {\n", "        if (key in obj) {\n", "            picked[key] = obj[key];\n", "        }\n", "    }\n", "    return picked;\n", "}\n", "\n", "const userSummary = pickProperties(sampleUser, ['id', 'name', 'email']);\n", "console.log('User summary (Pick):', userSummary);\n", "\n", "// Omit utility simulation\n", "function omitProperties(obj, keys) {\n", "    const result = { ...obj };\n", "    for (const key of keys) {\n", "        delete result[key];\n", "    }\n", "    return result;\n", "}\n", "\n", "const createUserData = omitProperties(sampleUser, ['id']);\n", "console.log('Create user data (Omit):', createUserData);\n", "\n", "// Record utility simulation\n", "function createRecord(keys, valueFactory) {\n", "    const record = {};\n", "    for (const key of keys) {\n", "        record[key] = typeof valueFactory === 'function' ? valueFactory(key) : valueFactory;\n", "    }\n", "    return record;\n", "}\n", "\n", "const userRoles = createRecord(\n", "    ['admin', 'user', 'moderator'],\n", "    (role) => role === 'admin' ? ['read', 'write', 'delete'] : ['read']\n", ");\n", "console.log('User roles (Record):', userRoles);\n", "\n", "// Readonly utility simulation\n", "function makeReadonly(obj) {\n", "    return Object.freeze({ ...obj });\n", "}\n", "\n", "const readonlyUser = makeReadonly(sampleUser);\n", "console.log('Readonly user:', readonlyUser);\n", "\n", "try {\n", "    readonlyUser.name = '<PERSON>'; // This will fail silently or throw in strict mode\n", "} catch (error) {\n", "    console.log('Cannot modify readonly object');\n", "}\n", "\n", "// Deep readonly simulation\n", "function makeDeepReadonly(obj) {\n", "    if (obj === null || typeof obj !== 'object') {\n", "        return obj;\n", "    }\n", "    \n", "    const result = Array.isArray(obj) ? [] : {};\n", "    \n", "    for (const [key, value] of Object.entries(obj)) {\n", "        result[key] = makeDeepReadonly(value);\n", "    }\n", "    \n", "    return Object.freeze(result);\n", "}\n", "\n", "const nestedUser = {\n", "    ...sampleUser,\n", "    preferences: {\n", "        theme: 'dark',\n", "        notifications: true\n", "    },\n", "    tags: ['developer', 'typescript']\n", "};\n", "\n", "const deepReadonlyUser = makeDeepReadonly(nestedUser);\n", "console.log('Deep readonly user:', deepReadonlyUser);\n", "\n", "// Function type utilities simulation\n", "function getReturnType(fn) {\n", "    // This is a runtime check, TypeScript does this at compile time\n", "    try {\n", "        const result = fn();\n", "        return typeof result;\n", "    } catch {\n", "        return 'unknown';\n", "    }\n", "}\n", "\n", "function getParameterCount(fn) {\n", "    return fn.length;\n", "}\n", "\n", "function sampleFunction(a, b, c) {\n", "    return a + b + c;\n", "}\n", "\n", "console.log('\\nFunction analysis:');\n", "console.log('Parameter count:', getParameterCount(sampleFunction));\n", "console.log('Return type:', getReturnType(() => 'hello'));\n", "\n", "// Template literal simulation\n", "function createEventName(eventType) {\n", "    return `on${eventType.charAt(0).toUpperCase()}${eventType.slice(1)}`;\n", "}\n", "\n", "console.log('\\nEvent names:');\n", "console.log('Click event:', createEventName('click'));\n", "console.log('Hover event:', createEventName('hover'));\n", "console.log('Focus event:', createEventName('focus'));\n", "\n", "// Conditional type simulation\n", "function isStringType(value) {\n", "    return typeof value === 'string';\n", "}\n", "\n", "function processValue(value) {\n", "    if (isStringType(value)) {\n", "        return value.toUpperCase();\n", "    } else if (typeof value === 'number') {\n", "        return value * 2;\n", "    } else {\n", "        return String(value);\n", "    }\n", "}\n", "\n", "console.log('\\nConditional processing:');\n", "console.log('String:', processValue('hello'));\n", "console.log('Number:', processValue(42));\n", "console.log('Boolean:', processValue(true));\n", "\n", "console.log('\\nTypeScript utility types provide powerful type transformations at compile time!');"]}, {"cell_type": "markdown", "id": "bc9108b3", "metadata": {}, "source": ["# Configuration and Best Practices {#config}\n", "\n", "Proper TypeScript configuration and following best practices ensures optimal development experience and code quality."]}, {"cell_type": "code", "execution_count": null, "id": "e5eb596d", "metadata": {}, "outputs": [], "source": ["// TypeScript Configuration and Best Practices\n", "console.log('=== TypeScript Configuration and Best Practices ===');\n", "\n", "/*\n", "// tsconfig.json - TypeScript configuration file\n", "{\n", "  \"compilerOptions\": {\n", "    // Target JavaScript version\n", "    \"target\": \"ES2020\",\n", "    \n", "    // Module system\n", "    \"module\": \"commonjs\",\n", "    \"moduleResolution\": \"node\",\n", "    \n", "    // Output settings\n", "    \"outDir\": \"./dist\",\n", "    \"rootDir\": \"./src\",\n", "    \"sourceMap\": true,\n", "    \"declaration\": true,\n", "    \n", "    // Type checking\n", "    \"strict\": true,\n", "    \"noImplicitAny\": true,\n", "    \"strictNullChecks\": true,\n", "    \"strictFunctionTypes\": true,\n", "    \"noImplicitReturns\": true,\n", "    \"noUnusedLocals\": true,\n", "    \"noUnusedParameters\": true,\n", "    \n", "    // ES features\n", "    \"experimentalDecorators\": true,\n", "    \"emitDecoratorMetadata\": true,\n", "    \n", "    // Library support\n", "    \"lib\": [\"ES2020\", \"DOM\"],\n", "    \"esModuleInterop\": true,\n", "    \"allowSyntheticDefaultImports\": true,\n", "    \"skipLib<PERSON>heck\": true\n", "  },\n", "  \"include\": [\n", "    \"src/**/*\"\n", "  ],\n", "  \"exclude\": [\n", "    \"node_modules\",\n", "    \"dist\",\n", "    \"**/*.test.ts\"\n", "  ]\n", "}\n", "\n", "// Package.json scripts\n", "{\n", "  \"scripts\": {\n", "    \"build\": \"tsc\",\n", "    \"build:watch\": \"tsc --watch\",\n", "    \"start\": \"node dist/index.js\",\n", "    \"dev\": \"ts-node src/index.ts\",\n", "    \"type-check\": \"tsc --noEmit\",\n", "    \"lint\": \"eslint src/**/*.ts\"\n", "  }\n", "}\n", "*/\n", "\n", "// Best Practices Demonstrations\n", "console.log('TypeScript Best Practices:');\n", "\n", "// 1. Use strict type checking\n", "console.log('\\n1. Strict Type Checking');\n", "console.log('✓ Enable strict mode in tsconfig.json');\n", "console.log('✓ Use noImplicitAny to catch missing type annotations');\n", "console.log('✓ Enable strictNullChecks to handle null/undefined safely');\n", "\n", "// 2. Prefer interfaces over type aliases for object shapes\n", "console.log('\\n2. Interfaces vs Type Aliases');\n", "console.log('✓ Use interfaces for object shapes (extensible)');\n", "console.log('✓ Use type aliases for unions, primitives, and computed types');\n", "\n", "/*\n", "// Good: Interface for object shape\n", "interface User {\n", "    id: number;\n", "    name: string;\n", "}\n", "\n", "// Good: Type alias for union\n", "type Status = 'pending' | 'approved' | 'rejected';\n", "\n", "// Good: Type alias for computed type\n", "type UserKeys = keyof User;\n", "*/\n", "\n", "// 3. Use meaningful names and avoid 'any'\n", "console.log('\\n3. Type Safety');\n", "console.log('✓ Avoid \"any\" type - use \"unknown\" instead');\n", "console.log('✓ Use type assertions sparingly and safely');\n", "console.log('✓ Prefer type guards for runtime type checking');\n", "\n", "// Type guard examples\n", "function isString(value) {\n", "    return typeof value === 'string';\n", "}\n", "\n", "function isNumber(value) {\n", "    return typeof value === 'number' && !isNaN(value);\n", "}\n", "\n", "function isUser(obj) {\n", "    return obj && \n", "           typeof obj === 'object' &&\n", "           typeof obj.id === 'number' &&\n", "           typeof obj.name === 'string';\n", "}\n", "\n", "// Safe type checking\n", "function processUnknownValue(value) {\n", "    if (isString(value)) {\n", "        return value.toUpperCase();\n", "    } else if (isNumber(value)) {\n", "        return value.toFixed(2);\n", "    } else if (isUser(value)) {\n", "        return `User: ${value.name} (ID: ${value.id})`;\n", "    } else {\n", "        return 'Unknown type';\n", "    }\n", "}\n", "\n", "console.log('Processing string:', processUnknownValue('hello'));\n", "console.log('Processing number:', processUnknownValue(42.567));\n", "console.log('Processing user:', processUnknownValue({ id: 1, name: '<PERSON>' }));\n", "console.log('Processing unknown:', processUnknownValue(true));\n", "\n", "// 4. Use readonly for immutable data\n", "console.log('\\n4. Immutability');\n", "console.log('✓ Use readonly for properties that shouldn\\'t change');\n", "console.log('✓ Use const assertions for literal types');\n", "\n", "// Simulating readonly behavior\n", "function createImmutableConfig(config) {\n", "    return Object.freeze({ ...config });\n", "}\n", "\n", "const appConfig = createImmutableConfig({\n", "    apiUrl: 'https://api.example.com',\n", "    timeout: 5000,\n", "    retries: 3\n", "});\n", "\n", "console.log('Immutable config:', appConfig);\n", "\n", "// 5. Use utility types effectively\n", "console.log('\\n5. Utility Types');\n", "console.log('✓ Use Partial<T> for optional updates');\n", "console.log('✓ Use Pick<T, K> to select specific properties');\n", "console.log('✓ Use Omit<T, K> to exclude properties');\n", "\n", "// 6. Organize code with modules\n", "console.log('\\n6. Module Organization');\n", "console.log('✓ Use barrel exports (index.ts files)');\n", "console.log('✓ Group related types and functions');\n", "console.log('✓ Use path mapping in tsconfig.json');\n", "\n", "/*\n", "// Example barrel export (index.ts)\n", "export { User, CreateUserData } from './types/user';\n", "export { UserService } from './services/user-service';\n", "export { validateUser } from './utils/validation';\n", "\n", "// Path mapping in tsconfig.json\n", "{\n", "  \"compilerOptions\": {\n", "    \"baseUrl\": \"./src\",\n", "    \"paths\": {\n", "      \"@types/*\": [\"types/*\"],\n", "      \"@services/*\": [\"services/*\"],\n", "      \"@utils/*\": [\"utils/*\"]\n", "    }\n", "  }\n", "}\n", "*/\n", "\n", "// 7. Error handling patterns\n", "console.log('\\n7. Error Handling');\n", "console.log('✓ Use Result/Either patterns for error handling');\n", "console.log('✓ Create specific error types');\n", "console.log('✓ Use discriminated unions for error states');\n", "\n", "// Result pattern simulation\n", "function createResult(success, data, error) {\n", "    if (success) {\n", "        return { success: true, data, error: null };\n", "    } else {\n", "        return { success: false, data: null, error };\n", "    }\n", "}\n", "\n", "function divideNumbers(a, b) {\n", "    if (b === 0) {\n", "        return createResult(false, null, 'Division by zero');\n", "    }\n", "    return createResult(true, a / b, null);\n", "}\n", "\n", "const result1 = divideNumbers(10, 2);\n", "const result2 = divideNumbers(10, 0);\n", "\n", "console.log('Division result 1:', result1);\n", "console.log('Division result 2:', result2);\n", "\n", "// 8. Performance considerations\n", "console.log('\\n8. Performance');\n", "console.log('✓ Use skipLibCheck for faster compilation');\n", "console.log('✓ Use incremental compilation');\n", "console.log('✓ Avoid deep type recursion');\n", "console.log('✓ Use type-only imports when possible');\n", "\n", "/*\n", "// Type-only imports\n", "import type { User } from './types';\n", "import { createUser } from './services'; // Regular import for runtime\n", "*/\n", "\n", "// Common pitfalls to avoid\n", "console.log('\\n=== Common Pitfalls to Avoid ===');\n", "console.log('❌ Using \"any\" everywhere');\n", "console.log('❌ Ignoring TypeScript errors with @ts-ignore');\n", "console.log('❌ Not enabling strict mode');\n", "console.log('❌ Using type assertions instead of type guards');\n", "console.log('❌ Not handling null/undefined properly');\n", "console.log('❌ Creating overly complex types');\n", "console.log('❌ Not using utility types when appropriate');\n", "\n", "console.log('\\n✅ TypeScript best practices lead to more maintainable and reliable code!');"]}], "metadata": {"kernelspec": {"display_name": "JavaScript (Node.js)", "language": "javascript", "name": "javascript"}, "language_info": {"file_extension": ".js", "mimetype": "application/javascript", "name": "javascript", "version": "18.0.0"}}, "nbformat": 4, "nbformat_minor": 5}