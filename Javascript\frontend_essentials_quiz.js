/**
 * Front-End Essentials Quiz
 * Comprehensive quiz covering HTML5, CSS3, responsive design, and modern web standards
 */

const quizzes = {
  "HTML5 Fundamentals": [
    { 
      question: "What is the purpose of the DOCTYPE declaration in HTML5?", 
      answer: "Tells the browser to use HTML5 standards mode for rendering the document." 
    },
    { 
      question: "What are the new semantic elements in HTML5?", 
      answer: "header, nav, main, article, section, aside, footer, figure, figcaption, time, mark." 
    },
    { 
      question: "What's the difference between <article> and <section>?", 
      answer: "Article is independent, self-contained content; section is a thematic grouping within content." 
    },
    { 
      question: "What new input types does HTML5 provide?", 
      answer: "email, url, tel, number, range, date, time, color, search, and more with built-in validation." 
    },
    { 
      question: "What is the purpose of the <canvas> element?", 
      answer: "Provides a drawing surface for graphics, animations, and interactive content using JavaScript." 
    },
    { 
      question: "How do you make HTML5 forms accessible?", 
      answer: "Use proper labels, fieldsets, ARIA attributes, semantic input types, and validation messages." 
    }
  ],

  "CSS3 Fundamentals": [
    { 
      question: "What are CSS3 selectors?", 
      answer: "Advanced selectors like :nth-child(), :not(), [attribute^=value], ::before, ::after for precise targeting." 
    },
    { 
      question: "What is the CSS box model?", 
      answer: "Content, padding, border, margin layers that determine element size and spacing." 
    },
    { 
      question: "What's the difference between margin and padding?", 
      answer: "Margin is space outside the border; padding is space inside the border around content." 
    },
    { 
      question: "What are CSS custom properties (variables)?", 
      answer: "Reusable values defined with -- prefix: --main-color: blue; used with var(--main-color)." 
    },
    { 
      question: "What is specificity in CSS?", 
      answer: "System that determines which CSS rules apply when multiple rules target the same element." 
    },
    { 
      question: "How does CSS inheritance work?", 
      answer: "Child elements inherit certain properties from parents unless overridden." 
    }
  ],

  "CSS Layout Systems": [
    { 
      question: "What is CSS Flexbox?", 
      answer: "One-dimensional layout method for arranging items in rows or columns with flexible sizing." 
    },
    { 
      question: "What's the difference between justify-content and align-items?", 
      answer: "justify-content aligns along main axis; align-items aligns along cross axis." 
    },
    { 
      question: "What is CSS Grid?", 
      answer: "Two-dimensional layout system for creating complex layouts with rows and columns." 
    },
    { 
      question: "When should you use Flexbox vs Grid?", 
      answer: "Flexbox for one-dimensional layouts; Grid for two-dimensional layouts and complex designs." 
    },
    { 
      question: "What are CSS positioning values?", 
      answer: "static (default), relative, absolute, fixed, sticky - control element positioning context." 
    },
    { 
      question: "What is the difference between block and inline elements?", 
      answer: "Block elements take full width and stack vertically; inline elements flow horizontally." 
    }
  ],

  "Responsive Design": [
    { 
      question: "What is responsive web design?", 
      answer: "Design approach that makes web pages render well on various devices and screen sizes." 
    },
    { 
      question: "What are CSS media queries?", 
      answer: "CSS technique to apply styles based on device characteristics like screen width, height, orientation." 
    },
    { 
      question: "What is mobile-first design?", 
      answer: "Design approach starting with mobile layout and progressively enhancing for larger screens." 
    },
    { 
      question: "What are CSS viewport units?", 
      answer: "vw (viewport width), vh (viewport height), vmin, vmax for sizing relative to viewport." 
    },
    { 
      question: "What is the viewport meta tag?", 
      answer: "<meta name='viewport' content='width=device-width, initial-scale=1'> for proper mobile rendering." 
    },
    { 
      question: "What are common responsive breakpoints?", 
      answer: "Mobile: 320-768px, Tablet: 768-1024px, Desktop: 1024px+, but should be content-driven." 
    }
  ],

  "CSS Animations and Transitions": [
    { 
      question: "What's the difference between CSS transitions and animations?", 
      answer: "Transitions animate between two states; animations can have multiple keyframes and loop." 
    },
    { 
      question: "What properties can you animate with CSS?", 
      answer: "Most numeric properties: opacity, transform, colors, dimensions, positions." 
    },
    { 
      question: "What are CSS transform functions?", 
      answer: "translate(), rotate(), scale(), skew() for moving, rotating, scaling, and skewing elements." 
    },
    { 
      question: "What is the will-change property?", 
      answer: "Hints to browser about upcoming changes to optimize rendering performance." 
    },
    { 
      question: "What are animation timing functions?", 
      answer: "ease, linear, ease-in, ease-out, ease-in-out, cubic-bezier() for animation pacing." 
    },
    { 
      question: "How do you create smooth 60fps animations?", 
      answer: "Use transform and opacity properties, avoid layout-triggering properties, use will-change." 
    }
  ],

  "Modern CSS Features": [
    { 
      question: "What are CSS logical properties?", 
      answer: "Properties that adapt to writing direction: margin-inline-start instead of margin-left." 
    },
    { 
      question: "What is CSS container queries?", 
      answer: "Responsive design based on container size rather than viewport size." 
    },
    { 
      question: "What are CSS cascade layers?", 
      answer: "@layer rule for controlling specificity and cascade order of stylesheets." 
    },
    { 
      question: "What is CSS subgrid?", 
      answer: "Allows grid items to inherit grid lines from their parent grid container." 
    },
    { 
      question: "What are CSS color functions?", 
      answer: "hsl(), rgb(), lab(), lch(), color() for advanced color manipulation." 
    },
    { 
      question: "What is CSS aspect-ratio property?", 
      answer: "Sets preferred aspect ratio for elements: aspect-ratio: 16/9;" 
    }
  ],

  "Web Performance": [
    { 
      question: "What are Core Web Vitals?", 
      answer: "LCP (Largest Contentful Paint), FID (First Input Delay), CLS (Cumulative Layout Shift)." 
    },
    { 
      question: "How do you optimize CSS for performance?", 
      answer: "Minimize CSS, remove unused styles, use efficient selectors, avoid @import, inline critical CSS." 
    },
    { 
      question: "What is critical CSS?", 
      answer: "Above-the-fold CSS inlined in HTML to prevent render-blocking and improve perceived performance." 
    },
    { 
      question: "How do you optimize images for web?", 
      answer: "Use appropriate formats (WebP, AVIF), responsive images, lazy loading, proper sizing." 
    },
    { 
      question: "What is lazy loading?", 
      answer: "Technique to defer loading of non-critical resources until they're needed." 
    },
    { 
      question: "What are web fonts optimization techniques?", 
      answer: "Font-display: swap, preload important fonts, subset fonts, use system fonts as fallback." 
    }
  ],

  "Accessibility": [
    { 
      question: "What is web accessibility (a11y)?", 
      answer: "Making web content usable by people with disabilities through proper markup and design." 
    },
    { 
      question: "What are ARIA attributes?", 
      answer: "Accessible Rich Internet Applications attributes that provide semantic information to assistive technologies." 
    },
    { 
      question: "What is semantic HTML?", 
      answer: "Using HTML elements for their intended meaning rather than appearance." 
    },
    { 
      question: "How do you make forms accessible?", 
      answer: "Use proper labels, fieldsets, error messages, keyboard navigation, and ARIA attributes." 
    },
    { 
      question: "What is color contrast and why is it important?", 
      answer: "Sufficient contrast between text and background colors for readability by users with visual impairments." 
    },
    { 
      question: "How do you test for accessibility?", 
      answer: "Use screen readers, keyboard navigation, automated tools (axe, Lighthouse), manual testing." 
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n🎨 ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available Front-End Quiz Topics:');
  console.log('='.repeat(37));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM FRONT-END QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) || 
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });
  
  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }
  
  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));
  
  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });
  
  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    quizzes, 
    printQuiz, 
    listTopics, 
    randomQuiz, 
    searchQuestions 
  };
}

// Example usage and help
console.log('🎨 Front-End Essentials Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('🔍 searchQuestions("keyword") - Search for questions');

console.log('\n💡 Example: printQuiz("HTML5 Fundamentals")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("responsive", true) - Find responsive design questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
