/**
 * Vue.js Manual Quiz
 * Comprehensive quiz covering all sections of the Vue.js programming manual
 */

const quizzes = {
  "Vue.js Fundamentals": [
    { 
      question: "What is Vue.js?", 
      answer: "A progressive JavaScript framework for building user interfaces that can be incrementally adopted." 
    },
    { 
      question: "What does 'progressive framework' mean?", 
      answer: "You can adopt Vue incrementally - start with just the view layer and gradually add more features as needed." 
    },
    { 
      question: "What are the core features of Vue.js?", 
      answer: "Reactive data binding, component-based architecture, template syntax with directives, and single file components." 
    },
    { 
      question: "What's the difference between Vue 2 and Vue 3?", 
      answer: "Vue 3 has Composition API, better performance, TypeScript support, multiple root elements, and improved tree-shaking." 
    },
    { 
      question: "What is the Vue instance?", 
      answer: "The root of every Vue application, created with createApp() in Vue 3 or new Vue() in Vue 2." 
    },
    { 
      question: "What are Single File Components?", 
      answer: ".vue files that contain template, script, and style in one file with scoped styling." 
    }
  ],

  "Template Syntax and Directives": [
    { 
      question: "What is Vue's template syntax?", 
      answer: "HTML-based template syntax with interpolation {{ }}, directives (v-), and event listeners (@)." 
    },
    { 
      question: "What does v-model do?", 
      answer: "Creates two-way data binding between form inputs and component data." 
    },
    { 
      question: "What's the difference between v-if and v-show?", 
      answer: "v-if conditionally renders elements (DOM manipulation); v-show toggles CSS display property." 
    },
    { 
      question: "What does v-for do and why do you need keys?", 
      answer: "Renders lists of elements. Keys help Vue track changes for efficient updates and avoid rendering issues." 
    },
    { 
      question: "What is v-bind used for?", 
      answer: "Dynamically binds attributes to expressions. Shorthand is : (colon)." 
    },
    { 
      question: "What does v-on do?", 
      answer: "Listens to DOM events and runs JavaScript when triggered. Shorthand is @ symbol." 
    },
    { 
      question: "What are event modifiers?", 
      answer: "Postfixes for v-on that modify event behavior: .prevent, .stop, .once, .capture, etc." 
    }
  ],

  "Components and Props": [
    { 
      question: "How do you define a Vue component?", 
      answer: "Using defineComponent() or object with template, data, methods, etc. In SFC: <template>, <script>, <style>." 
    },
    { 
      question: "What are props in Vue?", 
      answer: "Custom attributes for passing data from parent to child components." 
    },
    { 
      question: "How do you validate props?", 
      answer: "Define props as object with type, required, default, and validator properties." 
    },
    { 
      question: "What is prop drilling and how do you solve it?", 
      answer: "Passing props through multiple levels. Solve with provide/inject, Vuex, or Pinia." 
    },
    { 
      question: "What are slots in Vue?", 
      answer: "Content distribution outlets that allow parent components to pass content to child components." 
    },
    { 
      question: "What's the difference between named slots and scoped slots?", 
      answer: "Named slots have specific names; scoped slots pass data from child to parent template." 
    }
  ],

  "Reactivity and Data": [
    { 
      question: "How does Vue's reactivity system work?", 
      answer: "Uses Proxy (Vue 3) or Object.defineProperty (Vue 2) to track dependencies and trigger updates." 
    },
    { 
      question: "What is the data option in Vue?", 
      answer: "Function that returns an object containing the component's reactive state." 
    },
    { 
      question: "What are reactive references (ref)?", 
      answer: "Vue 3 Composition API function that makes primitive values reactive. Access with .value." 
    },
    { 
      question: "What's the difference between ref and reactive?", 
      answer: "ref works with primitives and objects (.value access); reactive only works with objects (direct access)." 
    },
    { 
      question: "What is nextTick used for?", 
      answer: "Waits for the next DOM update cycle, useful for accessing updated DOM after data changes." 
    },
    { 
      question: "What are Vue's reactivity caveats?", 
      answer: "In Vue 2: can't detect property addition/deletion or array index assignment. Vue 3 fixes these with Proxy." 
    }
  ],

  "Computed Properties and Watchers": [
    { 
      question: "What are computed properties?", 
      answer: "Cached reactive values based on dependencies. Only re-evaluate when dependencies change." 
    },
    { 
      question: "When should you use computed vs methods?", 
      answer: "Computed for derived state with caching; methods for actions or when you need to pass parameters." 
    },
    { 
      question: "What are watchers in Vue?", 
      answer: "Functions that react to data changes, useful for async operations or expensive operations." 
    },
    { 
      question: "What's the difference between immediate and deep watchers?", 
      answer: "immediate runs watcher immediately; deep watches nested object properties." 
    },
    { 
      question: "How do you watch multiple values?", 
      answer: "Use array syntax in watch or computed that depends on multiple reactive values." 
    },
    { 
      question: "What is watchEffect in Vue 3?", 
      answer: "Automatically tracks reactive dependencies and runs effect when they change." 
    }
  ],

  "Lifecycle Hooks": [
    { 
      question: "What are Vue lifecycle hooks?", 
      answer: "Methods called at specific stages of component lifecycle: creation, mounting, updating, destruction." 
    },
    { 
      question: "What's the order of Vue lifecycle hooks?", 
      answer: "beforeCreate, created, beforeMount, mounted, beforeUpdate, updated, beforeUnmount, unmounted." 
    },
    { 
      question: "When should you use mounted vs created?", 
      answer: "created for data initialization; mounted for DOM access and third-party library integration." 
    },
    { 
      question: "What is beforeUnmount used for?", 
      answer: "Cleanup before component destruction: clear timers, remove event listeners, cancel requests." 
    },
    { 
      question: "How do lifecycle hooks work in Composition API?", 
      answer: "Use onMounted, onUpdated, onUnmounted functions inside setup()." 
    },
    { 
      question: "What's the difference between beforeUpdate and updated?", 
      answer: "beforeUpdate runs before DOM re-render; updated runs after DOM has been updated." 
    }
  ],

  "Composition API": [
    { 
      question: "What is the Composition API?", 
      answer: "Vue 3 feature for organizing component logic using functions instead of options object." 
    },
    { 
      question: "What is the setup() function?", 
      answer: "Entry point for Composition API where you define reactive state, computed, methods, and lifecycle hooks." 
    },
    { 
      question: "What does setup() return?", 
      answer: "Object with properties/methods to expose to template, or render function." 
    },
    { 
      question: "What are composables?", 
      answer: "Reusable functions that encapsulate stateful logic using Composition API." 
    },
    { 
      question: "How do you access props in setup()?", 
      answer: "First parameter of setup function: setup(props, context)" 
    },
    { 
      question: "What's in the context parameter of setup()?", 
      answer: "Object with attrs, slots, emit, and expose properties." 
    },
    { 
      question: "When should you use Composition API vs Options API?", 
      answer: "Composition API for complex logic, reusability, TypeScript; Options API for simpler components." 
    }
  ],

  "Vue Router": [
    { 
      question: "What is Vue Router?", 
      answer: "Official routing library for Vue.js that enables single-page application navigation." 
    },
    { 
      question: "How do you define routes?", 
      answer: "Array of route objects with path, component, and optional name, props, meta properties." 
    },
    { 
      question: "What are dynamic routes?", 
      answer: "Routes with parameters like /user/:id that match multiple paths with different parameter values." 
    },
    { 
      question: "How do you navigate programmatically?", 
      answer: "Use router.push(), router.replace(), or router.go() methods." 
    },
    { 
      question: "What are navigation guards?", 
      answer: "Functions that control navigation: beforeEach, beforeResolve, afterEach, beforeEnter, beforeRouteUpdate." 
    },
    { 
      question: "What's the difference between router-link and programmatic navigation?", 
      answer: "router-link for declarative navigation in templates; programmatic for conditional/dynamic navigation." 
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n🟢 ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available Vue.js Quiz Topics:');
  console.log('='.repeat(34));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM VUE.JS QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) || 
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });
  
  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }
  
  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));
  
  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });
  
  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    quizzes, 
    printQuiz, 
    listTopics, 
    randomQuiz, 
    searchQuestions 
  };
}

// Example usage and help
console.log('🟢 Vue.js Manual Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('🔍 searchQuestions("keyword") - Search for questions');

console.log('\n💡 Example: printQuiz("Vue.js Fundamentals")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("reactive", true) - Find reactivity-related questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
