{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive Python SQL and Database Interaction Manual\n", "\n", "This manual covers essential database operations in Python, from basic SQL execution to advanced ORM usage. Master database connectivity, query optimization, and data pipeline integration.\n", "\n", "## Table of Contents\n", "1. [Database Fundamentals](#fundamentals)\n", "2. [SQLite with Python](#sqlite)\n", "3. [PostgreSQL Integration](#postgresql)\n", "4. [SQLAlchemy ORM](#sqlalchemy)\n", "5. [Query Optimization](#optimization)\n", "6. [Connection Management](#connections)\n", "7. [Transactions and Error Handling](#transactions)\n", "8. [Database Migrations](#migrations)\n", "9. [Data Pipeline Integration](#pipelines)\n", "10. [Security Best Practices](#security)\n", "11. [Performance Monitoring](#monitoring)\n", "12. [Best Practices](#best-practices)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# Database Fundamentals {#fundamentals}\n", "\n", "Understanding database concepts, SQL basics, and how Python interacts with different database systems."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["# Database Fundamentals\n", "print('=== Database Fundamentals ===')\n", "\n", "# Database types and Python libraries\n", "databases = {\n", "    'SQLite': {\n", "        'description': 'Lightweight, file-based database',\n", "        'library': 'sqlite3 (built-in)',\n", "        'use_cases': 'Development, small applications, embedded systems',\n", "        'pros': 'No setup required, fast for small data',\n", "        'cons': 'Limited concurrency, no network access'\n", "    },\n", "    'PostgreSQL': {\n", "        'description': 'Advanced open-source relational database',\n", "        'library': 'psycopg2, asyncpg',\n", "        'use_cases': 'Web applications, data warehousing, analytics',\n", "        'pros': 'ACID compliant, extensible, JSON support',\n", "        'cons': 'More complex setup, resource intensive'\n", "    },\n", "    'MySQL': {\n", "        'description': 'Popular open-source relational database',\n", "        'library': 'mysql-connector-python, PyMySQL',\n", "        'use_cases': 'Web applications, content management',\n", "        'pros': 'Fast, widely supported, good documentation',\n", "        'cons': 'Less feature-rich than PostgreSQL'\n", "    },\n", "    'MongoDB': {\n", "        'description': 'Document-oriented NoSQL database',\n", "        'library': 'pymongo',\n", "        'use_cases': 'Flexible schemas, rapid development',\n", "        'pros': 'Schema flexibility, horizontal scaling',\n", "        'cons': 'No ACID transactions (older versions)'\n", "    }\n", "}\n", "\n", "for db_name, details in databases.items():\n", "    print(f'\\n{db_name}:')\n", "    for key, value in details.items():\n", "        print(f'  {key.replace(\"_\", \" \").title()}: {value}')\n", "\n", "# SQL fundamentals\n", "print('\\n=== SQL Fundamentals ===')\n", "sql_operations = {\n", "    'DDL (Data Definition Language)': [\n", "        'CREATE TABLE - Define table structure',\n", "        'ALTER TABLE - Modify table structure',\n", "        'DROP TABLE - Delete table',\n", "        'CREATE INDEX - Create database indexes'\n", "    ],\n", "    'DML (Data Manipulation Language)': [\n", "        'SELECT - Query data',\n", "        'INSERT - Add new records',\n", "        'UPDATE - Modify existing records',\n", "        'DELETE - Remove records'\n", "    ],\n", "    'DCL (Data Control Language)': [\n", "        'GRANT - Give permissions',\n", "        'REVOKE - Remove permissions',\n", "        'COMMIT - Save transaction',\n", "        'ROLLBACK - Undo transaction'\n", "    ]\n", "}\n", "\n", "for category, operations in sql_operations.items():\n", "    print(f'\\n{category}:')\n", "    for operation in operations:\n", "        print(f'  - {operation}')\n", "\n", "# Python database APIs\n", "print('\\n=== Python Database API (DB-API 2.0) ===')\n", "db_api_concepts = [\n", "    'Connection objects - Represent database connections',\n", "    'Cursor objects - Execute SQL statements and fetch results',\n", "    'Parameter substitution - Safely insert values into queries',\n", "    'Transaction management - Control commit/rollback behavior',\n", "    'Exception handling - Handle database-specific errors'\n", "]\n", "\n", "for concept in db_api_concepts:\n", "    print(f'- {concept}')\n", "\n", "# Common data types mapping\n", "print('\\n=== Python to SQL Data Type Mapping ===')\n", "type_mapping = {\n", "    'str': 'VARCHAR, TEXT, CHAR',\n", "    'int': 'INTEGER, BIGINT, SMALLINT',\n", "    'float': 'REAL, DOUBLE, DECIMAL',\n", "    'bool': 'BOOLEAN (or INTEGER 0/1)',\n", "    'datetime': 'TIMESTAMP, DATETIME',\n", "    'date': 'DATE',\n", "    'bytes': 'BLOB, BYTEA',\n", "    'None': 'NULL'\n", "}\n", "\n", "for python_type, sql_types in type_mapping.items():\n", "    print(f'{python_type}: {sql_types}')\n", "\n", "print('\\nUnderstanding these fundamentals is crucial for effective database programming!')"]}, {"cell_type": "markdown", "id": "71c71fe2", "metadata": {}, "source": ["# SQLite with Python {#sqlite}\n", "\n", "SQLite is Python's built-in database solution, perfect for development, testing, and lightweight applications."]}, {"cell_type": "code", "execution_count": null, "id": "ff42471f", "metadata": {}, "outputs": [], "source": ["# SQLite with Python\n", "print('=== SQLite with Python ===')\n", "\n", "# Basic SQLite operations\n", "sqlite_basic = '''\n", "import sqlite3\n", "from datetime import datetime\n", "import json\n", "\n", "# Connect to database (creates file if doesn't exist)\n", "conn = sqlite3.connect('example.db')\n", "cursor = conn.cursor()\n", "\n", "# Create table\n", "cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS users (\n", "        id INTEGER PRIMARY KEY AUTOINCREMENT,\n", "        username TEXT UNIQUE NOT NULL,\n", "        email TEXT UNIQUE NOT NULL,\n", "        password_hash TEXT NOT NULL,\n", "        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n", "        is_active BOOLEAN DEFAULT 1,\n", "        profile_data TEXT  -- JSON data\n", "    )\n", "''')\n", "\n", "# Insert single record\n", "cursor.execute('''\n", "    INSERT INTO users (username, email, password_hash, profile_data)\n", "    VALUES (?, ?, ?, ?)\n", "''', ('alice', '<EMAIL>', 'hashed_password', \n", "      json.dumps({'age': 30, 'city': 'New York'})))\n", "\n", "# Insert multiple records\n", "users_data = [\n", "    ('bob', '<EMAIL>', 'hashed_password2', \n", "     json.dumps({'age': 25, 'city': 'San Francisco'})),\n", "    ('charlie', '<EMAIL>', 'hashed_password3',\n", "     json.dumps({'age': 35, 'city': 'Chicago'}))\n", "]\n", "\n", "cursor.executemany('''\n", "    INSERT INTO users (username, email, password_hash, profile_data)\n", "    VALUES (?, ?, ?, ?)\n", "''', users_data)\n", "\n", "# Commit changes\n", "conn.commit()\n", "'''\n", "print('Basic SQLite Operations:')\n", "print(sqlite_basic)\n", "\n", "print('\\n=== Querying Data ===')\n", "sqlite_queries = '''\n", "# Fetch all records\n", "cursor.execute('SELECT * FROM users')\n", "all_users = cursor.fetchall()\n", "print(\"All users:\", all_users)\n", "\n", "# Fetch one record\n", "cursor.execute('SELECT * FROM users WHERE username = ?', ('alice',))\n", "user = cursor.fetchone()\n", "print(\"Alice:\", user)\n", "\n", "# Fetch with limit\n", "cursor.execute('SELECT username, email FROM users LIMIT 2')\n", "limited_users = cursor.fetchmany(2)\n", "print(\"Limited users:\", limited_users)\n", "\n", "# Using row factory for named access\n", "conn.row_factory = sqlite3.Row\n", "cursor = conn.cursor()\n", "\n", "cursor.execute('SELECT * FROM users WHERE id = ?', (1,))\n", "user_row = cursor.fetchone()\n", "if user_row:\n", "    print(f\"Username: {user_row['username']}\")\n", "    print(f\"Email: {user_row['email']}\")\n", "    \n", "    # Parse JSON data\n", "    profile = json.loads(user_row['profile_data'])\n", "    print(f\"Age: {profile['age']}\")\n", "'''\n", "print(sqlite_queries)\n", "\n", "print('\\n=== Advanced SQLite Features ===')\n", "sqlite_advanced = '''\n", "# Transactions\n", "try:\n", "    conn.execute('BEGIN')\n", "    \n", "    # Multiple operations\n", "    cursor.execute('UPDATE users SET is_active = 0 WHERE username = ?', ('bob',))\n", "    cursor.execute('INSERT INTO user_logs (user_id, action) VALUES (?, ?)', (2, 'deactivated'))\n", "    \n", "    conn.commit()\n", "    print(\"Transaction completed successfully\")\n", "except sqlite3.Error as e:\n", "    conn.rollback()\n", "    print(f\"Transaction failed: {e}\")\n", "\n", "# Context manager (automatic commit/rollback)\n", "with conn:\n", "    cursor.execute('UPDATE users SET email = ? WHERE username = ?', \n", "                  ('<EMAIL>', 'alice'))\n", "\n", "# Custom functions\n", "def calculate_age(birth_year):\n", "    return datetime.now().year - birth_year\n", "\n", "conn.create_function('calculate_age', 1, calculate_age)\n", "\n", "# Use custom function in query\n", "cursor.execute('SELECT username, calculate_age(1990) as age FROM users')\n", "\n", "# Indexes for performance\n", "cursor.execute('CREATE INDEX IF NOT EXISTS idx_username ON users(username)')\n", "cursor.execute('CREATE INDEX IF NOT EXISTS idx_email ON users(email)')\n", "\n", "# Full-text search\n", "cursor.execute('''\n", "    CREATE VIRTUAL TABLE IF NOT EXISTS posts_fts USING fts5(\n", "        title, content, author\n", "    )\n", "''')\n", "'''\n", "print(sqlite_advanced)\n", "\n", "print('\\n=== SQLite Best Practices ===')\n", "best_practices = [\n", "    'Always use parameterized queries to prevent SQL injection',\n", "    'Use context managers or try/finally for connection cleanup',\n", "    'Enable foreign key constraints: PRAGMA foreign_keys = ON',\n", "    'Use transactions for multiple related operations',\n", "    'Create indexes on frequently queried columns',\n", "    'Use VACUUM periodically to reclaim space',\n", "    'Consider WAL mode for better concurrency: PRAGMA journal_mode = WAL',\n", "    'Use sqlite3.Row for named column access'\n", "]\n", "\n", "for i, practice in enumerate(best_practices, 1):\n", "    print(f'{i}. {practice}')\n", "\n", "print('\\nSQLite is perfect for development and lightweight production applications!')"]}, {"cell_type": "markdown", "id": "b3ad4049", "metadata": {}, "source": ["# PostgreSQL Integration {#postgresql}\n", "\n", "PostgreSQL is a powerful, enterprise-grade database system with excellent Python support through psycopg2 and asyncpg."]}, {"cell_type": "code", "execution_count": null, "id": "4adc818d", "metadata": {}, "outputs": [], "source": ["# PostgreSQL Integration\n", "print('=== PostgreSQL Integration ===')\n", "\n", "print('Installation:')\n", "print('pip install psycopg2-binary  # For most use cases')\n", "print('pip install psycopg2        # If you have PostgreSQL dev libraries')\n", "print('pip install asyncpg         # For async operations')\n", "\n", "print('\\n=== Basic PostgreSQL Operations ===')\n", "postgresql_basic = '''\n", "import psycopg2\n", "from psycopg2.extras import RealDictCursor, execute_values\n", "from psycopg2 import sql\n", "import os\n", "\n", "# Connection parameters\n", "DATABASE_URL = os.getenv('DATABASE_URL', \n", "    'postgresql://username:password@localhost:5432/dbname')\n", "\n", "# Connect to database\n", "try:\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor(cursor_factory=RealDictCursor)\n", "    \n", "    print(\"Connected to PostgreSQL successfully\")\n", "    \n", "    # Get database version\n", "    cursor.execute('SELECT version()')\n", "    version = cursor.fetchone()\n", "    print(f\"PostgreSQL version: {version['version']}\")\n", "    \n", "except psycopg2.Error as e:\n", "    print(f\"Error connecting to PostgreSQL: {e}\")\n", "\n", "# Create table with advanced features\n", "cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS products (\n", "        id SERIAL PRIMARY KEY,\n", "        name VARCHAR(255) NOT NULL,\n", "        description TEXT,\n", "        price DECIMAL(10, 2) NOT NULL,\n", "        category_id INTEGER REFERENCES categories(id),\n", "        tags TEXT[],  -- Array column\n", "        metadata JSONB,  -- JSON column with indexing\n", "        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n", "        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n", "        search_vector TSVECTOR  -- Full-text search\n", "    )\n", "''')\n", "\n", "# Create indexes\n", "cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)')\n", "cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_metadata ON products USING GIN(metadata)')\n", "cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_search ON products USING GIN(search_vector)')\n", "'''\n", "print(postgresql_basic)\n", "\n", "print('\\n=== Advanced PostgreSQL Features ===')\n", "postgresql_advanced = '''\n", "# Insert with RETURNING clause\n", "cursor.execute('''\n", "    INSERT INTO products (name, description, price, tags, metadata)\n", "    VALUES (%s, %s, %s, %s, %s)\n", "    RETURNING id, created_at\n", "''', (\n", "    'Laptop',\n", "    'High-performance laptop',\n", "    999.99,\n", "    ['electronics', 'computers'],\n", "    {'brand': 'TechCorp', 'warranty': '2 years'}\n", "))\n", "\n", "new_product = cursor.fetchone()\n", "print(f\"Created product with ID: {new_product['id']}\")\n", "\n", "# Bulk insert with execute_values\n", "products_data = [\n", "    ('Mouse', 'Wireless mouse', 29.99, ['electronics', 'accessories'], \n", "     {'brand': 'MouseCorp', 'wireless': True}),\n", "    ('Keyboard', 'Mechanical keyboard', 79.99, ['electronics', 'accessories'],\n", "     {'brand': 'KeyCorp', 'switches': 'blue'})\n", "]\n", "\n", "execute_values(\n", "    cursor,\n", "    \"INSERT INTO products (name, description, price, tags, metadata) VALUES %s\",\n", "    products_data,\n", "    template=None,\n", "    page_size=100\n", ")\n", "\n", "# JSON queries\n", "cursor.execute('''\n", "    SELECT name, metadata->>'brand' as brand\n", "    FROM products\n", "    WHERE metadata->>'brand' = %s\n", "''', ('TechCorp',))\n", "\n", "# Array queries\n", "cursor.execute('''\n", "    SELECT name, tags\n", "    FROM products\n", "    WHERE 'electronics' = ANY(tags)\n", "''')\n", "\n", "# Full-text search\n", "cursor.execute('''\n", "    UPDATE products\n", "    SET search_vector = to_tsvector('english', name || ' ' || COALESCE(description, ''))\n", "''')\n", "\n", "cursor.execute('''\n", "    SELECT name, ts_rank(search_vector, query) as rank\n", "    FROM products, plainto_tsquery('english', %s) query\n", "    WHERE search_vector @@ query\n", "    ORDER BY rank DESC\n", "''', ('laptop computer',))\n", "'''\n", "print(postgresql_advanced)\n", "\n", "print('\\n=== Connection Pooling ===')\n", "connection_pooling = '''\n", "from psycopg2 import pool\n", "import threading\n", "\n", "# Create connection pool\n", "connection_pool = psycopg2.pool.ThreadedConnectionPool(\n", "    minconn=1,\n", "    maxconn=20,\n", "    host='localhost',\n", "    database='mydb',\n", "    user='username',\n", "    password='password'\n", ")\n", "\n", "def get_user_data(user_id):\n", "    # Get connection from pool\n", "    conn = connection_pool.getconn()\n", "    \n", "    try:\n", "        with conn.cursor(cursor_factory=RealDictCursor) as cursor:\n", "            cursor.execute('SELECT * FROM users WHERE id = %s', (user_id,))\n", "            return cursor.fetchone()\n", "    finally:\n", "        # Return connection to pool\n", "        connection_pool.putconn(conn)\n", "\n", "# Context manager for pool connections\n", "from contextlib import contextmanager\n", "\n", "@contextmanager\n", "def get_db_connection():\n", "    conn = connection_pool.getconn()\n", "    try:\n", "        yield conn\n", "    finally:\n", "        connection_pool.putconn(conn)\n", "\n", "# Usage\n", "with get_db_connection() as conn:\n", "    with conn.cursor() as cursor:\n", "        cursor.execute('SELECT COUNT(*) FROM users')\n", "        count = cursor.fetchone()[0]\n", "'''\n", "print(connection_pooling)\n", "\n", "print('\\nPostgreSQL provides enterprise-grade features for complex applications!')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"file_extension": ".py", "mimetype": "text/x-python", "name": "python", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 5}