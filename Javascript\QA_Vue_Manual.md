# Quality Assurance for Vue.js Code Generation

## 1. Introduction

Professional Vue.js development requires mastery of reactivity, component composition, and the Vue ecosystem. AI-generated Vue code often lacks the sophisticated patterns and optimizations that experienced Vue developers implement. This page ensures generated Vue code follows modern Vue 3 best practices and leverages the framework's full potential.

## 2. Component Architecture and Composition

### Best Practices:
- **Single File Components**: Proper SFC structure and organization
- **Composition API**: Prefer Composition API for complex logic
- **Composables**: Extract reusable logic into composable functions
- **Component Communication**: Props down, events up pattern
- **Slot Design**: Flexible content distribution with slots
- **Provide/Inject**: Dependency injection for deep component trees

### Example:
```vue
<!-- UserCard.vue -->
<template>
  <div class="user-card" :class="cardClasses">
    <UserAvatar :user="user" :size="avatarSize" />
    
    <div class="user-info">
      <slot name="header" :user="user">
        <h3>{{ user.name }}</h3>
      </slot>
      
      <slot name="content" :user="user">
        <p>{{ user.email }}</p>
      </slot>
    </div>
    
    <div class="user-actions" v-if="$slots.actions || showDefaultActions">
      <slot name="actions" :user="user" :handlers="actionHandlers">
        <button @click="handleEdit" v-if="canEdit">Edit</button>
        <button @click="handleDelete" v-if="canDelete">Delete</button>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  user: User;
  variant?: 'compact' | 'detailed';
  canEdit?: boolean;
  canDelete?: boolean;
}

interface Emits {
  edit: [user: User];
  delete: [userId: string];
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'detailed',
  canEdit: false,
  canDelete: false
});

const emit = defineEmits<Emits>();

// Composable for user operations
const { loading, error, updateUser, deleteUser } = useUserOperations();

// Computed properties
const cardClasses = computed(() => ({
  'user-card--compact': props.variant === 'compact',
  'user-card--detailed': props.variant === 'detailed',
  'user-card--loading': loading.value
}));

const avatarSize = computed(() => 
  props.variant === 'compact' ? 'small' : 'medium'
);

const showDefaultActions = computed(() => 
  props.canEdit || props.canDelete
);

// Event handlers
const handleEdit = () => {
  emit('edit', props.user);
};

const handleDelete = async () => {
  if (confirm(`Delete user ${props.user.name}?`)) {
    try {
      await deleteUser(props.user.id);
      emit('delete', props.user.id);
    } catch (err) {
      console.error('Failed to delete user:', err);
    }
  }
};

const actionHandlers = {
  edit: handleEdit,
  delete: handleDelete
};
</script>

<style scoped>
.user-card {
  @apply border rounded-lg p-4 transition-all duration-200;
}

.user-card--compact {
  @apply p-2;
}

.user-card--loading {
  @apply opacity-50 pointer-events-none;
}
</style>
```

### Composable Example:
```typescript
// composables/useUserOperations.ts
export function useUserOperations() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const updateUser = async (user: User): Promise<void> => {
    loading.value = true;
    error.value = null;
    
    try {
      await userService.update(user);
      // Trigger reactivity updates
      await refreshUserData();
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Update failed';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteUser = async (userId: string): Promise<void> => {
    loading.value = true;
    error.value = null;
    
    try {
      await userService.delete(userId);
      await refreshUserData();
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Delete failed';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading: readonly(loading),
    error: readonly(error),
    updateUser,
    deleteUser
  };
}
```

## 3. Vue-Specific Edge Cases and Reactivity

### Critical Considerations:
- **Reactivity Gotchas**: Proper ref/reactive usage
- **Async Component Loading**: Handle loading states and errors
- **Watchers**: Proper cleanup and deep watching
- **Lifecycle Management**: Component lifecycle and cleanup
- **Template Refs**: Safe DOM element access
- **Teleport Usage**: Portal-like functionality for modals/tooltips

### Example:
```vue
<script setup lang="ts">
// Proper reactivity handling
const users = ref<User[]>([]);
const selectedUser = ref<User | null>(null);
const searchQuery = ref('');

// Computed with proper dependencies
const filteredUsers = computed(() => {
  if (!searchQuery.value.trim()) return users.value;
  
  const query = searchQuery.value.toLowerCase();
  return users.value.filter(user => 
    user.name.toLowerCase().includes(query) ||
    user.email.toLowerCase().includes(query)
  );
});

// Watcher with proper cleanup
const stopWatcher = watchEffect(() => {
  if (selectedUser.value) {
    // Track user activity
    analytics.track('user_selected', {
      userId: selectedUser.value.id,
      timestamp: Date.now()
    });
  }
});

// Template refs with null safety
const userListRef = ref<HTMLElement | null>(null);
const modalRef = ref<InstanceType<typeof Modal> | null>(null);

// Async data fetching with error handling
const { data: users, error, refresh } = await useLazyAsyncData(
  'users',
  () => userService.getAll(),
  {
    default: () => [],
    server: false
  }
);

// Lifecycle hooks
onMounted(async () => {
  try {
    await refresh();
    
    // Focus search input after mount
    nextTick(() => {
      const searchInput = document.querySelector('#search-input') as HTMLInputElement;
      searchInput?.focus();
    });
  } catch (err) {
    console.error('Failed to load users:', err);
  }
});

onBeforeUnmount(() => {
  // Cleanup
  stopWatcher();
  analytics.flush();
});

// Error handling for async operations
const handleAsyncOperation = async (operation: () => Promise<void>) => {
  try {
    await operation();
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Operation failed';
    
    // Show user-friendly error
    toast.error(message);
    
    // Log for debugging
    console.error('Async operation failed:', err);
  }
};
</script>

<template>
  <div class="user-management">
    <!-- Error boundary equivalent -->
    <ErrorBoundary>
      <Suspense>
        <template #default>
          <UserList 
            ref="userListRef"
            :users="filteredUsers"
            :selected="selectedUser"
            @select="selectedUser = $event"
          />
        </template>
        
        <template #fallback>
          <LoadingSpinner />
        </template>
      </Suspense>
    </ErrorBoundary>

    <!-- Teleport for modal -->
    <Teleport to="body">
      <Modal 
        ref="modalRef"
        v-if="selectedUser"
        @close="selectedUser = null"
      >
        <UserDetails :user="selectedUser" />
      </Modal>
    </Teleport>
  </div>
</template>
```

## 4. Performance Optimization and Vue Patterns

### Optimization Strategies:
- **v-memo**: Memoize expensive template parts
- **Async Components**: Code splitting with defineAsyncComponent
- **Virtual Scrolling**: For large lists
- **Computed Caching**: Leverage computed property caching
- **Event Delegation**: Efficient event handling
- **Keep-Alive**: Cache component instances

### Example:
```vue
<script setup lang="ts">
// Async component with loading states
const UserDetails = defineAsyncComponent({
  loader: () => import('./UserDetails.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
});

// Virtual scrolling for large lists
const { containerProps, wrapperProps, list } = useVirtualList(
  filteredUsers,
  {
    itemHeight: 80,
    overscan: 5
  }
);

// Debounced search
const debouncedSearch = refDebounced(searchQuery, 300);

// Memoized expensive computation
const userStatistics = computed(() => {
  // Expensive calculation
  return calculateUserStatistics(users.value);
});

// Optimized event handlers
const handleUserClick = (user: User) => {
  selectedUser.value = user;
  
  // Batch DOM updates
  nextTick(() => {
    modalRef.value?.focus();
  });
};
</script>

<template>
  <div class="user-list" v-bind="containerProps">
    <!-- Virtual scrolling wrapper -->
    <div v-bind="wrapperProps">
      <!-- v-memo for expensive list items -->
      <UserCard
        v-for="{ data: user, index } in list"
        :key="user.id"
        :user="user"
        :index="index"
        v-memo="[user.id, user.updatedAt, selectedUser?.id]"
        @click="handleUserClick(user)"
      />
    </div>
  </div>

  <!-- Keep-alive for expensive components -->
  <KeepAlive :max="5">
    <component 
      :is="currentView"
      :key="currentViewKey"
    />
  </KeepAlive>
</template>
```

## 5. State Management and Architecture

### Vue State Patterns:
- **Pinia**: Modern state management
- **Composable State**: Shared reactive state
- **Provide/Inject**: Dependency injection
- **Event Bus**: Component communication (sparingly)
- **Store Composition**: Modular store design

### Example:
```typescript
// stores/userStore.ts
export const useUserStore = defineStore('user', () => {
  // State
  const users = ref<User[]>([]);
  const currentUser = ref<User | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const activeUsers = computed(() => 
    users.value.filter(user => user.isActive)
  );

  const userById = computed(() => 
    (id: string) => users.value.find(user => user.id === id)
  );

  // Actions
  const fetchUsers = async (): Promise<void> => {
    loading.value = true;
    error.value = null;

    try {
      const response = await userService.getAll();
      users.value = response.data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch users';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateUser = async (user: User): Promise<void> => {
    try {
      const updated = await userService.update(user);
      
      // Update local state
      const index = users.value.findIndex(u => u.id === user.id);
      if (index !== -1) {
        users.value[index] = updated;
      }
      
      // Update current user if it's the same
      if (currentUser.value?.id === user.id) {
        currentUser.value = updated;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update user';
      throw err;
    }
  };

  // Reset function
  const $reset = () => {
    users.value = [];
    currentUser.value = null;
    loading.value = false;
    error.value = null;
  };

  return {
    // State
    users: readonly(users),
    currentUser: readonly(currentUser),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    activeUsers,
    userById,
    
    // Actions
    fetchUsers,
    updateUser,
    $reset
  };
});
```

## 6. Testing and Quality Assurance

### Vue Testing Strategy:
- **Component Testing**: Vue Test Utils best practices
- **Composable Testing**: Test reusable logic
- **Store Testing**: Pinia store testing
- **E2E Testing**: User interaction flows
- **Accessibility Testing**: Vue-specific a11y concerns

### Example:
```typescript
// UserCard.test.ts
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import UserCard from './UserCard.vue';

describe('UserCard', () => {
  const mockUser: User = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    isActive: true
  };

  const createWrapper = (props = {}) => {
    return mount(UserCard, {
      props: {
        user: mockUser,
        ...props
      },
      global: {
        plugins: [createTestingPinia()]
      }
    });
  };

  it('renders user information correctly', () => {
    const wrapper = createWrapper();
    
    expect(wrapper.text()).toContain('John Doe');
    expect(wrapper.text()).toContain('<EMAIL>');
  });

  it('emits edit event when edit button is clicked', async () => {
    const wrapper = createWrapper({ canEdit: true });
    
    await wrapper.find('[data-testid="edit-button"]').trigger('click');
    
    expect(wrapper.emitted('edit')).toBeTruthy();
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockUser]);
  });

  it('applies correct CSS classes based on variant', () => {
    const wrapper = createWrapper({ variant: 'compact' });
    
    expect(wrapper.classes()).toContain('user-card--compact');
  });
});

// Composable testing
describe('useUserOperations', () => {
  it('handles user update successfully', async () => {
    const { updateUser, loading, error } = useUserOperations();
    
    const updatedUser = { ...mockUser, name: 'Jane Doe' };
    
    await updateUser(updatedUser);
    
    expect(loading.value).toBe(false);
    expect(error.value).toBeNull();
  });
});
```

## 7. Review & Improvement Prompt for AI Assistant

After generating initial Vue.js code, use this prompt for comprehensive review:

> **Vue.js Code Quality Review**
> 
> Review the generated Vue.js code carefully and improve it by performing the following steps:
> 
> 1. **Component Architecture**: Refactor into well-structured Single File Components using Composition API. Extract reusable logic into composables and implement proper slot design for flexibility.
> 
> 2. **Vue Reactivity**: Ensure proper use of ref/reactive, implement safe template refs, add proper watchers with cleanup, and handle async operations with appropriate loading states.
> 
> 3. **Performance Optimization**: Implement v-memo for expensive renders, use defineAsyncComponent for code splitting, add virtual scrolling for large lists, and optimize computed properties.
> 
> 4. **State Management**: Evaluate state placement (local vs Pinia store), implement proper store composition, use provide/inject for dependency injection, and ensure reactive state updates.
> 
> 5. **Vue-Specific Patterns**: Use Teleport for portals, implement proper lifecycle management, add Suspense for async components, and use KeepAlive where appropriate.
> 
> 6. **TypeScript Integration**: Add comprehensive prop types, emit types, template ref types, and composable return types for full type safety.
> 
> 7. **Testing & Maintainability**: Ensure components are easily testable, composables are pure functions where possible, and follow Vue 3 best practices.
> 
> 8. **Accessibility & UX**: Implement proper ARIA attributes, keyboard navigation, focus management, and semantic HTML within Vue templates.
> 
> Provide the enhanced code with detailed explanations for each Vue-specific improvement, focusing on leveraging Vue's reactivity system and ecosystem effectively.

## 8. Quality Checklist

### Vue.js Excellence Verification:
- [ ] Composition API used appropriately
- [ ] Composables extract reusable logic
- [ ] Reactivity properly implemented
- [ ] Template refs safely accessed
- [ ] Watchers include proper cleanup
- [ ] Performance optimizations applied
- [ ] TypeScript types comprehensive
- [ ] Pinia stores well-structured
- [ ] Components properly tested
- [ ] Accessibility requirements met
- [ ] Lifecycle hooks used correctly
- [ ] Slots designed for flexibility
- [ ] Error boundaries implemented
- [ ] Modern Vue 3 patterns followed
