{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive Vue.js Programming Manual\n", "\n", "This manual serves as a complete reference for Vue.js development, from basic concepts to advanced patterns. Vue.js is a progressive JavaScript framework for building user interfaces.\n", "\n", "## Table of Contents\n", "1. [Vue.js Fundamentals](#fundamentals)\n", "2. [Template Syntax and Directives](#templates)\n", "3. [Components and Props](#components)\n", "4. [Reactivity and Data](#reactivity)\n", "5. [Event Handling](#events)\n", "6. [Computed Properties and Watchers](#computed)\n", "7. [Lifecycle Hooks](#lifecycle)\n", "8. [Composition API](#composition)\n", "9. [Vue Router](#router)\n", "10. [State Management (Vuex/Pinia)](#state)\n", "11. [Testing](#testing)\n", "12. [Best Practices](#best-practices)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# Vue.js Fundamentals {#fundamentals}\n", "\n", "Vue.js is a progressive framework that can be incrementally adopted. It focuses on the view layer and provides reactive data binding and component composition."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["// Vue.js Fundamentals\n", "// Note: This shows Vue concepts in JavaScript since we can't run Vue directly here\n", "\n", "console.log('=== Vue.js Fundamentals ===');\n", "\n", "// Vue Core Concepts\n", "console.log('Vue Core Concepts:');\n", "console.log('1. Progressive Framework - can be adopted incrementally');\n", "console.log('2. Reactive Data Binding - automatic UI updates');\n", "console.log('3. Component-Based Architecture');\n", "console.log('4. Template-based syntax with directives');\n", "console.log('5. Single File Components (.vue files)');\n", "\n", "/*\n", "// Basic Vue 3 setup\n", "import { createApp } from 'vue';\n", "\n", "// Options API style\n", "const app = createApp({\n", "  data() {\n", "    return {\n", "      message: 'Hello Vue!',\n", "      count: 0\n", "    };\n", "  },\n", "  methods: {\n", "    increment() {\n", "      this.count++;\n", "    }\n", "  },\n", "  template: `\n", "    <div>\n", "      <h1>{{ message }}</h1>\n", "      <p>Count: {{ count }}</p>\n", "      <button @click=\"increment\">Increment</button>\n", "    </div>\n", "  `\n", "});\n", "\n", "app.mount('#app');\n", "\n", "// Composition API style (Vue 3)\n", "import { ref, reactive } from 'vue';\n", "\n", "const app2 = createApp({\n", "  setup() {\n", "    const message = ref('Hello Vue!');\n", "    const count = ref(0);\n", "    \n", "    const increment = () => {\n", "      count.value++;\n", "    };\n", "    \n", "    return {\n", "      message,\n", "      count,\n", "      increment\n", "    };\n", "  },\n", "  template: `\n", "    <div>\n", "      <h1>{{ message }}</h1>\n", "      <p>Count: {{ count }}</p>\n", "      <button @click=\"increment\">Increment</button>\n", "    </div>\n", "  `\n", "});\n", "\n", "// Single File Component (.vue)\n", "/*\n", "<template>\n", "  <div class=\"hello\">\n", "    <h1>{{ msg }}</h1>\n", "    <button @click=\"count++\">Count: {{ count }}</button>\n", "  </div>\n", "</template>\n", "\n", "<script>\n", "export default {\n", "  name: '<PERSON><PERSON><PERSON><PERSON>',\n", "  props: {\n", "    msg: String\n", "  },\n", "  data() {\n", "    return {\n", "      count: 0\n", "    };\n", "  }\n", "};\n", "</script>\n", "\n", "<style scoped>\n", ".hello {\n", "  color: #42b883;\n", "}\n", "</style>\n", "*/\n", "\n", "// Reactivity simulation\n", "console.log('\\n=== Reactivity System Simulation ===');\n", "\n", "// Simple reactivity system\n", "function createReactiveData() {\n", "    const data = {};\n", "    const watchers = {};\n", "    \n", "    return new Proxy(data, {\n", "        get(target, key) {\n", "            return target[key];\n", "        },\n", "        set(target, key, value) {\n", "            const oldValue = target[key];\n", "            target[key] = value;\n", "            \n", "            // Trigger watchers\n", "            if (watchers[key]) {\n", "                watchers[key].forEach(watcher => {\n", "                    watcher(value, oldValue);\n", "                });\n", "            }\n", "            \n", "            console.log(`Reactive update: ${key} = ${value}`);\n", "            return true;\n", "        }\n", "    });\n", "}\n", "\n", "// Test reactivity\n", "const reactiveData = createReactiveData();\n", "reactiveData.message = 'Hello Vue!';\n", "reactiveData.count = 0;\n", "\n", "console.log('Initial data:', { message: reactiveData.message, count: reactiveData.count });\n", "\n", "// Simulate data changes\n", "reactiveData.count = 1;\n", "reactiveData.message = 'Hello World!';\n", "\n", "// Vue instance simulation\n", "console.log('\\n=== Vue Instance Simulation ===');\n", "\n", "class VueInstance {\n", "    constructor(options) {\n", "        this.options = options;\n", "        this.data = typeof options.data === 'function' ? options.data() : options.data || {};\n", "        this.methods = options.methods || {};\n", "        this.computed = options.computed || {};\n", "        \n", "        // Bind methods to instance\n", "        Object.keys(this.methods).forEach(key => {\n", "            this[key] = this.methods[key].bind(this);\n", "        });\n", "        \n", "        // Setup computed properties\n", "        Object.keys(this.computed).forEach(key => {\n", "            Object.defineProperty(this, key, {\n", "                get: this.computed[key].bind(this)\n", "            });\n", "        });\n", "        \n", "        // Make data reactive\n", "        this.makeReactive();\n", "        \n", "        console.log('Vue instance created');\n", "    }\n", "    \n", "    makeReactive() {\n", "        Object.keys(this.data).forEach(key => {\n", "            let value = this.data[key];\n", "            \n", "            Object.defineProperty(this, key, {\n", "                get() {\n", "                    return value;\n", "                },\n", "                set(newValue) {\n", "                    if (value !== newValue) {\n", "                        console.log(`Data changed: ${key} = ${newValue}`);\n", "                        value = newValue;\n", "                        this.update();\n", "                    }\n", "                }\n", "            });\n", "        });\n", "    }\n", "    \n", "    update() {\n", "        console.log('Component updated');\n", "        // In real Vue, this would re-render the template\n", "    }\n", "    \n", "    mount(selector) {\n", "        console.log(`Mounted to ${selector}`);\n", "        return this;\n", "    }\n", "}\n", "\n", "// Test Vue instance\n", "const vm = new VueInstance({\n", "    data() {\n", "        return {\n", "            message: 'Hello Vue!',\n", "            count: 0\n", "        };\n", "    },\n", "    methods: {\n", "        increment() {\n", "            this.count++;\n", "        },\n", "        updateMessage(newMessage) {\n", "            this.message = newMessage;\n", "        }\n", "    },\n", "    computed: {\n", "        doubleCount() {\n", "            return this.count * 2;\n", "        }\n", "    }\n", "});\n", "\n", "console.log('Initial state:', { message: vm.message, count: vm.count, doubleCount: vm.doubleCount });\n", "\n", "// Test reactivity\n", "vm.increment();\n", "vm.increment();\n", "vm.updateMessage('Hello World!');\n", "\n", "console.log('Updated state:', { message: vm.message, count: vm.count, doubleCount: vm.doubleCount });\n", "\n", "// Vue ecosystem\n", "console.log('\\n=== Vue Ecosystem ===');\n", "console.log('Core Libraries:');\n", "console.log('- Vue 3: Core framework with Composition API');\n", "console.log('- Vue Router: Official routing library');\n", "console.log('- Vuex/Pinia: State management');\n", "console.log('- Vue CLI: Project scaffolding and build tools');\n", "console.log('- Vite: Fast build tool (recommended)');\n", "\n", "console.log('\\nDevelopment Tools:');\n", "console.log('- Vue DevTools: Browser extension');\n", "console.log('- Vetur/Volar: VS Code extensions');\n", "console.log('- Vue Test Utils: Testing utilities');\n", "console.log('- Nuxt.js: Full-stack framework');\n", "console.log('- Quasar: UI framework');\n", "\n", "// Vue principles\n", "console.log('\\n=== Vue Principles ===');\n", "console.log('1. Progressive Enhancement - start small, scale up');\n", "console.log('2. Declarative Rendering - describe what you want');\n", "console.log('3. Component-Based - build with reusable pieces');\n", "console.log('4. Reactive Data - automatic UI updates');\n", "console.log('5. Template-Based - HTML-like syntax');\n", "console.log('6. Flexible - Options API or Composition API');\n", "\n", "console.log('\\nVue.js provides an approachable and versatile framework for building UIs!');"]}], "metadata": {"kernelspec": {"display_name": "JavaScript (Node.js)", "language": "javascript", "name": "javascript"}, "language_info": {"file_extension": ".js", "mimetype": "application/javascript", "name": "javascript", "version": "18.0.0"}}, "nbformat": 4, "nbformat_minor": 5}