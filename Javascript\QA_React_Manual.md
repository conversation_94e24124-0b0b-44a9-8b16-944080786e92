# Quality Assurance for React Code Generation

## 1. Introduction

Professional React development requires attention to component design, performance optimization, state management, and user experience. AI-generated React code often lacks the sophisticated patterns and optimizations that experienced React developers implement. This page ensures generated React code follows modern best practices and scales effectively.

## 2. Component Architecture and Modularization

### Best Practices:
- **Single Responsibility**: Each component should have one clear purpose
- **Composition over Inheritance**: Use component composition patterns
- **Custom Hooks**: Extract reusable logic into custom hooks
- **Component Hierarchy**: Proper parent-child relationships
- **Props Interface Design**: Clear and minimal prop interfaces
- **File Organization**: Logical component and hook organization

### Example:
```tsx
// Good: Focused, composable components
interface UserCardProps {
    user: User;
    onEdit?: (user: User) => void;
    onDelete?: (userId: string) => void;
    variant?: 'compact' | 'detailed';
}

const UserCard: React.FC<UserCardProps> = ({ 
    user, 
    onEdit, 
    onDelete, 
    variant = 'detailed' 
}) => {
    const handleEdit = useCallback(() => {
        onEdit?.(user);
    }, [onEdit, user]);

    const handleDelete = useCallback(() => {
        onDelete?.(user.id);
    }, [onDelete, user.id]);

    return (
        <Card variant={variant}>
            <UserAvatar user={user} />
            <UserInfo user={user} variant={variant} />
            <UserActions 
                onEdit={handleEdit}
                onDelete={handleDelete}
                canEdit={!!onEdit}
                canDelete={!!onDelete}
            />
        </Card>
    );
};

// Custom hook for user operations
const useUserOperations = (users: User[]) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateUser = useCallback(async (user: User) => {
        setLoading(true);
        setError(null);
        
        try {
            await userService.update(user);
            // Update local state or trigger refetch
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Update failed');
        } finally {
            setLoading(false);
        }
    }, []);

    return { updateUser, loading, error };
};
```

## 3. React-Specific Edge Cases and Error Boundaries

### Critical Considerations:
- **Async State Management**: Handle loading, error, and success states
- **Component Unmounting**: Cleanup effects and prevent memory leaks
- **Conditional Rendering**: Handle null/undefined states gracefully
- **List Rendering**: Proper key usage and empty state handling
- **Form Validation**: Real-time validation and error display
- **Error Boundaries**: Catch and handle component errors
- **Suspense Boundaries**: Handle async component loading

### Example:
```tsx
// Comprehensive error boundary
class ErrorBoundary extends React.Component<
    { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
    { hasError: boolean; error: Error | null }
> {
    constructor(props: any) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('Component error caught:', error, errorInfo);
        // Send to error reporting service
        errorReportingService.captureException(error, {
            extra: errorInfo,
            tags: { component: 'ErrorBoundary' }
        });
    }

    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback || DefaultErrorFallback;
            return <FallbackComponent error={this.state.error!} />;
        }

        return this.props.children;
    }
}

// Async data fetching with proper error handling
const useAsyncData = <T,>(
    fetchFn: () => Promise<T>,
    dependencies: React.DependencyList = []
) => {
    const [state, setState] = useState<{
        data: T | null;
        loading: boolean;
        error: Error | null;
    }>({
        data: null,
        loading: true,
        error: null
    });

    useEffect(() => {
        let cancelled = false;

        const fetchData = async () => {
            setState(prev => ({ ...prev, loading: true, error: null }));
            
            try {
                const data = await fetchFn();
                if (!cancelled) {
                    setState({ data, loading: false, error: null });
                }
            } catch (error) {
                if (!cancelled) {
                    setState({
                        data: null,
                        loading: false,
                        error: error instanceof Error ? error : new Error('Unknown error')
                    });
                }
            }
        };

        fetchData();

        return () => {
            cancelled = true;
        };
    }, dependencies);

    return state;
};
```

## 4. Performance Optimization and React Patterns

### Optimization Strategies:
- **Memoization**: React.memo, useMemo, useCallback usage
- **Code Splitting**: React.lazy and Suspense implementation
- **Virtual Scrolling**: For large lists
- **Debouncing**: For search and input handling
- **Image Optimization**: Lazy loading and responsive images
- **Bundle Optimization**: Tree shaking and chunk splitting

### Example:
```tsx
// Optimized list component with virtualization
const VirtualizedUserList = React.memo<{
    users: User[];
    onUserSelect: (user: User) => void;
}>(({ users, onUserSelect }) => {
    const [searchTerm, setSearchTerm] = useState('');
    
    // Debounced search
    const debouncedSearchTerm = useDebounce(searchTerm, 300);
    
    // Memoized filtered users
    const filteredUsers = useMemo(() => {
        if (!debouncedSearchTerm) return users;
        return users.filter(user => 
            user.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
    }, [users, debouncedSearchTerm]);

    // Memoized callback
    const handleUserSelect = useCallback((user: User) => {
        onUserSelect(user);
    }, [onUserSelect]);

    return (
        <div>
            <SearchInput 
                value={searchTerm}
                onChange={setSearchTerm}
                placeholder="Search users..."
            />
            <FixedSizeList
                height={400}
                itemCount={filteredUsers.length}
                itemSize={80}
                itemData={filteredUsers}
            >
                {({ index, style, data }) => (
                    <div style={style}>
                        <UserCard 
                            user={data[index]}
                            onSelect={handleUserSelect}
                            variant="compact"
                        />
                    </div>
                )}
            </FixedSizeList>
        </div>
    );
});

// Custom debounce hook
const useDebounce = <T,>(value: T, delay: number): T => {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};
```

## 5. State Management and Architecture Review

### State Management Patterns:
- **Local vs Global State**: Appropriate state placement
- **Context API**: Proper context design and optimization
- **State Machines**: For complex state logic
- **Reducer Patterns**: useReducer for complex state
- **External State**: Integration with Redux, Zustand, etc.
- **Server State**: React Query, SWR integration

### Key Questions:
- **State Colocation**: Is state as close to usage as possible?
- **State Normalization**: Are complex state structures normalized?
- **Side Effect Management**: Are effects properly organized?
- **Performance Impact**: Does state cause unnecessary re-renders?
- **Testing**: Is state logic easily testable?

### Example:
```tsx
// Context with optimization
interface AppContextValue {
    user: User | null;
    theme: Theme;
    updateUser: (user: User) => void;
    updateTheme: (theme: Theme) => void;
}

const AppContext = React.createContext<AppContextValue | null>(null);

// Split contexts to prevent unnecessary re-renders
const UserContext = React.createContext<{
    user: User | null;
    updateUser: (user: User) => void;
} | null>(null);

const ThemeContext = React.createContext<{
    theme: Theme;
    updateTheme: (theme: Theme) => void;
} | null>(null);

// Custom hooks for context consumption
const useUser = () => {
    const context = useContext(UserContext);
    if (!context) {
        throw new Error('useUser must be used within UserProvider');
    }
    return context;
};

const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within ThemeProvider');
    }
    return context;
};
```

## 6. Testing and Quality Assurance

### Testing Strategy:
- **Component Testing**: React Testing Library best practices
- **Hook Testing**: Custom hook testing with renderHook
- **Integration Testing**: User interaction flows
- **Accessibility Testing**: Screen reader and keyboard navigation
- **Visual Regression**: Snapshot and visual testing
- **Performance Testing**: Render performance monitoring

### Example:
```tsx
// Comprehensive component test
describe('UserCard', () => {
    const mockUser: User = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'avatar.jpg'
    };

    it('renders user information correctly', () => {
        render(<UserCard user={mockUser} />);
        
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByRole('img', { name: /john doe/i })).toHaveAttribute(
            'src', 
            'avatar.jpg'
        );
    });

    it('calls onEdit when edit button is clicked', async () => {
        const onEdit = jest.fn();
        render(<UserCard user={mockUser} onEdit={onEdit} />);
        
        const editButton = screen.getByRole('button', { name: /edit/i });
        await user.click(editButton);
        
        expect(onEdit).toHaveBeenCalledWith(mockUser);
    });

    it('is accessible', async () => {
        const { container } = render(<UserCard user={mockUser} />);
        const results = await axe(container);
        
        expect(results).toHaveNoViolations();
    });
});

// Hook testing
describe('useUserOperations', () => {
    it('handles user update successfully', async () => {
        const mockUsers = [mockUser];
        const { result } = renderHook(() => useUserOperations(mockUsers));
        
        await act(async () => {
            await result.current.updateUser({ ...mockUser, name: 'Jane Doe' });
        });
        
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
    });
});
```

## 7. Review & Improvement Prompt for AI Assistant

After generating initial React code, use this prompt for comprehensive review:

> **React Code Quality Review**
> 
> Review the generated React code carefully and improve it by performing the following steps:
> 
> 1. **Component Architecture**: Refactor into focused, composable components following single responsibility principle. Extract reusable logic into custom hooks and ensure proper component hierarchy.
> 
> 2. **React-Specific Edge Cases**: Add comprehensive error boundaries, handle async states (loading/error/success), implement proper cleanup in effects, and handle conditional rendering edge cases.
> 
> 3. **Performance Optimization**: Implement React.memo, useMemo, and useCallback appropriately. Add code splitting with React.lazy, optimize list rendering, and prevent unnecessary re-renders.
> 
> 4. **State Management**: Evaluate state placement (local vs global), implement proper context design, consider state machines for complex logic, and optimize context to prevent unnecessary re-renders.
> 
> 5. **Accessibility & UX**: Ensure proper ARIA attributes, keyboard navigation, screen reader support, focus management, and semantic HTML usage.
> 
> 6. **Testing Integration**: Make components easily testable with proper prop interfaces, avoid implementation details in tests, and ensure accessibility testing coverage.
> 
> 7. **TypeScript Integration**: Add comprehensive prop types, event handler types, ref types, and generic component patterns where appropriate.
> 
> 8. **Modern React Patterns**: Use latest React features appropriately, implement proper Suspense boundaries, and follow React 18+ best practices.
> 
> Provide the enhanced code with detailed explanations for each React-specific improvement, focusing on component reusability, performance, and user experience.

## 8. Quality Checklist

### React Excellence Verification:
- [ ] Components follow single responsibility
- [ ] Custom hooks extract reusable logic
- [ ] Error boundaries implemented
- [ ] Loading and error states handled
- [ ] Performance optimizations applied
- [ ] Accessibility requirements met
- [ ] TypeScript types comprehensive
- [ ] Tests cover user interactions
- [ ] State management optimized
- [ ] Effects properly cleaned up
- [ ] Keys used correctly in lists
- [ ] Conditional rendering safe
- [ ] Context usage optimized
- [ ] Modern React patterns used
