/**
 * Node.js Manual Quiz
 * Comprehensive quiz covering all sections of the Node.js programming manual
 */

const quizzes = {
  "Node.js Fundamentals": [
    { 
      question: "What is Node.js built on?", 
      answer: "Chrome's V8 JavaScript engine, which allows JavaScript to run outside the browser." 
    },
    { 
      question: "What are the main global objects in Node.js?", 
      answer: "global, process, Buffer, __dirname, __filename, require, module, exports." 
    },
    { 
      question: "How do you access command line arguments in Node.js?", 
      answer: "Using process.argv array. Arguments start from index 2 (0 is node path, 1 is script path)." 
    },
    { 
      question: "What is the Buffer class used for?", 
      answer: "Handling binary data in Node.js, such as file operations and network communications." 
    },
    { 
      question: "What's the difference between setTimeout and setImmediate?", 
      answer: "setTimeout schedules callback after minimum delay; setImmediate executes callback in next iteration of event loop." 
    },
    { 
      question: "How do you check the Node.js version programmatically?", 
      answer: "Using process.version property, which returns the Node.js version string." 
    }
  ],

  "Event-Driven Architecture": [
    { 
      question: "What is the EventEmitter class?", 
      answer: "A core Node.js class that allows objects to emit and listen for events, implementing the observer pattern." 
    },
    { 
      question: "What's the difference between on() and once() methods?", 
      answer: "on() registers a persistent listener; once() registers a listener that runs only once then removes itself." 
    },
    { 
      question: "What are the phases of the Node.js event loop?", 
      answer: "Timer phase, I/O callbacks, idle/prepare, poll, check, close callbacks. Plus microtask queues between phases." 
    },
    { 
      question: "What's the difference between microtasks and macrotasks?", 
      answer: "Microtasks (Promises, process.nextTick) have higher priority and run before macrotasks (setTimeout, setInterval)." 
    },
    { 
      question: "How do you handle errors in EventEmitter?", 
      answer: "Listen for 'error' events. If no error listener exists, Node.js will throw and potentially crash." 
    },
    { 
      question: "What is process.nextTick() used for?", 
      answer: "Schedules callback to execute in next iteration of event loop, before any other I/O events." 
    }
  ],

  "Modules and Package Management": [
    { 
      question: "What's the difference between require() and import?", 
      answer: "require() is CommonJS (synchronous, dynamic); import is ES6 modules (static analysis, tree-shaking)." 
    },
    { 
      question: "How does Node.js resolve modules?", 
      answer: "Checks built-in modules, then node_modules folders up the directory tree, then global modules." 
    },
    { 
      question: "What is package.json used for?", 
      answer: "Project manifest containing metadata, dependencies, scripts, and configuration for Node.js projects." 
    },
    { 
      question: "What's the difference between dependencies and devDependencies?", 
      answer: "dependencies: needed in production; devDependencies: only needed for development (testing, building)." 
    },
    { 
      question: "How do you create a global npm package?", 
      answer: "Add 'bin' field to package.json, install with npm install -g, or use npm link for development." 
    },
    { 
      question: "What is the purpose of package-lock.json?", 
      answer: "Locks exact versions of dependencies and their sub-dependencies for consistent installs across environments." 
    }
  ],

  "Asynchronous Programming": [
    { 
      question: "What are the three main patterns for async programming in Node.js?", 
      answer: "Callbacks, Promises, and async/await. Each builds on the previous for better error handling and readability." 
    },
    { 
      question: "How do you convert a callback-based function to Promise?", 
      answer: "Use util.promisify() or manually wrap in new Promise((resolve, reject) => { ... })." 
    },
    { 
      question: "What's callback hell and how do you avoid it?", 
      answer: "Deeply nested callbacks. Avoid with Promises, async/await, or breaking into named functions." 
    },
    { 
      question: "How do you handle multiple async operations in parallel?", 
      answer: "Use Promise.all() for all to complete, Promise.race() for first to complete, Promise.allSettled() for all to settle." 
    },
    { 
      question: "What happens if you don't handle a Promise rejection?", 
      answer: "Unhandled rejection warning, and in future Node.js versions, the process will terminate." 
    },
    { 
      question: "How do you handle errors in async/await?", 
      answer: "Use try/catch blocks around await statements, or .catch() on the Promise chain." 
    }
  ],

  "Express.js Framework": [
    { 
      question: "What is Express.js?", 
      answer: "Minimal and flexible Node.js web application framework providing robust features for web and mobile applications." 
    },
    { 
      question: "What is middleware in Express?", 
      answer: "Functions that execute during request-response cycle, having access to req, res, and next function." 
    },
    { 
      question: "How do you handle different HTTP methods in Express?", 
      answer: "Use app.get(), app.post(), app.put(), app.delete(), or app.all() for all methods." 
    },
    { 
      question: "What's the purpose of the next() function?", 
      answer: "Passes control to the next middleware function. If not called, request will hang." 
    },
    { 
      question: "How do you serve static files in Express?", 
      answer: "Use express.static() middleware: app.use(express.static('public'))." 
    },
    { 
      question: "How do you handle errors in Express?", 
      answer: "Use error-handling middleware with 4 parameters: (err, req, res, next). Must be defined last." 
    }
  ],

  "File System Operations": [
    { 
      question: "What's the difference between fs.readFile() and fs.readFileSync()?", 
      answer: "readFile() is asynchronous (non-blocking); readFileSync() is synchronous (blocking)." 
    },
    { 
      question: "How do you check if a file exists?", 
      answer: "Use fs.access() or fs.promises.access(). Avoid fs.exists() as it's deprecated." 
    },
    { 
      question: "What is fs.promises used for?", 
      answer: "Promise-based versions of fs methods, allowing use with async/await instead of callbacks." 
    },
    { 
      question: "How do you watch for file changes?", 
      answer: "Use fs.watch() or fs.watchFile(). fs.watch() is more efficient but less reliable across platforms." 
    },
    { 
      question: "What's the difference between fs.unlink() and fs.rmdir()?", 
      answer: "unlink() removes files; rmdir() removes directories (must be empty, use recursive option for non-empty)." 
    },
    { 
      question: "How do you get file statistics?", 
      answer: "Use fs.stat() to get file size, creation time, modification time, and type (file/directory)." 
    }
  ],

  "Streams and Networking": [
    { 
      question: "What are the four types of streams in Node.js?", 
      answer: "Readable, Writable, Transform, and Duplex streams." 
    },
    { 
      question: "What's the advantage of using streams?", 
      answer: "Memory efficient processing of large data, can process data chunk by chunk without loading everything into memory." 
    },
    { 
      question: "How do you pipe streams together?", 
      answer: "Use .pipe() method: readableStream.pipe(writableStream), or pipeline() for better error handling." 
    },
    { 
      question: "What is backpressure in streams?", 
      answer: "When writable stream can't keep up with readable stream. Node.js handles this automatically with internal buffering." 
    },
    { 
      question: "How do you create an HTTP server?", 
      answer: "Use http.createServer((req, res) => { ... }).listen(port) or Express.js for more features." 
    },
    { 
      question: "What's the difference between http and https modules?", 
      answer: "http for regular HTTP; https for SSL/TLS encrypted connections. https requires certificates." 
    }
  ],

  "Testing and Debugging": [
    { 
      question: "What testing frameworks are popular in Node.js?", 
      answer: "Jest, Mocha, Jasmine, and Node.js built-in test runner (Node 18+)." 
    },
    { 
      question: "How do you debug Node.js applications?", 
      answer: "Use node --inspect, console methods, debugger statement, or IDE debugging tools." 
    },
    { 
      question: "What is the assert module used for?", 
      answer: "Built-in module for writing test assertions, checking if conditions are true and throwing errors if not." 
    },
    { 
      question: "How do you test async functions?", 
      answer: "Return promises, use async/await in test functions, or use done() callback in callback-based tests." 
    },
    { 
      question: "What is mocking in testing?", 
      answer: "Replacing dependencies with fake implementations to isolate code under test and control behavior." 
    },
    { 
      question: "How do you measure test coverage?", 
      answer: "Use tools like nyc (Istanbul), jest --coverage, or c8 to see which code lines are tested." 
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n🚀 ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available Node.js Quiz Topics:');
  console.log('='.repeat(35));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM NODE.JS QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) || 
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });
  
  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }
  
  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));
  
  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });
  
  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    quizzes, 
    printQuiz, 
    listTopics, 
    randomQuiz, 
    searchQuestions 
  };
}

// Example usage and help
console.log('🚀 Node.js Manual Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('🔍 searchQuestions("keyword") - Search for questions');

console.log('\n💡 Example: printQuiz("Node.js Fundamentals")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("express", true) - Find Express-related questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
