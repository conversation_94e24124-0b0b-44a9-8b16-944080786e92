# JavaScript Manual Quiz System

A comprehensive quiz system covering all 21 sections of the JavaScript programming manual. Perfect for testing knowledge, interview preparation, and as a coding primer for AI assistants.

## 📚 Coverage

The quiz covers **21 comprehensive topics** with **5-10 questions each**:

1. **Basic Syntax and Data Types** - Variables, operators, type conversion
2. **Control Flow and Loops** - Conditionals, loops, iteration
3. **Functions and Arrow Functions** - Function types, parameters, scope
4. **Objects and Prototypes** - Object creation, inheritance, methods
5. **Arrays and Array Methods** - Array manipulation, iteration methods
6. **DOM Manipulation and Events** - Element selection, event handling
7. **Asynchronous JavaScript** - Promises, async/await, error handling
8. **Modules and Imports/Exports** - ES6 modules, CommonJS, dynamic imports
9. **Error Handling and Debugging** - Try/catch, custom errors, debugging
10. **ES6+ Features** - Destructuring, spread/rest, template literals
11. **Closures and Scope** - Lexical scope, closure patterns, pitfalls
12. **Classes and Inheritance** - Class syntax, inheritance, private fields
13. **Event Loop and Concurrency** - Call stack, microtasks, macrotasks
14. **Web APIs and Fetch** - HTTP requests, response handling, CORS
15. **Local Storage and Cookies** - Browser storage, persistence, limits
16. **Testing** - Jest, Mocha, mocking, TDD principles
17. **Build Tools and Bundlers** - Webpack, Rollup, optimization
18. **Frameworks Overview** - React, Vue, Angular basics
19. **TypeScript Basics** - Types, interfaces, generics
20. **Best Practices and Code Style** - Clean code, patterns, performance
21. **CLI and Node.js Basics** - Server-side JavaScript, npm, environment

## 🚀 Quick Start

### Node.js Usage
```javascript
// Load the quiz system
const quiz = require('./javascript_manual_quiz.js');

// Show all available topics
quiz.listTopics();

// Take a specific quiz
quiz.printQuiz("Basic Syntax and Data Types");

// Take quiz with answers
quiz.printQuiz("Functions and Arrow Functions", true);

// Random quiz
quiz.randomQuiz(5, true);
```

### Browser Usage
```html
<script src="javascript_manual_quiz.js"></script>
<script>
  // Functions are available globally
  listTopics();
  printQuiz("Arrays and Array Methods");
  randomQuiz(10);
</script>
```

## 📖 Available Functions

### `listTopics()`
Shows all available quiz topics with question counts.

### `printQuiz(topic, withAnswers = false)`
- **topic**: Topic name (must match exactly)
- **withAnswers**: Show answers immediately (default: false)

### `randomQuiz(questionCount = 10, withAnswers = false)`
- **questionCount**: Number of random questions (default: 10)
- **withAnswers**: Show answers immediately (default: false)

### `getQuizStats()`
Shows statistics about all quiz topics and total question count.

### `searchQuestions(keyword, withAnswers = false)`
- **keyword**: Search term to find in questions/answers
- **withAnswers**: Show answers immediately (default: false)

## 💡 Usage Examples

```javascript
// Basic topic quiz
printQuiz("Basic Syntax and Data Types");

// Quiz with immediate answers
printQuiz("Asynchronous JavaScript (Promises, Async/Await)", true);

// 5 random questions with answers
randomQuiz(5, true);

// Find all questions about arrays
searchQuestions("array", true);

// Get quiz statistics
getQuizStats();

// List all topics
listTopics();
```

## 🎯 Use Cases

### 📖 **Learning & Study**
- Test knowledge after reading the manual
- Identify weak areas for focused study
- Quick review before coding sessions

### 💼 **Interview Preparation**
- Practice common JavaScript interview questions
- Review fundamental concepts
- Test understanding of advanced topics

### 🤖 **AI Coding Assistant Primer**
- Use as context for AI assistants
- Quick reference for JavaScript concepts
- Validate AI responses against correct answers

### 👥 **Teaching & Training**
- Classroom quizzes and assessments
- Self-paced learning modules
- Knowledge verification

## 📊 Quiz Statistics

- **Total Questions**: 140+ comprehensive questions
- **Topics Covered**: 21 essential JavaScript areas
- **Question Types**: Conceptual, practical, best practices
- **Difficulty**: Beginner to advanced levels

## 🔧 Customization

The quiz data is stored in a simple object structure:

```javascript
const quizzes = {
  "Topic Name": [
    { 
      question: "What is...?", 
      answer: "Detailed explanation..." 
    },
    // More questions...
  ]
};
```

You can easily:
- Add new questions to existing topics
- Create new topic sections
- Modify existing questions/answers
- Export specific topics

## 📝 Question Format

Each question includes:
- **Clear, concise question** focusing on key concepts
- **Comprehensive answer** with explanations
- **Practical examples** where applicable
- **Best practices** and common pitfalls

## 🎉 Getting Started

1. **Load the quiz system**:
   ```bash
   node javascript_manual_quiz.js
   ```

2. **Start with basics**:
   ```javascript
   printQuiz("Basic Syntax and Data Types", true);
   ```

3. **Try a random quiz**:
   ```javascript
   randomQuiz(5, true);
   ```

4. **Explore topics**:
   ```javascript
   listTopics();
   ```

Perfect for developers at any level who want to master JavaScript fundamentals and advanced concepts!
