{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "c47163a8",
   "metadata": {},
   "source": [
    "# Comprehensive Node.js Programming Manual\n",
    "\n",
    "This manual serves as a complete reference for Node.js development, from server-side fundamentals to advanced concepts. Each section includes concise explanations and practical code examples.\n",
    "\n",
    "## Table of Contents\n",
    "1. [Node.js Fundamentals](#fundamentals)\n",
    "2. [Node.js Runtime and Event-Driven Architecture](#runtime)\n",
    "3. [Modules and Package Management](#modules)\n",
    "4. [Asynchronous Programming](#async)\n",
    "5. [Express.js Framework](#express)\n",
    "6. [File System Operations](#filesystem)\n",
    "7. [Streams and Networking](#streams)\n",
    "8. [Database Integration](#database)\n",
    "9. [Authentication and Security](#security)\n",
    "10. [Testing and Debugging](#testing)\n",
    "11. [Deployment and Production](#deployment)\n",
    "12. [Performance Optimization](#performance)"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "73fefecc",
   "metadata": {},
   "source": [
    "# Node.js Fundamentals {#fundamentals}\n",
    "\n",
    "Node.js is a JavaScript runtime built on Chrome's V8 engine that allows JavaScript to run on servers and desktop applications."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e84d17fb",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Node.js Global Objects\n",
    "console.log('=== Node.js Global Objects ===');\n",
    "\n",
    "// Process object - information about current Node.js process\n",
    "console.log('Node.js version:', process.version);\n",
    "console.log('Platform:', process.platform);\n",
    "console.log('Architecture:', process.arch);\n",
    "console.log('Process ID:', process.pid);\n",
    "console.log('Current working directory:', process.cwd());\n",
    "console.log('Node.js executable path:', process.execPath);\n",
    "\n",
    "// Command line arguments\n",
    "console.log('\\nCommand line arguments:');\n",
    "process.argv.forEach((arg, index) => {\n",
    "    console.log(`${index}: ${arg}`);\n",
    "});\n",
    "\n",
    "// Environment variables\n",
    "console.log('\\nEnvironment variables:');\n",
    "console.log('NODE_ENV:', process.env.NODE_ENV || 'not set');\n",
    "console.log('PATH exists:', !!process.env.PATH);\n",
    "\n",
    "// Global objects\n",
    "console.log('\\nGlobal objects available:');\n",
    "console.log('- global: Global namespace');\n",
    "console.log('- process: Current process info');\n",
    "console.log('- Buffer: Binary data handling');\n",
    "console.log('- __dirname: Current directory path');\n",
    "console.log('- __filename: Current file path');\n",
    "console.log('- require: Module loading function');\n",
    "console.log('- module: Current module object');\n",
    "console.log('- exports: Module exports object');\n",
    "\n",
    "// Buffer example\n",
    "console.log('\\n=== Buffer Examples ===');\n",
    "const buffer1 = Buffer.from('Hello World', 'utf8');\n",
    "console.log('Buffer from string:', buffer1);\n",
    "console.log('Buffer to string:', buffer1.toString());\n",
    "console.log('Buffer length:', buffer1.length);\n",
    "\n",
    "const buffer2 = Buffer.alloc(10, 'a');\n",
    "console.log('Allocated buffer:', buffer2.toString());\n",
    "\n",
    "// Timers\n",
    "console.log('\\n=== Node.js Timers ===');\n",
    "console.log('Setting timeout...');\n",
    "const timeoutId = setTimeout(() => {\n",
    "    console.log('Timeout executed!');\n",
    "}, 1000);\n",
    "\n",
    "const intervalId = setInterval(() => {\n",
    "    console.log('Interval tick');\n",
    "}, 2000);\n",
    "\n",
    "// Clear interval after 5 seconds\n",
    "setTimeout(() => {\n",
    "    clearInterval(intervalId);\n",
    "    console.log('Interval cleared');\n",
    "}, 5000);\n",
    "\n",
    "// Immediate execution\n",
    "setImmediate(() => {\n",
    "    console.log('Immediate execution');\n",
    "});\n",
    "\n",
    "// Process next tick\n",
    "process.nextTick(() => {\n",
    "    console.log('Next tick callback');\n",
    "});\n",
    "\n",
    "console.log('Synchronous code completed');"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "71c71fe2",
   "metadata": {},
   "source": [
    "# Node.js Runtime and Event-Driven Architecture {#runtime}\n",
    "\n",
    "Node.js uses an event-driven, non-blocking I/O model that makes it lightweight and efficient for data-intensive real-time applications."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "ff42471f",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Event Emitter Pattern\n",
    "const EventEmitter = require('events');\n",
    "\n",
    "console.log('=== Event Emitter Pattern ===');\n",
    "\n",
    "// Create custom event emitter\n",
    "class MyEmitter extends EventEmitter {}\n",
    "const myEmitter = new MyEmitter();\n",
    "\n",
    "// Register event listeners\n",
    "myEmitter.on('event', (data) => {\n",
    "    console.log('Event received:', data);\n",
    "});\n",
    "\n",
    "myEmitter.on('error', (error) => {\n",
    "    console.error('Error occurred:', error.message);\n",
    "});\n",
    "\n",
    "// One-time listener\n",
    "myEmitter.once('startup', () => {\n",
    "    console.log('Application started (this will only run once)');\n",
    "});\n",
    "\n",
    "// Emit events\n",
    "myEmitter.emit('startup');\n",
    "myEmitter.emit('event', { message: 'Hello from event!' });\n",
    "myEmitter.emit('startup'); // Won't trigger the once listener\n",
    "\n",
    "// Event Loop Demonstration\n",
    "console.log('\\n=== Event Loop Phases ===');\n",
    "\n",
    "console.log('1. Start');\n",
    "\n",
    "// Timer phase\n",
    "setTimeout(() => {\n",
    "    console.log('4. Timer phase (setTimeout)');\n",
    "}, 0);\n",
    "\n",
    "// I/O callbacks phase\n",
    "setImmediate(() => {\n",
    "    console.log('5. Check phase (setImmediate)');\n",
    "});\n",
    "\n",
    "// Next tick queue (highest priority)\n",
    "process.nextTick(() => {\n",
    "    console.log('2. Next tick queue');\n",
    "});\n",
    "\n",
    "// Promise microtask queue\n",
    "Promise.resolve().then(() => {\n",
    "    console.log('3. Promise microtask queue');\n",
    "});\n",
    "\n",
    "console.log('6. End of main thread');\n",
    "\n",
    "// Custom Event Emitter Example\n",
    "console.log('\\n=== Custom Event Emitter ===');\n",
    "\n",
    "class Logger extends EventEmitter {\n",
    "    log(level, message) {\n",
    "        this.emit('log', { level, message, timestamp: new Date() });\n",
    "    }\n",
    "    \n",
    "    error(message) {\n",
    "        this.emit('error', { message, timestamp: new Date() });\n",
    "    }\n",
    "}\n",
    "\n",
    "const logger = new Logger();\n",
    "\n",
    "logger.on('log', (data) => {\n",
    "    console.log(`[${data.timestamp.toISOString()}] ${data.level.toUpperCase()}: ${data.message}`);\n",
    "});\n",
    "\n",
    "logger.on('error', (data) => {\n",
    "    console.error(`[${data.timestamp.toISOString()}] ERROR: ${data.message}`);\n",
    "});\n",
    "\n",
    "logger.log('info', 'Application started');\n",
    "logger.log('debug', 'Debug information');\n",
    "logger.error('Something went wrong');\n",
    "\n",
    "// Memory usage monitoring\n",
    "console.log('\\n=== Memory Usage ===');\n",
    "const memUsage = process.memoryUsage();\n",
    "console.log('Memory usage:');\n",
    "console.log(`RSS: ${Math.round(memUsage.rss / 1024 / 1024)} MB`);\n",
    "console.log(`Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`);\n",
    "console.log(`Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`);\n",
    "console.log(`External: ${Math.round(memUsage.external / 1024 / 1024)} MB`);\n",
    "\n",
    "// CPU usage\n",
    "const startUsage = process.cpuUsage();\n",
    "setTimeout(() => {\n",
    "    const endUsage = process.cpuUsage(startUsage);\n",
    "    console.log('\\nCPU usage:');\n",
    "    console.log(`User: ${endUsage.user} microseconds`);\n",
    "    console.log(`System: ${endUsage.system} microseconds`);\n",
    "}, 100);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "b3ad4049",
   "metadata": {},
   "source": [
    "# Modules and Package Management {#modules}\n",
    "\n",
    "Node.js uses CommonJS modules by default, with support for ES6 modules. NPM (Node Package Manager) handles package installation and dependency management."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "4adc818d",
   "metadata": {},
   "outputs": [],
   "source": [
    "// CommonJS Module System\n",
    "console.log('=== CommonJS Modules ===');\n",
    "\n",
    "// Built-in modules\n",
    "const fs = require('fs');\n",
    "const path = require('path');\n",
    "const os = require('os');\n",
    "const crypto = require('crypto');\n",
    "const util = require('util');\n",
    "\n",
    "console.log('Built-in modules loaded successfully');\n",
    "\n",
    "// Module information\n",
    "console.log('\\nModule information:');\n",
    "console.log('Current module filename:', __filename);\n",
    "console.log('Current module directory:', __dirname);\n",
    "console.log('Module exports:', typeof module.exports);\n",
    "console.log('Module require:', typeof require);\n",
    "\n",
    "// Creating and exporting modules\n",
    "console.log('\\n=== Module Export Patterns ===');\n",
    "\n",
    "// Pattern 1: Single function export\n",
    "function createSingleExport() {\n",
    "    return function greet(name) {\n",
    "        return `Hello, ${name}!`;\n",
    "    };\n",
    "}\n",
    "\n",
    "// Pattern 2: Object with multiple exports\n",
    "const mathUtils = {\n",
    "    add: (a, b) => a + b,\n",
    "    subtract: (a, b) => a - b,\n",
    "    multiply: (a, b) => a * b,\n",
    "    divide: (a, b) => {\n",
    "        if (b === 0) throw new Error('Division by zero');\n",
    "        return a / b;\n",
    "    }\n",
    "};\n",
    "\n",
    "// Pattern 3: Class export\n",
    "class Calculator {\n",
    "    constructor() {\n",
    "        this.result = 0;\n",
    "    }\n",
    "    \n",
    "    add(value) {\n",
    "        this.result += value;\n",
    "        return this;\n",
    "    }\n",
    "    \n",
    "    getResult() {\n",
    "        return this.result;\n",
    "    }\n",
    "    \n",
    "    reset() {\n",
    "        this.result = 0;\n",
    "        return this;\n",
    "    }\n",
    "}\n",
    "\n",
    "// Demonstrate exports\n",
    "console.log('Math utils:', mathUtils.add(5, 3));\n",
    "const calc = new Calculator();\n",
    "console.log('Calculator result:', calc.add(10).add(5).getResult());\n",
    "\n",
    "// Path module utilities\n",
    "console.log('\\n=== Path Module ===');\n",
    "const filePath = '/users/john/documents/file.txt';\n",
    "console.log('Original path:', filePath);\n",
    "console.log('Directory name:', path.dirname(filePath));\n",
    "console.log('Base name:', path.basename(filePath));\n",
    "console.log('Extension:', path.extname(filePath));\n",
    "console.log('Parsed path:', path.parse(filePath));\n",
    "\n",
    "// Join paths\n",
    "const joinedPath = path.join('/users', 'john', 'documents', 'file.txt');\n",
    "console.log('Joined path:', joinedPath);\n",
    "\n",
    "// Resolve paths\n",
    "const resolvedPath = path.resolve('documents', 'file.txt');\n",
    "console.log('Resolved path:', resolvedPath);\n",
    "\n",
    "// OS module utilities\n",
    "console.log('\\n=== OS Module ===');\n",
    "console.log('Platform:', os.platform());\n",
    "console.log('Architecture:', os.arch());\n",
    "console.log('CPU count:', os.cpus().length);\n",
    "console.log('Total memory:', Math.round(os.totalmem() / 1024 / 1024 / 1024), 'GB');\n",
    "console.log('Free memory:', Math.round(os.freemem() / 1024 / 1024 / 1024), 'GB');\n",
    "console.log('Uptime:', Math.round(os.uptime() / 3600), 'hours');\n",
    "console.log('Home directory:', os.homedir());\n",
    "console.log('Temp directory:', os.tmpdir());\n",
    "\n",
    "// Crypto module utilities\n",
    "console.log('\\n=== Crypto Module ===');\n",
    "const hash = crypto.createHash('sha256');\n",
    "hash.update('Hello World');\n",
    "console.log('SHA256 hash:', hash.digest('hex'));\n",
    "\n",
    "// Generate random bytes\n",
    "const randomBytes = crypto.randomBytes(16);\n",
    "console.log('Random bytes (hex):', randomBytes.toString('hex'));\n",
    "\n",
    "// UUID generation\n",
    "const { randomUUID } = crypto;\n",
    "console.log('Random UUID:', randomUUID());\n",
    "\n",
    "// Util module utilities\n",
    "console.log('\\n=== Util Module ===');\n",
    "const obj = { name: 'John', age: 30, hobbies: ['reading', 'coding'] };\n",
    "console.log('Inspected object:', util.inspect(obj, { colors: false, depth: 2 }));\n",
    "\n",
    "// Promisify callback-based functions\n",
    "const readFileAsync = util.promisify(fs.readFile);\n",
    "console.log('Promisified readFile created');\n",
    "\n",
    "// Format strings\n",
    "const formatted = util.format('Hello %s, you are %d years old', 'Alice', 25);\n",
    "console.log('Formatted string:', formatted);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "fa36a827",
   "metadata": {},
   "source": [
    "# Asynchronous Programming {#async}\n",
    "\n",
    "Node.js excels at handling asynchronous operations. Understanding callbacks, promises, and async/await is crucial for effective Node.js development."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "56c306d3",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Callback Pattern\n",
    "console.log('=== Callback Pattern ===');\n",
    "\n",
    "function readFileCallback(filename, callback) {\n",
    "    // Simulate async file reading\n",
    "    setTimeout(() => {\n",
    "        if (filename.endsWith('.txt')) {\n",
    "            callback(null, `Content of ${filename}`);\n",
    "        } else {\n",
    "            callback(new Error('Invalid file type'));\n",
    "        }\n",
    "    }, 100);\n",
    "}\n",
    "\n",
    "readFileCallback('example.txt', (err, data) => {\n",
    "    if (err) {\n",
    "        console.error('Callback error:', err.message);\n",
    "    } else {\n",
    "        console.log('Callback success:', data);\n",
    "    }\n",
    "});\n",
    "\n",
    "// Promise Pattern\n",
    "console.log('\\n=== Promise Pattern ===');\n",
    "\n",
    "function readFilePromise(filename) {\n",
    "    return new Promise((resolve, reject) => {\n",
    "        setTimeout(() => {\n",
    "            if (filename.endsWith('.txt')) {\n",
    "                resolve(`Content of ${filename}`);\n",
    "            } else {\n",
    "                reject(new Error('Invalid file type'));\n",
    "            }\n",
    "        }, 100);\n",
    "    });\n",
    "}\n",
    "\n",
    "readFilePromise('example.txt')\n",
    "    .then(data => {\n",
    "        console.log('Promise success:', data);\n",
    "    })\n",
    "    .catch(err => {\n",
    "        console.error('Promise error:', err.message);\n",
    "    });\n",
    "\n",
    "// Async/Await Pattern\n",
    "console.log('\\n=== Async/Await Pattern ===');\n",
    "\n",
    "async function readFileAsync(filename) {\n",
    "    try {\n",
    "        const data = await readFilePromise(filename);\n",
    "        console.log('Async/await success:', data);\n",
    "        return data;\n",
    "    } catch (err) {\n",
    "        console.error('Async/await error:', err.message);\n",
    "        throw err;\n",
    "    }\n",
    "}\n",
    "\n",
    "readFileAsync('example.txt');\n",
    "\n",
    "// Parallel Execution\n",
    "console.log('\\n=== Parallel Execution ===');\n",
    "\n",
    "async function processMultipleFiles() {\n",
    "    const files = ['file1.txt', 'file2.txt', 'file3.txt'];\n",
    "    \n",
    "    try {\n",
    "        // Sequential processing\n",
    "        console.log('Sequential processing:');\n",
    "        const start1 = Date.now();\n",
    "        for (const file of files) {\n",
    "            await readFilePromise(file);\n",
    "        }\n",
    "        console.log(`Sequential time: ${Date.now() - start1}ms`);\n",
    "        \n",
    "        // Parallel processing\n",
    "        console.log('\\nParallel processing:');\n",
    "        const start2 = Date.now();\n",
    "        const results = await Promise.all(\n",
    "            files.map(file => readFilePromise(file))\n",
    "        );\n",
    "        console.log(`Parallel time: ${Date.now() - start2}ms`);\n",
    "        console.log('Results:', results.length, 'files processed');\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error('Processing error:', error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "processMultipleFiles();\n",
    "\n",
    "// Error Handling Patterns\n",
    "console.log('\\n=== Error Handling ===');\n",
    "\n",
    "// Callback error handling\n",
    "function handleCallbackErrors(callback) {\n",
    "    try {\n",
    "        // Simulate operation that might throw\n",
    "        const result = Math.random() > 0.5 ? 'success' : null;\n",
    "        if (!result) {\n",
    "            return callback(new Error('Operation failed'));\n",
    "        }\n",
    "        callback(null, result);\n",
    "    } catch (error) {\n",
    "        callback(error);\n",
    "    }\n",
    "}\n",
    "\n",
    "// Promise error handling\n",
    "async function handlePromiseErrors() {\n",
    "    try {\n",
    "        const result = await readFilePromise('invalid.doc');\n",
    "        console.log('This won\\'t execute');\n",
    "    } catch (error) {\n",
    "        console.log('Caught promise error:', error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "handlePromiseErrors();\n",
    "\n",
    "// Event-driven async patterns\n",
    "console.log('\\n=== Event-Driven Async ===');\n",
    "\n",
    "const EventEmitter = require('events');\n",
    "\n",
    "class AsyncProcessor extends EventEmitter {\n",
    "    async processData(data) {\n",
    "        this.emit('start', { data });\n",
    "        \n",
    "        try {\n",
    "            // Simulate processing\n",
    "            await new Promise(resolve => setTimeout(resolve, 200));\n",
    "            \n",
    "            const result = `Processed: ${data}`;\n",
    "            this.emit('complete', { result });\n",
    "            return result;\n",
    "            \n",
    "        } catch (error) {\n",
    "            this.emit('error', error);\n",
    "            throw error;\n",
    "        }\n",
    "    }\n",
    "}\n",
    "\n",
    "const processor = new AsyncProcessor();\n",
    "\n",
    "processor.on('start', ({ data }) => {\n",
    "    console.log('Processing started for:', data);\n",
    "});\n",
    "\n",
    "processor.on('complete', ({ result }) => {\n",
    "    console.log('Processing completed:', result);\n",
    "});\n",
    "\n",
    "processor.on('error', (error) => {\n",
    "    console.error('Processing error:', error.message);\n",
    "});\n",
    "\n",
    "processor.processData('sample data');"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "dfa02e04",
   "metadata": {},
   "source": [
    "# Express.js Framework {#express}\n",
    "\n",
    "Express.js is a minimal and flexible Node.js web application framework that provides a robust set of features for web and mobile applications."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "9440a6fd",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Express.js Basic Setup\n",
    "console.log('=== Express.js Framework ===');\n",
    "\n",
    "// Note: This shows Express.js concepts and syntax\n",
    "// To run, you need: npm install express\n",
    "\n",
    "/*\n",
    "const express = require('express');\n",
    "const app = express();\n",
    "const PORT = process.env.PORT || 3000;\n",
    "\n",
    "// Middleware\n",
    "app.use(express.json()); // Parse JSON bodies\n",
    "app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies\n",
    "app.use(express.static('public')); // Serve static files\n",
    "\n",
    "// Custom middleware\n",
    "app.use((req, res, next) => {\n",
    "    console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);\n",
    "    next();\n",
    "});\n",
    "\n",
    "// Routes\n",
    "app.get('/', (req, res) => {\n",
    "    res.json({ message: 'Welcome to Express.js API' });\n",
    "});\n",
    "\n",
    "app.get('/users', (req, res) => {\n",
    "    const users = [\n",
    "        { id: 1, name: 'Alice', email: '<EMAIL>' },\n",
    "        { id: 2, name: 'Bob', email: '<EMAIL>' }\n",
    "    ];\n",
    "    res.json(users);\n",
    "});\n",
    "\n",
    "app.get('/users/:id', (req, res) => {\n",
    "    const userId = parseInt(req.params.id);\n",
    "    const user = { id: userId, name: `User ${userId}`, email: `user${userId}@example.com` };\n",
    "    res.json(user);\n",
    "});\n",
    "\n",
    "app.post('/users', (req, res) => {\n",
    "    const { name, email } = req.body;\n",
    "    const newUser = {\n",
    "        id: Date.now(),\n",
    "        name,\n",
    "        email,\n",
    "        createdAt: new Date()\n",
    "    };\n",
    "    res.status(201).json(newUser);\n",
    "});\n",
    "\n",
    "app.put('/users/:id', (req, res) => {\n",
    "    const userId = parseInt(req.params.id);\n",
    "    const { name, email } = req.body;\n",
    "    const updatedUser = {\n",
    "        id: userId,\n",
    "        name,\n",
    "        email,\n",
    "        updatedAt: new Date()\n",
    "    };\n",
    "    res.json(updatedUser);\n",
    "});\n",
    "\n",
    "app.delete('/users/:id', (req, res) => {\n",
    "    const userId = parseInt(req.params.id);\n",
    "    res.json({ message: `User ${userId} deleted` });\n",
    "});\n",
    "\n",
    "// Error handling middleware\n",
    "app.use((err, req, res, next) => {\n",
    "    console.error(err.stack);\n",
    "    res.status(500).json({ error: 'Something went wrong!' });\n",
    "});\n",
    "\n",
    "// 404 handler\n",
    "app.use((req, res) => {\n",
    "    res.status(404).json({ error: 'Route not found' });\n",
    "});\n",
    "\n",
    "// Start server\n",
    "app.listen(PORT, () => {\n",
    "    console.log(`Server running on port ${PORT}`);\n",
    "});\n",
    "*/\n",
    "\n",
    "// Express.js Concepts Demonstration\n",
    "console.log('Express.js concepts:');\n",
    "console.log('1. Middleware - Functions that execute during request-response cycle');\n",
    "console.log('2. Routing - Defining application endpoints');\n",
    "console.log('3. Request/Response objects - Enhanced HTTP objects');\n",
    "console.log('4. Error handling - Centralized error management');\n",
    "console.log('5. Static files - Serving CSS, JS, images');\n",
    "\n",
    "// Simulated Express-like functionality\n",
    "class SimpleExpress {\n",
    "    constructor() {\n",
    "        this.routes = [];\n",
    "        this.middlewares = [];\n",
    "    }\n",
    "    \n",
    "    use(middleware) {\n",
    "        this.middlewares.push(middleware);\n",
    "    }\n",
    "    \n",
    "    get(path, handler) {\n",
    "        this.routes.push({ method: 'GET', path, handler });\n",
    "    }\n",
    "    \n",
    "    post(path, handler) {\n",
    "        this.routes.push({ method: 'POST', path, handler });\n",
    "    }\n",
    "    \n",
    "    handleRequest(method, path, body = {}) {\n",
    "        console.log(`\\nHandling ${method} ${path}`);\n",
    "        \n",
    "        // Execute middlewares\n",
    "        this.middlewares.forEach(middleware => {\n",
    "            middleware({ method, path, body });\n",
    "        });\n",
    "        \n",
    "        // Find matching route\n",
    "        const route = this.routes.find(r => r.method === method && r.path === path);\n",
    "        \n",
    "        if (route) {\n",
    "            const req = { method, path, body };\n",
    "            const res = {\n",
    "                json: (data) => console.log('Response:', JSON.stringify(data)),\n",
    "                status: (code) => ({ json: (data) => console.log(`Status ${code}:`, JSON.stringify(data)) })\n",
    "            };\n",
    "            route.handler(req, res);\n",
    "        } else {\n",
    "            console.log('404 - Route not found');\n",
    "        }\n",
    "    }\n",
    "}\n",
    "\n",
    "// Demo usage\n",
    "const app = new SimpleExpress();\n",
    "\n",
    "// Add middleware\n",
    "app.use((req) => {\n",
    "    console.log(`Middleware: ${req.method} ${req.path}`);\n",
    "});\n",
    "\n",
    "// Add routes\n",
    "app.get('/api/users', (req, res) => {\n",
    "    res.json([{ id: 1, name: 'Alice' }, { id: 2, name: 'Bob' }]);\n",
    "});\n",
    "\n",
    "app.post('/api/users', (req, res) => {\n",
    "    res.status(201).json({ id: 3, ...req.body });\n",
    "});\n",
    "\n",
    "// Test requests\n",
    "app.handleRequest('GET', '/api/users');\n",
    "app.handleRequest('POST', '/api/users', { name: 'Charlie', email: '<EMAIL>' });\n",
    "app.handleRequest('GET', '/api/nonexistent');"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "bc9108b2",
   "metadata": {},
   "source": [
    "# File System Operations {#filesystem}\n",
    "\n",
    "Node.js provides comprehensive file system operations through the 'fs' module, supporting both synchronous and asynchronous operations."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e5eb596c",
   "metadata": {},
   "outputs": [],
   "source": [
    "// File System Operations\n",
    "const fs = require('fs');\n",
    "const path = require('path');\n",
    "const { promisify } = require('util');\n",
    "\n",
    "console.log('=== File System Operations ===');\n",
    "\n",
    "// Promisified versions for async/await\n",
    "const readFileAsync = promisify(fs.readFile);\n",
    "const writeFileAsync = promisify(fs.writeFile);\n",
    "const readdirAsync = promisify(fs.readdir);\n",
    "const statAsync = promisify(fs.stat);\n",
    "\n",
    "// Or use fs.promises (Node.js 10+)\n",
    "const fsPromises = fs.promises;\n",
    "\n",
    "// File Reading Examples\n",
    "console.log('\\n=== File Reading ===');\n",
    "\n",
    "// Synchronous reading (blocking)\n",
    "try {\n",
    "    // This would read an actual file if it exists\n",
    "    // const data = fs.readFileSync('example.txt', 'utf8');\n",
    "    console.log('Synchronous reading: Would block until complete');\n",
    "} catch (error) {\n",
    "    console.log('Sync read error (expected):', error.code);\n",
    "}\n",
    "\n",
    "// Asynchronous reading with callbacks\n",
    "fs.readFile('package.json', 'utf8', (err, data) => {\n",
    "    if (err) {\n",
    "        console.log('Async read error (expected):', err.code);\n",
    "    } else {\n",
    "        console.log('Async read success: File length', data.length);\n",
    "    }\n",
    "});\n",
    "\n",
    "// Asynchronous reading with promises\n",
    "async function readFileExample() {\n",
    "    try {\n",
    "        const data = await fsPromises.readFile('package.json', 'utf8');\n",
    "        console.log('Promise read success: File exists');\n",
    "    } catch (error) {\n",
    "        console.log('Promise read error (expected):', error.code);\n",
    "    }\n",
    "}\n",
    "\n",
    "readFileExample();\n",
    "\n",
    "// File Writing Examples\n",
    "console.log('\\n=== File Writing ===');\n",
    "\n",
    "const sampleData = `# Sample File\n",
    "This is a sample file created by Node.js\n",
    "Created at: ${new Date().toISOString()}\n",
    "Process ID: ${process.pid}\n",
    "`;\n",
    "\n",
    "// Write file asynchronously\n",
    "async function writeFileExample() {\n",
    "    try {\n",
    "        await fsPromises.writeFile('sample.txt', sampleData, 'utf8');\n",
    "        console.log('File written successfully');\n",
    "        \n",
    "        // Read it back\n",
    "        const readData = await fsPromises.readFile('sample.txt', 'utf8');\n",
    "        console.log('File content length:', readData.length);\n",
    "        \n",
    "        // Append to file\n",
    "        await fsPromises.appendFile('sample.txt', '\\nAppended line\\n');\n",
    "        console.log('Data appended to file');\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error('File operation error:', error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "writeFileExample();\n",
    "\n",
    "// Directory Operations\n",
    "console.log('\\n=== Directory Operations ===');\n",
    "\n",
    "async function directoryOperations() {\n",
    "    try {\n",
    "        // Create directory\n",
    "        await fsPromises.mkdir('test-dir', { recursive: true });\n",
    "        console.log('Directory created');\n",
    "        \n",
    "        // List directory contents\n",
    "        const files = await fsPromises.readdir('.');\n",
    "        console.log('Current directory files:', files.slice(0, 5), '...');\n",
    "        \n",
    "        // Get file stats\n",
    "        const stats = await fsPromises.stat('.');\n",
    "        console.log('Directory stats:');\n",
    "        console.log('  Is directory:', stats.isDirectory());\n",
    "        console.log('  Is file:', stats.isFile());\n",
    "        console.log('  Size:', stats.size, 'bytes');\n",
    "        console.log('  Modified:', stats.mtime.toISOString());\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error('Directory operation error:', error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "directoryOperations();\n",
    "\n",
    "// File Watching\n",
    "console.log('\\n=== File Watching ===');\n",
    "\n",
    "// Watch for file changes\n",
    "try {\n",
    "    const watcher = fs.watch('.', { recursive: false }, (eventType, filename) => {\n",
    "        if (filename) {\n",
    "            console.log(`File ${filename} changed: ${eventType}`);\n",
    "        }\n",
    "    });\n",
    "    \n",
    "    // Stop watching after 5 seconds\n",
    "    setTimeout(() => {\n",
    "        watcher.close();\n",
    "        console.log('File watching stopped');\n",
    "    }, 5000);\n",
    "    \n",
    "} catch (error) {\n",
    "    console.error('File watching error:', error.message);\n",
    "}\n",
    "\n",
    "// File Utilities\n",
    "console.log('\\n=== File Utilities ===');\n",
    "\n",
    "class FileUtils {\n",
    "    static async exists(filePath) {\n",
    "        try {\n",
    "            await fsPromises.access(filePath);\n",
    "            return true;\n",
    "        } catch {\n",
    "            return false;\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    static async copyFile(source, destination) {\n",
    "        try {\n",
    "            await fsPromises.copyFile(source, destination);\n",
    "            return true;\n",
    "        } catch (error) {\n",
    "            console.error('Copy error:', error.message);\n",
    "            return false;\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    static async deleteFile(filePath) {\n",
    "        try {\n",
    "            await fsPromises.unlink(filePath);\n",
    "            return true;\n",
    "        } catch (error) {\n",
    "            console.error('Delete error:', error.message);\n",
    "            return false;\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    static async getFileInfo(filePath) {\n",
    "        try {\n",
    "            const stats = await fsPromises.stat(filePath);\n",
    "            return {\n",
    "                size: stats.size,\n",
    "                created: stats.birthtime,\n",
    "                modified: stats.mtime,\n",
    "                isFile: stats.isFile(),\n",
    "                isDirectory: stats.isDirectory()\n",
    "            };\n",
    "        } catch (error) {\n",
    "            return null;\n",
    "        }\n",
    "    }\n",
    "}\n",
    "\n",
    "// Test file utilities\n",
    "async function testFileUtils() {\n",
    "    const exists = await FileUtils.exists('sample.txt');\n",
    "    console.log('Sample file exists:', exists);\n",
    "    \n",
    "    if (exists) {\n",
    "        const info = await FileUtils.getFileInfo('sample.txt');\n",
    "        console.log('File info:', info);\n",
    "    }\n",
    "}\n",
    "\n",
    "setTimeout(testFileUtils, 1000); // Wait for file creation"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "b1d7dad6",
   "metadata": {},
   "source": [
    "# Streams and Networking {#streams}\n",
    "\n",
    "Streams are a powerful way to handle data in Node.js, especially for large files or real-time data. Node.js also provides robust networking capabilities."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "6dbdc6d7",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Streams in Node.js\n",
    "const fs = require('fs');\n",
    "const { Readable, Writable, Transform, pipeline } = require('stream');\n",
    "const { promisify } = require('util');\n",
    "const pipelineAsync = promisify(pipeline);\n",
    "\n",
    "console.log('=== Streams in Node.js ===');\n",
    "\n",
    "// Readable Stream Example\n",
    "class NumberStream extends Readable {\n",
    "    constructor(options) {\n",
    "        super(options);\n",
    "        this.current = 1;\n",
    "        this.max = 10;\n",
    "    }\n",
    "    \n",
    "    _read() {\n",
    "        if (this.current <= this.max) {\n",
    "            this.push(`Number: ${this.current}\\n`);\n",
    "            this.current++;\n",
    "        } else {\n",
    "            this.push(null); // End of stream\n",
    "        }\n",
    "    }\n",
    "}\n",
    "\n",
    "// Writable Stream Example\n",
    "class LogStream extends Writable {\n",
    "    constructor(options) {\n",
    "        super(options);\n",
    "        this.logs = [];\n",
    "    }\n",
    "    \n",
    "    _write(chunk, encoding, callback) {\n",
    "        const data = chunk.toString().trim();\n",
    "        this.logs.push(`[${new Date().toISOString()}] ${data}`);\n",
    "        console.log('Logged:', data);\n",
    "        callback();\n",
    "    }\n",
    "    \n",
    "    getLogs() {\n",
    "        return this.logs;\n",
    "    }\n",
    "}\n",
    "\n",
    "// Transform Stream Example\n",
    "class UpperCaseTransform extends Transform {\n",
    "    _transform(chunk, encoding, callback) {\n",
    "        const upperCased = chunk.toString().toUpperCase();\n",
    "        this.push(upperCased);\n",
    "        callback();\n",
    "    }\n",
    "}\n",
    "\n",
    "// Using streams\n",
    "console.log('\\nUsing custom streams:');\n",
    "const numberStream = new NumberStream();\n",
    "const upperCaseTransform = new UpperCaseTransform();\n",
    "const logStream = new LogStream();\n",
    "\n",
    "// Pipe streams together\n",
    "numberStream\n",
    "    .pipe(upperCaseTransform)\n",
    "    .pipe(logStream);\n",
    "\n",
    "// File Streams\n",
    "console.log('\\n=== File Streams ===');\n",
    "\n",
    "async function fileStreamExample() {\n",
    "    try {\n",
    "        // Create a large file content\n",
    "        const largeContent = 'This is a line of text.\\n'.repeat(1000);\n",
    "        await fs.promises.writeFile('large-file.txt', largeContent);\n",
    "        \n",
    "        // Read file using streams (memory efficient)\n",
    "        const readStream = fs.createReadStream('large-file.txt', { \n",
    "            encoding: 'utf8',\n",
    "            highWaterMark: 1024 // 1KB chunks\n",
    "        });\n",
    "        \n",
    "        const writeStream = fs.createWriteStream('output-file.txt');\n",
    "        \n",
    "        // Transform stream to add line numbers\n",
    "        let lineNumber = 1;\n",
    "        const addLineNumbers = new Transform({\n",
    "            transform(chunk, encoding, callback) {\n",
    "                const lines = chunk.toString().split('\\n');\n",
    "                const numberedLines = lines.map(line => {\n",
    "                    if (line.trim()) {\n",
    "                        return `${lineNumber++}: ${line}`;\n",
    "                    }\n",
    "                    return line;\n",
    "                }).join('\\n');\n",
    "                \n",
    "                this.push(numberedLines);\n",
    "                callback();\n",
    "            }\n",
    "        });\n",
    "        \n",
    "        // Use pipeline for better error handling\n",
    "        await pipelineAsync(\n",
    "            readStream,\n",
    "            addLineNumbers,\n",
    "            writeStream\n",
    "        );\n",
    "        \n",
    "        console.log('File processing completed');\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error('File stream error:', error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "fileStreamExample();\n",
    "\n",
    "// HTTP Networking\n",
    "console.log('\\n=== HTTP Networking ===');\n",
    "\n",
    "const http = require('http');\n",
    "const https = require('https');\n",
    "const url = require('url');\n",
    "\n",
    "// HTTP Client Example\n",
    "function makeHttpRequest(targetUrl) {\n",
    "    return new Promise((resolve, reject) => {\n",
    "        const parsedUrl = url.parse(targetUrl);\n",
    "        const client = parsedUrl.protocol === 'https:' ? https : http;\n",
    "        \n",
    "        const req = client.request({\n",
    "            hostname: parsedUrl.hostname,\n",
    "            port: parsedUrl.port,\n",
    "            path: parsedUrl.path,\n",
    "            method: 'GET'\n",
    "        }, (res) => {\n",
    "            let data = '';\n",
    "            \n",
    "            res.on('data', (chunk) => {\n",
    "                data += chunk;\n",
    "            });\n",
    "            \n",
    "            res.on('end', () => {\n",
    "                resolve({\n",
    "                    statusCode: res.statusCode,\n",
    "                    headers: res.headers,\n",
    "                    data: data\n",
    "                });\n",
    "            });\n",
    "        });\n",
    "        \n",
    "        req.on('error', reject);\n",
    "        req.end();\n",
    "    });\n",
    "}\n",
    "\n",
    "// TCP Server Example\n",
    "console.log('\\n=== TCP Networking ===');\n",
    "\n",
    "const net = require('net');\n",
    "\n",
    "// Create TCP server\n",
    "const tcpServer = net.createServer((socket) => {\n",
    "    console.log('Client connected:', socket.remoteAddress);\n",
    "    \n",
    "    socket.write('Welcome to TCP server!\\n');\n",
    "    \n",
    "    socket.on('data', (data) => {\n",
    "        const message = data.toString().trim();\n",
    "        console.log('Received:', message);\n",
    "        \n",
    "        if (message === 'quit') {\n",
    "            socket.end('Goodbye!\\n');\n",
    "        } else {\n",
    "            socket.write(`Echo: ${message}\\n`);\n",
    "        }\n",
    "    });\n",
    "    \n",
    "    socket.on('end', () => {\n",
    "        console.log('Client disconnected');\n",
    "    });\n",
    "    \n",
    "    socket.on('error', (err) => {\n",
    "        console.error('Socket error:', err.message);\n",
    "    });\n",
    "});\n",
    "\n",
    "// Start TCP server\n",
    "tcpServer.listen(8080, () => {\n",
    "    console.log('TCP server listening on port 8080');\n",
    "    \n",
    "    // Stop server after 5 seconds\n",
    "    setTimeout(() => {\n",
    "        tcpServer.close(() => {\n",
    "            console.log('TCP server closed');\n",
    "        });\n",
    "    }, 5000);\n",
    "});\n",
    "\n",
    "// WebSocket-like functionality\n",
    "console.log('\\n=== Real-time Communication ===');\n",
    "\n",
    "class SimpleWebSocket extends require('events') {\n",
    "    constructor() {\n",
    "        super();\n",
    "        this.clients = new Set();\n",
    "    }\n",
    "    \n",
    "    addClient(client) {\n",
    "        this.clients.add(client);\n",
    "        this.emit('clientConnected', client);\n",
    "    }\n",
    "    \n",
    "    removeClient(client) {\n",
    "        this.clients.delete(client);\n",
    "        this.emit('clientDisconnected', client);\n",
    "    }\n",
    "    \n",
    "    broadcast(message) {\n",
    "        this.clients.forEach(client => {\n",
    "            client.send(message);\n",
    "        });\n",
    "    }\n",
    "}\n",
    "\n",
    "const wsServer = new SimpleWebSocket();\n",
    "\n",
    "wsServer.on('clientConnected', (client) => {\n",
    "    console.log('WebSocket client connected');\n",
    "});\n",
    "\n",
    "wsServer.on('clientDisconnected', (client) => {\n",
    "    console.log('WebSocket client disconnected');\n",
    "});\n",
    "\n",
    "// Simulate client\n",
    "const mockClient = {\n",
    "    id: 'client-1',\n",
    "    send: (message) => console.log('Client received:', message)\n",
    "};\n",
    "\n",
    "wsServer.addClient(mockClient);\n",
    "wsServer.broadcast('Hello all clients!');\n",
    "wsServer.removeClient(mockClient);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "ded1ffb4",
   "metadata": {},
   "source": [
    "# Testing and Debugging {#testing}\n",
    "\n",
    "Node.js provides excellent tools for testing and debugging applications. Popular testing frameworks include Jest, Mocha, and the built-in Node.js test runner."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "b35c08d6",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Node.js Testing and Debugging\n",
    "console.log('=== Node.js Testing ===');\n",
    "\n",
    "// Built-in assert module\n",
    "const assert = require('assert');\n",
    "\n",
    "// Simple test functions\n",
    "function add(a, b) {\n",
    "    return a + b;\n",
    "}\n",
    "\n",
    "function multiply(a, b) {\n",
    "    return a * b;\n",
    "}\n",
    "\n",
    "async function fetchUser(id) {\n",
    "    // Simulate async operation\n",
    "    return new Promise((resolve) => {\n",
    "        setTimeout(() => {\n",
    "            resolve({ id, name: `User ${id}`, email: `user${id}@example.com` });\n",
    "        }, 100);\n",
    "    });\n",
    "}\n",
    "\n",
    "// Basic assertions\n",
    "console.log('\\nRunning basic tests:');\n",
    "\n",
    "try {\n",
    "    assert.strictEqual(add(2, 3), 5, 'Addition should work');\n",
    "    console.log('✓ Addition test passed');\n",
    "    \n",
    "    assert.strictEqual(multiply(4, 5), 20, 'Multiplication should work');\n",
    "    console.log('✓ Multiplication test passed');\n",
    "    \n",
    "    assert.throws(() => {\n",
    "        throw new Error('Test error');\n",
    "    }, Error, 'Should throw an error');\n",
    "    console.log('✓ Error throwing test passed');\n",
    "    \n",
    "} catch (error) {\n",
    "    console.error('✗ Test failed:', error.message);\n",
    "}\n",
    "\n",
    "// Async testing\n",
    "async function testAsyncFunction() {\n",
    "    try {\n",
    "        const user = await fetchUser(1);\n",
    "        assert.strictEqual(user.id, 1, 'User ID should match');\n",
    "        assert.strictEqual(user.name, 'User 1', 'User name should match');\n",
    "        console.log('✓ Async test passed');\n",
    "    } catch (error) {\n",
    "        console.error('✗ Async test failed:', error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "testAsyncFunction();\n",
    "\n",
    "// Test runner simulation\n",
    "console.log('\\n=== Test Runner Simulation ===');\n",
    "\n",
    "class TestRunner {\n",
    "    constructor() {\n",
    "        this.tests = [];\n",
    "        this.passed = 0;\n",
    "        this.failed = 0;\n",
    "    }\n",
    "    \n",
    "    test(description, testFn) {\n",
    "        this.tests.push({ description, testFn });\n",
    "    }\n",
    "    \n",
    "    async run() {\n",
    "        console.log(`Running ${this.tests.length} tests...\\n`);\n",
    "        \n",
    "        for (const { description, testFn } of this.tests) {\n",
    "            try {\n",
    "                await testFn();\n",
    "                console.log(`✓ ${description}`);\n",
    "                this.passed++;\n",
    "            } catch (error) {\n",
    "                console.error(`✗ ${description}`);\n",
    "                console.error(`  Error: ${error.message}`);\n",
    "                this.failed++;\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        console.log(`\\nTest Results: ${this.passed} passed, ${this.failed} failed`);\n",
    "    }\n",
    "}\n",
    "\n",
    "// Use test runner\n",
    "const runner = new TestRunner();\n",
    "\n",
    "runner.test('should add numbers correctly', () => {\n",
    "    assert.strictEqual(add(2, 3), 5);\n",
    "});\n",
    "\n",
    "runner.test('should multiply numbers correctly', () => {\n",
    "    assert.strictEqual(multiply(3, 4), 12);\n",
    "});\n",
    "\n",
    "runner.test('should handle async operations', async () => {\n",
    "    const user = await fetchUser(2);\n",
    "    assert.strictEqual(user.id, 2);\n",
    "});\n",
    "\n",
    "runner.test('should fail this test', () => {\n",
    "    assert.strictEqual(1, 2, 'This should fail');\n",
    "});\n",
    "\n",
    "runner.run();\n",
    "\n",
    "// Debugging utilities\n",
    "console.log('\\n=== Debugging Utilities ===');\n",
    "\n",
    "// Console debugging\n",
    "const debugData = {\n",
    "    users: [\n",
    "        { id: 1, name: 'Alice', active: true },\n",
    "        { id: 2, name: 'Bob', active: false }\n",
    "    ],\n",
    "    config: {\n",
    "        apiUrl: 'https://api.example.com',\n",
    "        timeout: 5000\n",
    "    }\n",
    "};\n",
    "\n",
    "console.log('Debug data:');\n",
    "console.table(debugData.users);\n",
    "console.dir(debugData.config, { colors: false, depth: 2 });\n",
    "\n",
    "// Performance timing\n",
    "console.time('Performance Test');\n",
    "const largeArray = Array.from({ length: 100000 }, (_, i) => i);\n",
    "const sum = largeArray.reduce((acc, val) => acc + val, 0);\n",
    "console.timeEnd('Performance Test');\n",
    "console.log('Sum calculated:', sum > 0 ? 'Success' : 'Failed');\n",
    "\n",
    "// Memory usage tracking\n",
    "function trackMemory(label) {\n",
    "    const usage = process.memoryUsage();\n",
    "    console.log(`${label} Memory Usage:`);\n",
    "    console.log(`  RSS: ${Math.round(usage.rss / 1024 / 1024)} MB`);\n",
    "    console.log(`  Heap Used: ${Math.round(usage.heapUsed / 1024 / 1024)} MB`);\n",
    "    console.log(`  Heap Total: ${Math.round(usage.heapTotal / 1024 / 1024)} MB`);\n",
    "}\n",
    "\n",
    "trackMemory('Current');\n",
    "\n",
    "// Error handling and stack traces\n",
    "function createError() {\n",
    "    const error = new Error('Sample error for debugging');\n",
    "    error.code = 'SAMPLE_ERROR';\n",
    "    error.statusCode = 500;\n",
    "    return error;\n",
    "}\n",
    "\n",
    "const sampleError = createError();\n",
    "console.log('\\nError details:');\n",
    "console.log('Message:', sampleError.message);\n",
    "console.log('Code:', sampleError.code);\n",
    "console.log('Stack trace:');\n",
    "console.log(sampleError.stack);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "70e66b75",
   "metadata": {},
   "source": [
    "# Deployment and Production {#deployment}\n",
    "\n",
    "Deploying Node.js applications requires consideration of environment configuration, process management, monitoring, and scaling."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "109dc761",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Production Configuration\n",
    "console.log('=== Production Configuration ===');\n",
    "\n",
    "// Environment configuration\n",
    "class Config {\n",
    "    constructor() {\n",
    "        this.env = process.env.NODE_ENV || 'development';\n",
    "        this.port = parseInt(process.env.PORT) || 3000;\n",
    "        this.dbUrl = process.env.DATABASE_URL || 'mongodb://localhost:27017/myapp';\n",
    "        this.jwtSecret = process.env.JWT_SECRET || 'dev-secret';\n",
    "        this.logLevel = process.env.LOG_LEVEL || 'info';\n",
    "    }\n",
    "    \n",
    "    isProduction() {\n",
    "        return this.env === 'production';\n",
    "    }\n",
    "    \n",
    "    isDevelopment() {\n",
    "        return this.env === 'development';\n",
    "    }\n",
    "    \n",
    "    validate() {\n",
    "        const required = ['PORT', 'DATABASE_URL'];\n",
    "        const missing = required.filter(key => !process.env[key]);\n",
    "        \n",
    "        if (this.isProduction() && missing.length > 0) {\n",
    "            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);\n",
    "        }\n",
    "        \n",
    "        if (this.isProduction() && this.jwtSecret === 'dev-secret') {\n",
    "            throw new Error('JWT_SECRET must be set in production');\n",
    "        }\n",
    "    }\n",
    "}\n",
    "\n",
    "const config = new Config();\n",
    "console.log('Environment:', config.env);\n",
    "console.log('Port:', config.port);\n",
    "console.log('Is Production:', config.isProduction());\n",
    "\n",
    "try {\n",
    "    config.validate();\n",
    "    console.log('✓ Configuration valid');\n",
    "} catch (error) {\n",
    "    console.log('⚠ Configuration warning:', error.message);\n",
    "}\n",
    "\n",
    "// Logging for production\n",
    "console.log('\\n=== Production Logging ===');\n",
    "\n",
    "class Logger {\n",
    "    constructor(level = 'info') {\n",
    "        this.levels = { error: 0, warn: 1, info: 2, debug: 3 };\n",
    "        this.level = this.levels[level] || 2;\n",
    "    }\n",
    "    \n",
    "    log(level, message, meta = {}) {\n",
    "        if (this.levels[level] <= this.level) {\n",
    "            const timestamp = new Date().toISOString();\n",
    "            const logEntry = {\n",
    "                timestamp,\n",
    "                level: level.toUpperCase(),\n",
    "                message,\n",
    "                ...meta\n",
    "            };\n",
    "            \n",
    "            if (config.isProduction()) {\n",
    "                // In production, log as JSON for log aggregation\n",
    "                console.log(JSON.stringify(logEntry));\n",
    "            } else {\n",
    "                // In development, log human-readable format\n",
    "                console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);\n",
    "                if (Object.keys(meta).length > 0) {\n",
    "                    console.log('  Meta:', meta);\n",
    "                }\n",
    "            }\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    error(message, meta) { this.log('error', message, meta); }\n",
    "    warn(message, meta) { this.log('warn', message, meta); }\n",
    "    info(message, meta) { this.log('info', message, meta); }\n",
    "    debug(message, meta) { this.log('debug', message, meta); }\n",
    "}\n",
    "\n",
    "const logger = new Logger(config.logLevel);\n",
    "\n",
    "logger.info('Application starting', { port: config.port, env: config.env });\n",
    "logger.debug('Debug information', { userId: 123, action: 'login' });\n",
    "logger.warn('Warning message', { code: 'DEPRECATED_API' });\n",
    "logger.error('Error occurred', { error: 'Database connection failed', stack: 'Error stack...' });\n",
    "\n",
    "// Process management\n",
    "console.log('\\n=== Process Management ===');\n",
    "\n",
    "// Graceful shutdown\n",
    "class GracefulShutdown {\n",
    "    constructor() {\n",
    "        this.servers = [];\n",
    "        this.connections = [];\n",
    "        this.isShuttingDown = false;\n",
    "        \n",
    "        // Handle shutdown signals\n",
    "        process.on('SIGTERM', () => this.shutdown('SIGTERM'));\n",
    "        process.on('SIGINT', () => this.shutdown('SIGINT'));\n",
    "        \n",
    "        // Handle uncaught exceptions\n",
    "        process.on('uncaughtException', (error) => {\n",
    "            logger.error('Uncaught exception', { error: error.message, stack: error.stack });\n",
    "            this.shutdown('uncaughtException');\n",
    "        });\n",
    "        \n",
    "        // Handle unhandled promise rejections\n",
    "        process.on('unhandledRejection', (reason, promise) => {\n",
    "            logger.error('Unhandled promise rejection', { reason, promise });\n",
    "            this.shutdown('unhandledRejection');\n",
    "        });\n",
    "    }\n",
    "    \n",
    "    addServer(server) {\n",
    "        this.servers.push(server);\n",
    "    }\n",
    "    \n",
    "    addConnection(connection) {\n",
    "        this.connections.push(connection);\n",
    "    }\n",
    "    \n",
    "    async shutdown(signal) {\n",
    "        if (this.isShuttingDown) return;\n",
    "        \n",
    "        this.isShuttingDown = true;\n",
    "        logger.info(`Received ${signal}, starting graceful shutdown`);\n",
    "        \n",
    "        try {\n",
    "            // Close servers\n",
    "            await Promise.all(this.servers.map(server => {\n",
    "                return new Promise((resolve) => {\n",
    "                    server.close(resolve);\n",
    "                });\n",
    "            }));\n",
    "            \n",
    "            // Close database connections\n",
    "            await Promise.all(this.connections.map(conn => conn.close()));\n",
    "            \n",
    "            logger.info('Graceful shutdown completed');\n",
    "            process.exit(0);\n",
    "            \n",
    "        } catch (error) {\n",
    "            logger.error('Error during shutdown', { error: error.message });\n",
    "            process.exit(1);\n",
    "        }\n",
    "    }\n",
    "}\n",
    "\n",
    "const gracefulShutdown = new GracefulShutdown();\n",
    "\n",
    "// Health check endpoint\n",
    "console.log('\\n=== Health Monitoring ===');\n",
    "\n",
    "class HealthCheck {\n",
    "    constructor() {\n",
    "        this.checks = new Map();\n",
    "        this.startTime = Date.now();\n",
    "    }\n",
    "    \n",
    "    addCheck(name, checkFn) {\n",
    "        this.checks.set(name, checkFn);\n",
    "    }\n",
    "    \n",
    "    async getStatus() {\n",
    "        const results = {};\n",
    "        let healthy = true;\n",
    "        \n",
    "        for (const [name, checkFn] of this.checks) {\n",
    "            try {\n",
    "                const result = await checkFn();\n",
    "                results[name] = { status: 'healthy', ...result };\n",
    "            } catch (error) {\n",
    "                results[name] = { status: 'unhealthy', error: error.message };\n",
    "                healthy = false;\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        return {\n",
    "            status: healthy ? 'healthy' : 'unhealthy',\n",
    "            uptime: Date.now() - this.startTime,\n",
    "            timestamp: new Date().toISOString(),\n",
    "            checks: results\n",
    "        };\n",
    "    }\n",
    "}\n",
    "\n",
    "const healthCheck = new HealthCheck();\n",
    "\n",
    "// Add health checks\n",
    "healthCheck.addCheck('memory', () => {\n",
    "    const usage = process.memoryUsage();\n",
    "    const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024);\n",
    "    \n",
    "    if (heapUsedMB > 500) { // 500MB threshold\n",
    "        throw new Error(`High memory usage: ${heapUsedMB}MB`);\n",
    "    }\n",
    "    \n",
    "    return { heapUsedMB };\n",
    "});\n",
    "\n",
    "healthCheck.addCheck('database', async () => {\n",
    "    // Simulate database check\n",
    "    await new Promise(resolve => setTimeout(resolve, 10));\n",
    "    return { connectionTime: '10ms' };\n",
    "});\n",
    "\n",
    "// Test health check\n",
    "healthCheck.getStatus().then(status => {\n",
    "    console.log('Health check result:', JSON.stringify(status, null, 2));\n",
    "});\n",
    "\n",
    "console.log('\\nProduction deployment concepts demonstrated!');\n",
    "console.log('Key production considerations:');\n",
    "console.log('- Environment configuration and validation');\n",
    "console.log('- Structured logging for monitoring');\n",
    "console.log('- Graceful shutdown handling');\n",
    "console.log('- Health checks and monitoring');\n",
    "console.log('- Process management (PM2, Docker, Kubernetes)');\n",
    "console.log('- Load balancing and clustering');\n",
    "console.log('- Security headers and HTTPS');\n",
    "console.log('- Performance monitoring and profiling');"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "JavaScript (Node.js)",\n",
   "language": "javascript",\n",
   "name": "javascript"\n",
  },\n",
  "language_info": {\n",
   "file_extension": ".js",\n",
   "mimetype": "application/javascript",\n",
   "name": "javascript",\n",
   "version": "18.0.0"\n,
  }\n,
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
 "metadata": {
  "kernelspec": {
   "display_name": "JavaScript (Node.js)",
   "language": "javascript",
   "name": "javascript"
  },
  "language_info": {
   "file_extension": ".js",
   "mimetype": "application/javascript",
   "name": "javascript",
   "version": "18.0.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
