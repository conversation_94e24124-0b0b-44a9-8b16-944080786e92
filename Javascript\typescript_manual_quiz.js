/**
 * TypeScript Manual Quiz
 * Comprehensive quiz covering all sections of the TypeScript programming manual
 */

const quizzes = {
  "TypeScript Fundamentals": [
    { 
      question: "What is TypeScript?", 
      answer: "A superset of JavaScript that adds static type checking and compiles to plain JavaScript." 
    },
    { 
      question: "What are the main benefits of using TypeScript?", 
      answer: "Static type checking, better IDE support, early error detection, improved code documentation, easier refactoring." 
    },
    { 
      question: "How do you compile TypeScript to JavaScript?", 
      answer: "Use the TypeScript compiler (tsc): tsc filename.ts or tsc for entire project with tsconfig.json." 
    },
    { 
      question: "What file extensions does TypeScript use?", 
      answer: ".ts for TypeScript files, .tsx for TypeScript with JSX, .d.ts for type declaration files." 
    },
    { 
      question: "Can TypeScript run directly in browsers?", 
      answer: "No, TypeScript must be compiled to JavaScript first. Browsers only understand JavaScript." 
    },
    { 
      question: "What is the relationship between TypeScript and JavaScript?", 
      answer: "TypeScript is a superset of JavaScript - all valid JavaScript is valid TypeScript." 
    }
  ],

  "Basic Types and Annotations": [
    { 
      question: "What are TypeScript's primitive types?", 
      answer: "string, number, boolean, null, undefined, symbol, bigint." 
    },
    { 
      question: "How do you declare an array type?", 
      answer: "number[] or Array<number> for array of numbers. Both syntaxes are equivalent." 
    },
    { 
      question: "What is a tuple in TypeScript?", 
      answer: "Fixed-length array with specific types for each position: [string, number] = ['Alice', 30]." 
    },
    { 
      question: "What's the difference between 'any' and 'unknown'?", 
      answer: "any disables type checking; unknown is type-safe and requires type checking before use." 
    },
    { 
      question: "What is the 'never' type used for?", 
      answer: "Functions that never return (throw errors or infinite loops) or unreachable code branches." 
    },
    { 
      question: "How do you define optional properties?", 
      answer: "Use the ? operator: { name: string; age?: number } - age is optional." 
    },
    { 
      question: "What are union types?", 
      answer: "Types that can be one of several types: string | number means value can be string OR number." 
    }
  ],

  "Interfaces and Type Aliases": [
    { 
      question: "What's the difference between interface and type?", 
      answer: "Interfaces are extensible and for object shapes; types are for unions, primitives, and computed types." 
    },
    { 
      question: "How do you extend an interface?", 
      answer: "Use 'extends' keyword: interface AdminUser extends User { permissions: string[]; }" 
    },
    { 
      question: "What is interface merging?", 
      answer: "Multiple interface declarations with same name automatically merge their properties." 
    },
    { 
      question: "How do you define optional methods in interfaces?", 
      answer: "Use ? operator: interface API { getData(): string; updateData?(data: string): void; }" 
    },
    { 
      question: "What are index signatures?", 
      answer: "Allow objects to have properties with dynamic keys: { [key: string]: any }" 
    },
    { 
      question: "How do you create readonly properties?", 
      answer: "Use 'readonly' modifier: interface User { readonly id: number; name: string; }" 
    }
  ],

  "Generics": [
    { 
      question: "What are generics in TypeScript?", 
      answer: "Type variables that allow creating reusable components that work with multiple types while maintaining type safety." 
    },
    { 
      question: "How do you define a generic function?", 
      answer: "function identity<T>(arg: T): T { return arg; } - T is the type parameter." 
    },
    { 
      question: "What are generic constraints?", 
      answer: "Restrictions on generic types using 'extends': <T extends Lengthwise> ensures T has length property." 
    },
    { 
      question: "How do you use multiple type parameters?", 
      answer: "function pair<T, U>(first: T, second: U): [T, U] - separate with commas." 
    },
    { 
      question: "What is a generic interface?", 
      answer: "Interface with type parameters: interface Container<T> { value: T; getValue(): T; }" 
    },
    { 
      question: "How do you provide default generic types?", 
      answer: "Use = syntax: interface Response<T = any> { data: T; } - T defaults to any if not specified." 
    }
  ],

  "Advanced Types": [
    { 
      question: "What are conditional types?", 
      answer: "Types that depend on conditions: T extends string ? true : false - returns true if T is string." 
    },
    { 
      question: "What are mapped types?", 
      answer: "Transform existing types by mapping over properties: { [P in keyof T]: T[P] }" 
    },
    { 
      question: "What is keyof operator?", 
      answer: "Returns union of all property names of a type: keyof User gives 'id' | 'name' | 'email'." 
    },
    { 
      question: "What are template literal types?", 
      answer: "String literal types using template syntax: `on${Capitalize<T>}` creates event handler names." 
    },
    { 
      question: "What is type assertion?", 
      answer: "Telling TypeScript a value's type: value as Type or <Type>value (use sparingly)." 
    },
    { 
      question: "What are discriminated unions?", 
      answer: "Union types with common property for type narrowing: { type: 'success'; data: T } | { type: 'error'; error: string }" 
    }
  ],

  "Utility Types": [
    { 
      question: "What does Partial<T> do?", 
      answer: "Makes all properties of T optional: Partial<User> = { id?: number; name?: string; }" 
    },
    { 
      question: "What does Required<T> do?", 
      answer: "Makes all properties of T required, removing optional modifiers." 
    },
    { 
      question: "What does Pick<T, K> do?", 
      answer: "Selects specific properties K from type T: Pick<User, 'id' | 'name'>" 
    },
    { 
      question: "What does Omit<T, K> do?", 
      answer: "Excludes specific properties K from type T: Omit<User, 'id'> removes id property." 
    },
    { 
      question: "What does Record<K, T> do?", 
      answer: "Creates object type with keys K and values T: Record<'a' | 'b', string> = { a: string; b: string; }" 
    },
    { 
      question: "What does ReturnType<T> do?", 
      answer: "Extracts return type of function T: ReturnType<() => string> = string." 
    },
    { 
      question: "What does NonNullable<T> do?", 
      answer: "Excludes null and undefined from T: NonNullable<string | null> = string." 
    }
  ],

  "Classes and Inheritance": [
    { 
      question: "How do you define a class in TypeScript?", 
      answer: "class User { constructor(public name: string) {} } - similar to JavaScript with type annotations." 
    },
    { 
      question: "What are access modifiers in TypeScript?", 
      answer: "public (default), private (class only), protected (class and subclasses), readonly." 
    },
    { 
      question: "What are private fields (#)?", 
      answer: "ES2022 private fields using # syntax: #privateField. Only accessible within the class." 
    },
    { 
      question: "How do you implement interfaces in classes?", 
      answer: "Use 'implements' keyword: class User implements Serializable { serialize(): string { ... } }" 
    },
    { 
      question: "What are abstract classes?", 
      answer: "Classes that cannot be instantiated directly, used as base classes: abstract class Animal { abstract makeSound(): void; }" 
    },
    { 
      question: "How do you define static methods?", 
      answer: "Use 'static' keyword: static create(): User { return new User(); } - called on class, not instance." 
    }
  ],

  "Configuration and Tooling": [
    { 
      question: "What is tsconfig.json?", 
      answer: "TypeScript configuration file specifying compiler options, file inclusion/exclusion, and project settings." 
    },
    { 
      question: "What does 'strict' mode enable?", 
      answer: "Enables all strict type checking options: noImplicitAny, strictNullChecks, strictFunctionTypes, etc." 
    },
    { 
      question: "What is the 'target' option?", 
      answer: "Specifies ECMAScript target version for compiled JavaScript: ES5, ES2015, ES2020, etc." 
    },
    { 
      question: "What does 'noImplicitAny' do?", 
      answer: "Raises error when TypeScript can't infer type and falls back to 'any'." 
    },
    { 
      question: "What is 'strictNullChecks'?", 
      answer: "Makes null and undefined not assignable to other types unless explicitly allowed." 
    },
    { 
      question: "What are declaration files (.d.ts)?", 
      answer: "Files containing only type information for JavaScript libraries, enabling TypeScript support." 
    },
    { 
      question: "How do you install types for JavaScript libraries?", 
      answer: "Install from @types namespace: npm install @types/lodash for lodash type definitions." 
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n🔷 ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available TypeScript Quiz Topics:');
  console.log('='.repeat(38));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM TYPESCRIPT QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) || 
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });
  
  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }
  
  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));
  
  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });
  
  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    quizzes, 
    printQuiz, 
    listTopics, 
    randomQuiz, 
    searchQuestions 
  };
}

// Example usage and help
console.log('🔷 TypeScript Manual Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('🔍 searchQuestions("keyword") - Search for questions');

console.log('\n💡 Example: printQuiz("TypeScript Fundamentals")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("interface", true) - Find interface-related questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
