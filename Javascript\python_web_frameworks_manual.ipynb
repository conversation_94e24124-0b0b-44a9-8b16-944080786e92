{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive Python Web Frameworks Manual\n", "\n", "This manual covers essential Python web development using Django and Flask, from basic concepts to production deployment. Both frameworks serve different needs: Django for full-featured applications and Flask for lightweight, flexible projects.\n", "\n", "## Table of Contents\n", "1. [Web Framework Fundamentals](#fundamentals)\n", "2. [Flask Essentials](#flask)\n", "3. [Django Fundamentals](#django)\n", "4. [Routing and Views](#routing)\n", "5. [Templates and Forms](#templates)\n", "6. [Database Integration](#database)\n", "7. [Authentication and Authorization](#auth)\n", "8. [API Development](#api)\n", "9. [Testing Web Applications](#testing)\n", "10. [Deployment and Production](#deployment)\n", "11. [<PERSON> and Scaling](#performance)\n", "12. [Best Practices](#best-practices)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# Web Framework Fundamentals {#fundamentals}\n", "\n", "Understanding the core concepts of web development and how Python frameworks handle HTTP requests, responses, and application structure."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["# Web Framework Fundamentals\n", "print('=== Python Web Framework Fundamentals ===')\n", "\n", "# HTTP Request/Response Cycle\n", "print('\\nHTTP Request/Response Cycle:')\n", "print('1. Client sends HTTP request to server')\n", "print('2. Web server receives request and forwards to Python application')\n", "print('3. Framework routes request to appropriate view function')\n", "print('4. View processes request, interacts with database if needed')\n", "print('5. View returns HTTP response (HTML, JSON, etc.)')\n", "print('6. Server sends response back to client')\n", "\n", "# Framework Comparison\n", "frameworks = {\n", "    'Flask': {\n", "        'type': 'Microframework',\n", "        'philosophy': 'Minimalist, flexible, explicit',\n", "        'best_for': 'Small to medium apps, APIs, prototypes',\n", "        'learning_curve': 'Gentle',\n", "        'components': 'Choose your own (database, forms, etc.)'\n", "    },\n", "    'Django': {\n", "        'type': 'Full-stack framework',\n", "        'philosophy': 'Batteries included, convention over configuration',\n", "        'best_for': 'Large applications, content management, rapid development',\n", "        'learning_curve': 'Steeper but comprehensive',\n", "        'components': 'Built-in ORM, admin, forms, authentication'\n", "    }\n", "}\n", "\n", "print('\\n=== Framework Comparison ===')\n", "for framework, details in frameworks.items():\n", "    print(f'\\n{framework}:')\n", "    for key, value in details.items():\n", "        print(f'  {key.replace(\"_\", \" \").title()}: {value}')\n", "\n", "# WSGI (Web Server Gateway Interface)\n", "print('\\n=== WSGI Concept ===')\n", "print('WSGI is the standard interface between Python web applications and web servers')\n", "\n", "# Simple WSGI application example\n", "def simple_wsgi_app(environ, start_response):\n", "    \"\"\"\n", "    A minimal WSGI application\n", "    environ: Dictionary containing request information\n", "    start_response: Callable to begin the HTTP response\n", "    \"\"\"\n", "    status = '200 OK'\n", "    headers = [('Content-Type', 'text/html; charset=utf-8')]\n", "    start_response(status, headers)\n", "    \n", "    # Extract request information\n", "    method = environ.get('REQUEST_METHOD', 'GET')\n", "    path = environ.get('PATH_INFO', '/')\n", "    \n", "    response_body = f\"\"\"\n", "    <html>\n", "        <body>\n", "            <h1>Simple WSGI App</h1>\n", "            <p>Method: {method}</p>\n", "            <p>Path: {path}</p>\n", "        </body>\n", "    </html>\n", "    \"\"\"\n", "    \n", "    return [response_body.encode('utf-8')]\n", "\n", "print('WSGI application function created (see code above)')\n", "\n", "# Common web development patterns\n", "print('\\n=== Common Web Development Patterns ===')\n", "patterns = {\n", "    'MVC (Model-View-Controller)': 'Separates data, presentation, and logic',\n", "    'MTV (Model-Template-View)': 'Django\\'s variation of MVC',\n", "    'RESTful APIs': 'Stateless, resource-based API design',\n", "    'Middleware': 'Components that process requests/responses',\n", "    'Blueprints/Apps': 'Modular application organization',\n", "    'Dependency Injection': 'Providing dependencies to components'\n", "}\n", "\n", "for pattern, description in patterns.items():\n", "    print(f'{pattern}: {description}')\n", "\n", "# HTTP Methods and their purposes\n", "print('\\n=== HTTP Methods ===')\n", "http_methods = {\n", "    'GET': 'Retrieve data (idempotent, cacheable)',\n", "    'POST': 'Create new resources or submit data',\n", "    'PUT': 'Update/replace entire resource',\n", "    'PATCH': 'Partial update of resource',\n", "    'DELETE': 'Remove resource',\n", "    'HEAD': 'Get headers only (like GET but no body)',\n", "    'OPTIONS': 'Get allowed methods for resource'\n", "}\n", "\n", "for method, purpose in http_methods.items():\n", "    print(f'{method}: {purpose}')\n", "\n", "# Status codes\n", "print('\\n=== Common HTTP Status Codes ===')\n", "status_codes = {\n", "    200: 'OK - Request successful',\n", "    201: 'Created - Resource created successfully',\n", "    400: 'Bad Request - Invalid request syntax',\n", "    401: 'Unauthorized - Authentication required',\n", "    403: 'Forbidden - Access denied',\n", "    404: 'Not Found - Resource not found',\n", "    500: 'Internal Server Error - Server error'\n", "}\n", "\n", "for code, description in status_codes.items():\n", "    print(f'{code}: {description}')\n", "\n", "print('\\nWeb frameworks abstract away WSGI complexity and provide high-level APIs!')"]}, {"cell_type": "markdown", "id": "71c71fe2", "metadata": {}, "source": ["# Flask Essentials {#flask}\n", "\n", "Flask is a lightweight, flexible microframework that gives you the tools to build web applications without imposing a particular project structure."]}, {"cell_type": "code", "execution_count": null, "id": "ff42471f", "metadata": {}, "outputs": [], "source": ["# Flask Essentials\n", "print('=== Flask Essentials ===')\n", "\n", "# Note: This demonstrates Flask concepts without actually running Flask\n", "# In a real environment, you would: pip install flask\n", "\n", "print('Flask Installation: pip install flask')\n", "print('\\nBasic Flask Application Structure:')\n", "\n", "# Basic Flask app structure\n", "basic_flask_app = '''\n", "from flask import Flask, request, jsonify, render_template\n", "\n", "# Create Flask application instance\n", "app = Flask(__name__)\n", "app.config['SECRET_KEY'] = 'your-secret-key-here'\n", "\n", "# Basic route\n", "@app.route('/')\n", "def home():\n", "    return '<h1>Welcome to Flask!</h1>'\n", "\n", "# Route with parameter\n", "@app.route('/user/<string:username>')\n", "def user_profile(username):\n", "    return f'<h1>Hello, {username}!</h1>'\n", "\n", "# Route with multiple HTTP methods\n", "@app.route('/api/users', methods=['GET', 'POST'])\n", "def users_api():\n", "    if request.method == 'GET':\n", "        return jsonify({'users': ['alice', 'bob', 'charlie']})\n", "    elif request.method == 'POST':\n", "        user_data = request.get_json()\n", "        return jsonify({'message': 'User created', 'user': user_data}), 201\n", "\n", "# Route with query parameters\n", "@app.route('/search')\n", "def search():\n", "    query = request.args.get('q', '')\n", "    page = request.args.get('page', 1, type=int)\n", "    return f'Searching for: {query}, Page: {page}'\n", "\n", "# Error handler\n", "@app.errorhandler(404)\n", "def not_found(error):\n", "    return jsonify({'error': 'Resource not found'}), 404\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, host='0.0.0.0', port=5000)\n", "'''\n", "\n", "print(basic_flask_app)\n", "\n", "# Flask application factory pattern\n", "print('\\n=== Flask Application Factory Pattern ===')\n", "factory_pattern = '''\n", "# app/__init__.py\n", "from flask import Flask\n", "from flask_sqlalchemy import SQLAlchemy\n", "from flask_login import LoginManager\n", "\n", "db = SQLAlchemy()\n", "login_manager = LoginManager()\n", "\n", "def create_app(config_name='development'):\n", "    app = Flask(__name__)\n", "    \n", "    # Load configuration\n", "    app.config.from_object(f'config.{config_name.title()}Config')\n", "    \n", "    # Initialize extensions\n", "    db.init_app(app)\n", "    login_manager.init_app(app)\n", "    login_manager.login_view = 'auth.login'\n", "    \n", "    # Register blueprints\n", "    from app.main import bp as main_bp\n", "    app.register_blueprint(main_bp)\n", "    \n", "    from app.auth import bp as auth_bp\n", "    app.register_blueprint(auth_bp, url_prefix='/auth')\n", "    \n", "    from app.api import bp as api_bp\n", "    app.register_blueprint(api_bp, url_prefix='/api')\n", "    \n", "    return app\n", "\n", "# run.py\n", "from app import create_app\n", "\n", "app = create_app()\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True)\n", "'''\n", "\n", "print(factory_pattern)\n", "\n", "# Flask Blueprints\n", "print('\\n=== Flask Blueprints ===')\n", "blueprint_example = '''\n", "# app/main/routes.py\n", "from flask import Blueprint, render_template, request, flash, redirect, url_for\n", "from app.models import User, Post\n", "from app import db\n", "\n", "bp = Blueprint('main', __name__)\n", "\n", "@bp.route('/')\n", "@bp.route('/index')\n", "def index():\n", "    posts = Post.query.order_by(Post.timestamp.desc()).limit(10).all()\n", "    return render_template('index.html', posts=posts)\n", "\n", "@bp.route('/user/<username>')\n", "def user(username):\n", "    user = User.query.filter_by(username=username).first_or_404()\n", "    posts = user.posts.order_by(Post.timestamp.desc()).all()\n", "    return render_template('user.html', user=user, posts=posts)\n", "\n", "# app/main/__init__.py\n", "from flask import Blueprint\n", "\n", "bp = Blueprint('main', __name__)\n", "\n", "from app.main import routes\n", "'''\n", "\n", "print(blueprint_example)\n", "\n", "# Flask configuration\n", "print('\\n=== Flask Configuration ===')\n", "config_example = '''\n", "# config.py\n", "import os\n", "from datetime import timedelta\n", "\n", "class Config:\n", "    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'\n", "    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'\n", "    SQLALCHEMY_TRACK_MODIFICATIONS = False\n", "    MAIL_SERVER = os.environ.get('MAIL_SERVER')\n", "    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)\n", "    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']\n", "    PERMANENT_SESSION_LIFETIME = timedelta(days=7)\n", "\n", "class DevelopmentConfig(Config):\n", "    DEBUG = True\n", "    SQLALCHEMY_DATABASE_URI = 'sqlite:///dev.db'\n", "\n", "class ProductionConfig(Config):\n", "    DEBUG = False\n", "    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')\n", "\n", "class TestingConfig(Config):\n", "    TESTING = True\n", "    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'\n", "    WTF_CSRF_ENABLED = False\n", "'''\n", "\n", "print(config_example)\n", "\n", "print('\\nFlask provides a simple, flexible foundation for web applications!')"]}, {"cell_type": "markdown", "id": "b3ad4049", "metadata": {}, "source": ["# Django Fundamentals {#django}\n", "\n", "Django is a high-level Python web framework that encourages rapid development and clean, pragmatic design with a 'batteries included' philosophy."]}, {"cell_type": "code", "execution_count": null, "id": "4adc818d", "metadata": {}, "outputs": [], "source": ["# Django Fundamentals\n", "print('=== Django Fundamentals ===')\n", "\n", "# Django installation and project setup\n", "print('Django Installation and Setup:')\n", "print('pip install django')\n", "print('django-admin startproject myproject')\n", "print('cd myproject')\n", "print('python manage.py startapp myapp')\n", "\n", "# Django project structure\n", "print('\\n=== Django Project Structure ===')\n", "project_structure = '''\n", "myproject/\n", "    manage.py              # Command-line utility\n", "    myproject/\n", "        __init__.py\n", "        settings.py        # Project settings\n", "        urls.py           # URL routing\n", "        wsgi.py           # WSGI configuration\n", "        asgi.py           # ASGI configuration (async)\n", "    myapp/\n", "        __init__.py\n", "        admin.py          # Admin interface\n", "        apps.py           # App configuration\n", "        models.py         # Database models\n", "        views.py          # View functions/classes\n", "        urls.py           # App-specific URLs\n", "        tests.py          # Test cases\n", "        migrations/       # Database migrations\n", "        templates/        # HTML templates\n", "        static/           # Static files (CSS, JS, images)\n", "'''\n", "print(project_structure)\n", "\n", "# Django Models (ORM)\n", "print('\\n=== Django Models ===')\n", "models_example = '''\n", "# models.py\n", "from django.db import models\n", "from django.contrib.auth.models import User\n", "from django.urls import reverse\n", "\n", "class Category(models.Model):\n", "    name = models.CharField(max_length=100, unique=True)\n", "    slug = models.SlugField(max_length=100, unique=True)\n", "    description = models.TextField(blank=True)\n", "    created_at = models.DateTimeField(auto_now_add=True)\n", "    \n", "    class Meta:\n", "        verbose_name_plural = 'categories'\n", "        ordering = ['name']\n", "    \n", "    def __str__(self):\n", "        return self.name\n", "    \n", "    def get_absolute_url(self):\n", "        return reverse('category_detail', kwargs={'slug': self.slug})\n", "\n", "class Post(models.Model):\n", "    STATUS_CHOICES = [\n", "        ('draft', 'Draft'),\n", "        ('published', 'Published'),\n", "        ('archived', 'Archived'),\n", "    ]\n", "    \n", "    title = models.CharField(max_length=200)\n", "    slug = models.SlugField(max_length=200, unique=True)\n", "    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='posts')\n", "    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)\n", "    content = models.TextField()\n", "    excerpt = models.TextField(max_length=300, blank=True)\n", "    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')\n", "    featured_image = models.ImageField(upload_to='posts/', blank=True, null=True)\n", "    tags = models.ManyToManyField('Tag', blank=True)\n", "    created_at = models.DateTimeField(auto_now_add=True)\n", "    updated_at = models.DateTimeField(auto_now=True)\n", "    published_at = models.DateTimeField(null=True, blank=True)\n", "    \n", "    class Meta:\n", "        ordering = ['-created_at']\n", "        indexes = [\n", "            models.Index(fields=['status', 'published_at']),\n", "            models.Index(fields=['author', 'created_at']),\n", "        ]\n", "    \n", "    def __str__(self):\n", "        return self.title\n", "    \n", "    def get_absolute_url(self):\n", "        return reverse('post_detail', kwargs={'slug': self.slug})\n", "    \n", "    @property\n", "    def is_published(self):\n", "        return self.status == 'published'\n", "\n", "class Tag(models.Model):\n", "    name = models.CharField(max_length=50, unique=True)\n", "    slug = models.SlugField(max_length=50, unique=True)\n", "    \n", "    def __str__(self):\n", "        return self.name\n", "'''\n", "print(models_example)\n", "\n", "# Django Views\n", "print('\\n=== Django Views ===')\n", "views_example = '''\n", "# views.py\n", "from django.shortcuts import render, get_object_or_404, redirect\n", "from django.http import JsonResponse, Http404\n", "from django.views.generic import ListView, DetailView, CreateView, UpdateView\n", "from django.contrib.auth.decorators import login_required\n", "from django.contrib.auth.mixins import LoginRequiredMixin\n", "from django.contrib import messages\n", "from django.db.models import Q\n", "from .models import Post, Category\n", "from .forms import PostForm\n", "\n", "# Function-based view\n", "def post_list(request):\n", "    posts = Post.objects.filter(status='published').select_related('author', 'category')\n", "    \n", "    # Search functionality\n", "    search_query = request.GET.get('search')\n", "    if search_query:\n", "        posts = posts.filter(\n", "            Q(title__icontains=search_query) | \n", "            Q(content__icontains=search_query)\n", "        )\n", "    \n", "    # Category filtering\n", "    category_slug = request.GET.get('category')\n", "    if category_slug:\n", "        posts = posts.filter(category__slug=category_slug)\n", "    \n", "    context = {\n", "        'posts': posts,\n", "        'search_query': search_query,\n", "        'categories': Category.objects.all()\n", "    }\n", "    return render(request, 'blog/post_list.html', context)\n", "\n", "# Class-based views\n", "class PostListView(ListView):\n", "    model = Post\n", "    template_name = 'blog/post_list.html'\n", "    context_object_name = 'posts'\n", "    paginate_by = 10\n", "    \n", "    def get_queryset(self):\n", "        return Post.objects.filter(status='published').select_related('author', 'category')\n", "    \n", "    def get_context_data(self, **kwargs):\n", "        context = super().get_context_data(**kwargs)\n", "        context['categories'] = Category.objects.all()\n", "        return context\n", "\n", "class PostDetailView(DetailView):\n", "    model = Post\n", "    template_name = 'blog/post_detail.html'\n", "    context_object_name = 'post'\n", "    \n", "    def get_queryset(self):\n", "        return Post.objects.filter(status='published')\n", "\n", "class PostCreateView(LoginRequiredM<PERSON>in, CreateView):\n", "    model = Post\n", "    form_class = PostForm\n", "    template_name = 'blog/post_form.html'\n", "    \n", "    def form_valid(self, form):\n", "        form.instance.author = self.request.user\n", "        messages.success(self.request, 'Post created successfully!')\n", "        return super().form_valid(form)\n", "\n", "# API view\n", "def api_posts(request):\n", "    if request.method == 'GET':\n", "        posts = Post.objects.filter(status='published').values(\n", "            'id', 'title', 'slug', 'excerpt', 'created_at'\n", "        )\n", "        return JsonResponse({'posts': list(posts)})\n", "    \n", "    elif request.method == 'POST':\n", "        # Handle POST request for creating posts\n", "        pass\n", "'''\n", "print(views_example)\n", "\n", "print('\\nDjango provides a comprehensive framework with built-in admin, ORM, and more!')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"file_extension": ".py", "mimetype": "text/x-python", "name": "python", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 5}