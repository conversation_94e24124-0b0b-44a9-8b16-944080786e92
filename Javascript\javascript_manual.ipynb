{"cells": [{"cell_type": "markdown", "id": "c47163a8", "metadata": {}, "source": ["# Comprehensive JavaScript Programming Manual\n", "\n", "This manual serves as a complete reference for JavaScript programming, from basics to advanced concepts. Each section includes concise explanations and practical code examples that you can run directly in this notebook or in a browser console.\n", "\n", "## Table of Contents\n", "1. [Basic Syntax and Data Types](#basic-syntax)\n", "2. [Control Flow and Loops](#control-flow)\n", "3. [Functions and Arrow Functions](#functions)\n", "4. [Objects and Prototypes](#objects)\n", "5. [A<PERSON><PERSON> and Array Methods](#arrays)\n", "6. [DOM Manipulation and Events](#dom)\n", "7. [Asynchronous JavaScript (Promises, Async/Await)](#async)\n", "8. [Modules and Imports/Exports](#modules)\n", "9. [Error <PERSON>ling and Debugging](#errors)\n", "10. [ES6+ Features (Destructuring, Spread, Rest)](#es6)\n", "11. [Closures and Scope](#closures)\n", "12. [Classes and Inheritance](#classes)\n", "13. [Event Loop and Concurrency](#event-loop)\n", "14. [Web APIs and Fetch](#web-apis)\n", "15. [Local Storage and Cookies](#storage)\n", "16. [Testing (<PERSON><PERSON>, <PERSON><PERSON>)](#testing)\n", "17. [Build Tools and Bundlers (Webpack, Rollup)](#build-tools)\n", "18. [Frameworks Overview (React, Vue, Angular basics)](#frameworks)\n", "19. [TypeScript Basics](#typescript)\n", "20. [Best Practices and Code Style](#best-practices)\n", "21. [CLI and Node.js Basics](#nodejs)"]}, {"cell_type": "markdown", "id": "73fefecc", "metadata": {}, "source": ["# Basic Syntax and Data Types {#basic-syntax}\n", "\n", "JavaScript is a dynamically typed language with flexible syntax. Let's explore variables, operators, and fundamental data types."]}, {"cell_type": "code", "execution_count": null, "id": "e84d17fb", "metadata": {}, "outputs": [], "source": ["// Variables and basic data types\n", "let x = 42;                    // Number\n", "const pi = 3.14159;           // Number (constant)\n", "var name = \"JavaScript\";       // String (avoid var in modern JS)\n", "let isAwesome = true;         // <PERSON><PERSON>an\n", "let nothing = null;           // Null\n", "let notDefined;               // Undefined\n", "let bigNumber = 123n;         // BigInt\n", "let symbol = Symbol('id');    // Symbol\n", "\n", "// Basic operators\n", "let sum = 10 + 5;             // Addition\n", "let diff = 10 - 5;            // Subtraction\n", "let product = 10 * 5;         // Multiplication\n", "let quotient = 10 / 5;        // Division\n", "let remainder = 10 % 3;       // <PERSON><PERSON><PERSON>\n", "let power = 2 ** 3;           // Exponentiation\n", "\n", "// String operations\n", "let greeting = \"Hello\";\n", "let target = \"World\";\n", "let message = `${greeting}, ${target}!`;  // Template literals\n", "console.log(message);\n", "\n", "// Type conversion\n", "let strNum = \"123\";\n", "let num = Number(strNum);     // String to number\n", "let floatNum = parseFloat(strNum); // String to float\n", "let strBack = String(num);    // Number to string\n", "\n", "// Display types and values\n", "console.log(`Type of x: ${typeof x}, Value: ${x}`);\n", "console.log(`Type of pi: ${typeof pi}, Value: ${pi}`);\n", "console.log(`Type of name: ${typeof name}, Value: ${name}`);\n", "console.log(`Type of isAwesome: ${typeof isAwesome}, Value: ${isAwesome}`);\n", "console.log(`Type of nothing: ${typeof nothing}, Value: ${nothing}`);\n", "console.log(`Type of notDefined: ${typeof notDefined}, Value: ${notDefined}`);\n", "\n", "// Strict equality vs loose equality\n", "console.log(`\\nStrict equality (===): 5 === '5' is ${5 === '5'}`);\n", "console.log(`Loose equality (==): 5 == '5' is ${5 == '5'}`);\n", "\n", "// Truthy and falsy values\n", "let falsyValues = [false, 0, -0, 0n, '', null, undefined, NaN];\n", "console.log('\\nFalsy values:');\n", "falsyValues.forEach(val => {\n", "    console.log(`${val} is ${Boolean(val) ? 'truthy' : 'falsy'}`);\n", "});"]}, {"cell_type": "markdown", "id": "71c71fe2", "metadata": {}, "source": ["# Control Flow and Loops {#control-flow}\n", "\n", "JavaScript provides various structures for controlling program flow: conditional statements, loops, and flow control statements."]}, {"cell_type": "code", "execution_count": null, "id": "ff42471f", "metadata": {}, "outputs": [], "source": ["// Conditional statements\n", "let score = 85;\n", "let grade;\n", "\n", "if (score >= 90) {\n", "    grade = 'A';\n", "} else if (score >= 80) {\n", "    grade = 'B';\n", "} else if (score >= 70) {\n", "    grade = 'C';\n", "} else {\n", "    grade = 'F';\n", "}\n", "\n", "console.log(`Score: ${score}, Grade: ${grade}`);\n", "\n", "// Ternary operator\n", "let status = score >= 70 ? 'Pass' : 'Fail';\n", "console.log(`Status: ${status}`);\n", "\n", "// Switch statement\n", "let day = 3;\n", "let dayName;\n", "\n", "switch (day) {\n", "    case 1:\n", "        dayName = 'Monday';\n", "        break;\n", "    case 2:\n", "        dayName = 'Tuesday';\n", "        break;\n", "    case 3:\n", "        dayName = 'Wednesday';\n", "        break;\n", "    default:\n", "        dayName = 'Unknown';\n", "}\n", "console.log(`\\nDay ${day} is ${dayName}`);\n", "\n", "// For loop\n", "console.log('\\nFor loop:');\n", "for (let i = 0; i < 5; i++) {\n", "    console.log(`Iteration ${i}`);\n", "}\n", "\n", "// For...of loop (for arrays)\n", "console.log('\\nFor...of loop:');\n", "let fruits = ['apple', 'banana', 'cherry'];\n", "for (let fruit of fruits) {\n", "    console.log(fruit);\n", "}\n", "\n", "// For...in loop (for object properties)\n", "console.log('\\nFor...in loop:');\n", "let person = { name: '<PERSON>', age: 30, city: 'New York' };\n", "for (let key in person) {\n", "    console.log(`${key}: ${person[key]}`);\n", "}\n", "\n", "// While loop with break and continue\n", "console.log('\\nWhile loop with break and continue:');\n", "let counter = 0;\n", "while (true) {\n", "    counter++;\n", "    if (counter === 3) {\n", "        continue; // Skip iteration when counter is 3\n", "    }\n", "    if (counter > 5) {\n", "        break;    // Exit loop when counter exceeds 5\n", "    }\n", "    console.log(`Counter: ${counter}`);\n", "}\n", "\n", "// Do...while loop\n", "console.log('\\nDo...while loop:');\n", "let num = 1;\n", "do {\n", "    console.log(`Number: ${num}`);\n", "    num++;\n", "} while (num <= 3);\n", "\n", "// Array methods for iteration\n", "console.log('\\nArray iteration methods:');\n", "let numbers = [1, 2, 3, 4, 5];\n", "\n", "// for<PERSON>ach\n", "numbers.forEach((num, index) => {\n", "    console.log(`Index ${index}: ${num}`);\n", "});\n", "\n", "// map\n", "let doubled = numbers.map(num => num * 2);\n", "console.log(`Doubled: ${doubled}`);\n", "\n", "// filter\n", "let evenNumbers = numbers.filter(num => num % 2 === 0);\n", "console.log(`Even numbers: ${evenNumbers}`);"]}, {"cell_type": "markdown", "id": "b3ad4049", "metadata": {}, "source": ["# Functions and Arrow Functions {#functions}\n", "\n", "Functions are reusable blocks of code. JavaScript supports function declarations, expressions, arrow functions, and various parameter patterns."]}, {"cell_type": "code", "execution_count": null, "id": "4adc818d", "metadata": {}, "outputs": [], "source": ["// Function declaration\n", "function greet(name, greeting = \"Hello\") {\n", "    /**\n", "     * A simple greeting function with a default parameter.\n", "     * @param {string} name - The name to greet\n", "     * @param {string} greeting - The greeting to use (default: \"Hello\")\n", "     * @returns {string} The complete greeting message\n", "     */\n", "    return `${greeting}, ${name}!`;\n", "}\n", "\n", "// Function calls\n", "console.log(greet(\"<PERSON>\"));\n", "console.log(greet(\"<PERSON>\", \"<PERSON>\"));\n", "\n", "// Function expression\n", "const multiply = function(a, b) {\n", "    return a * b;\n", "};\n", "\n", "console.log(`Multiply: ${multiply(5, 3)}`);\n", "\n", "// Arrow functions\n", "const square = x => x ** 2;\n", "const cube = x => x ** 3;\n", "const add = (a, b) => a + b;\n", "const complexFunction = (x, y) => {\n", "    const sum = x + y;\n", "    const product = x * y;\n", "    return { sum, product };\n", "};\n", "\n", "console.log(`\\nSquare of 5: ${square(5)}`);\n", "console.log(`Cube of 3: ${cube(3)}`);\n", "console.log(`Add 2 + 3: ${add(2, 3)}`);\n", "console.log(`Complex function result:`, complexFunction(4, 5));\n", "\n", "// Rest parameters\n", "function sum(...numbers) {\n", "    return numbers.reduce((total, num) => total + num, 0);\n", "}\n", "\n", "console.log(`\\nSum of multiple numbers: ${sum(1, 2, 3, 4, 5)}`);\n", "\n", "// Destructuring parameters\n", "function displayPerson({ name, age, city = \"Unknown\" }) {\n", "    console.log(`Name: ${name}, Age: ${age}, City: ${city}`);\n", "}\n", "\n", "display<PERSON><PERSON>({ name: \"<PERSON>\", age: 30, city: \"New York\" });\n", "display<PERSON><PERSON>({ name: \"<PERSON>\", age: 25 });\n", "\n", "// Higher-order functions\n", "function createMultiplier(factor) {\n", "    return function(x) {\n", "        return x * factor;\n", "    };\n", "}\n", "\n", "const double = createMultiplier(2);\n", "const triple = createMultiplier(3);\n", "\n", "console.log(`\\nDouble 5: ${double(5)}`);\n", "console.log(`Triple 5: ${triple(5)}`);\n", "\n", "// Function as callback\n", "function processArray(arr, callback) {\n", "    return arr.map(callback);\n", "}\n", "\n", "const numbers = [1, 2, 3, 4, 5];\n", "const squared = processArray(numbers, x => x ** 2);\n", "console.log(`\\nSquared numbers: ${squared}`);\n", "\n", "// Immediately Invoked Function Expression (IIFE)\n", "(function() {\n", "    console.log(\"\\nThis function runs immediately!\");\n", "})();\n", "\n", "// Arrow function IIFE\n", "(() => {\n", "    console.log(\"Arrow function IIFE!\");\n", "})();\n", "\n", "// Function hoisting demonstration\n", "console.log(`\\nHoisted function result: ${hoistedFunction(5)}`);\n", "\n", "function hoistedFunction(x) {\n", "    return x * 2;\n", "}\n", "\n", "// Method shorthand in objects\n", "const calculator = {\n", "    add(a, b) {\n", "        return a + b;\n", "    },\n", "    subtract: (a, b) => a - b, // Arrow function as method\n", "    multiply: function(a, b) {\n", "        return a * b;\n", "    }\n", "};\n", "\n", "console.log(`\\nCalculator methods:`);\n", "console.log(`Add: ${calculator.add(10, 5)}`);\n", "console.log(`Subtract: ${calculator.subtract(10, 5)}`);\n", "console.log(`Multiply: ${calculator.multiply(10, 5)}`);"]}, {"cell_type": "markdown", "id": "fa36a827", "metadata": {}, "source": ["# Objects and Prototypes {#objects}\n", "\n", "JavaScript is a prototype-based language. Objects are collections of key-value pairs and can inherit from other objects through the prototype chain."]}, {"cell_type": "code", "execution_count": null, "id": "56c306d3", "metadata": {}, "outputs": [], "source": ["// Object creation methods\n", "console.log(\"=== Object Creation ===\");\n", "\n", "// Object literal\n", "const person = {\n", "    name: \"<PERSON>\",\n", "    age: 30,\n", "    city: \"New York\",\n", "    greet() {\n", "        return `Hello, I'm ${this.name}`;\n", "    }\n", "};\n", "\n", "console.log(person.greet());\n", "console.log(`Person:`, person);\n", "\n", "// Object.create()\n", "const personPrototype = {\n", "    greet() {\n", "        return `Hello, I'm ${this.name}`;\n", "    },\n", "    introduce() {\n", "        return `I'm ${this.name}, ${this.age} years old`;\n", "    }\n", "};\n", "\n", "const john = Object.create(personPrototype);\n", "john.name = \"<PERSON>\";\n", "john.age = 25;\n", "\n", "console.log(`\\n${john.introduce()}`);\n", "\n", "// Constructor function\n", "function Person(name, age) {\n", "    this.name = name;\n", "    this.age = age;\n", "}\n", "\n", "Person.prototype.greet = function() {\n", "    return `Hello, I'm ${this.name}`;\n", "};\n", "\n", "Person.prototype.getAge = function() {\n", "    return this.age;\n", "};\n", "\n", "const bob = new Person(\"<PERSON>\", 28);\n", "console.log(`\\n${bob.greet()}`);\n", "console.log(`<PERSON>'s age: ${bob.getAge()}`);\n", "\n", "// Property access and manipulation\n", "console.log(\"\\n=== Property Operations ===\");\n", "\n", "// Dot notation vs bracket notation\n", "console.log(`Dot notation: ${person.name}`);\n", "console.log(`Bracket notation: ${person['age']}`);\n", "\n", "// Dynamic property access\n", "const prop = \"city\";\n", "console.log(`Dynamic access: ${person[prop]}`);\n", "\n", "// Adding and deleting properties\n", "person.email = \"<EMAIL>\";\n", "console.log(`After adding email:`, person);\n", "\n", "delete person.city;\n", "console.log(`After deleting city:`, person);\n", "\n", "// Object methods\n", "console.log(\"\\n=== Object Methods ===\");\n", "\n", "const user = {\n", "    name: \"<PERSON>\",\n", "    age: 35,\n", "    skills: [\"JavaScript\", \"Python\", \"React\"]\n", "};\n", "\n", "console.log(`Keys: ${Object.keys(user)}`);\n", "console.log(`Values:`, Object.values(user));\n", "console.log(`Entries:`, Object.entries(user));\n", "\n", "// Object.assign() for copying/merging\n", "const userCopy = Object.assign({}, user);\n", "const extendedUser = Object.assign({}, user, { country: \"USA\", active: true });\n", "\n", "console.log(`\\nExtended user:`, extendedUser);\n", "\n", "// Spread operator for objects\n", "const spreadCopy = { ...user };\n", "const spreadExtended = { ...user, country: \"Canada\", age: 36 };\n", "\n", "console.log(`Spread extended:`, spreadExtended);\n", "\n", "// Property descriptors\n", "console.log(\"\\n=== Property Descriptors ===\");\n", "\n", "const product = {};\n", "\n", "Object.defineProperty(product, 'name', {\n", "    value: 'Laptop',\n", "    writable: false,\n", "    enumerable: true,\n", "    configurable: true\n", "});\n", "\n", "console.log(`Product:`, product);\n", "console.log(`Name descriptor:`, Object.getOwnPropertyDescriptor(product, 'name'));\n", "\n", "// Getters and setters\n", "const temperature = {\n", "    _celsius: 0,\n", "    \n", "    get celsius() {\n", "        return this._celsius;\n", "    },\n", "    \n", "    set celsius(value) {\n", "        if (value < -273.15) {\n", "            throw new Error(\"Temperature below absolute zero!\");\n", "        }\n", "        this._celsius = value;\n", "    },\n", "    \n", "    get fahrenheit() {\n", "        return (this._celsius * 9/5) + 32;\n", "    }\n", "};\n", "\n", "temperature.celsius = 25;\n", "console.log(`\\nTemperature: ${temperature.celsius}°C = ${temperature.fahrenheit}°F`);\n", "\n", "// Prototype chain\n", "console.log(\"\\n=== Prototype Chain ===\");\n", "\n", "console.log(`bob instanceof Person: ${bob instanceof Person}`);\n", "console.log(`bob.__proto__ === Person.prototype: ${bob.__proto__ === Person.prototype}`);\n", "console.log(`Person.prototype.constructor === Person: ${Person.prototype.constructor === Person}`);\n", "\n", "// Checking prototype chain\n", "console.log(`bob.hasOwnProperty('name'): ${bob.hasOwnProperty('name')}`);\n", "console.log(`bob.hasOwnProperty('greet'): ${bob.hasOwnProperty('greet')}`);\n", "console.log(`'greet' in bob: ${'greet' in bob}`);"]}, {"cell_type": "markdown", "id": "dfa02e04", "metadata": {}, "source": ["# Arrays and Array Methods {#arrays}\n", "\n", "Arrays are ordered collections of elements. JavaScript provides many powerful methods for array manipulation, iteration, and transformation."]}, {"cell_type": "code", "execution_count": null, "id": "9440a6fd", "metadata": {}, "outputs": [], "source": ["// Array creation and basic operations\n", "console.log(\"=== Array Creation ===\");\n", "\n", "// Array literal\n", "const fruits = ['apple', 'banana', 'cherry'];\n", "console.log(`Original array: ${fruits}`);\n", "\n", "// Array constructor\n", "const numbers = new Array(1, 2, 3, 4, 5);\n", "const emptyArray = new Array(5); // Creates array with 5 empty slots\n", "console.log(`Numbers: ${numbers}`);\n", "console.log(`Empty array length: ${emptyArray.length}`);\n", "\n", "// Array.from() and Array.of()\n", "const fromString = Array.from('hello');\n", "const fromRange = Array.from({length: 5}, (_, i) => i * 2);\n", "const ofNumbers = Array.of(1, 2, 3, 4, 5);\n", "\n", "console.log(`From string: ${fromString}`);\n", "console.log(`From range: ${fromRange}`);\n", "console.log(`Array.of: ${ofNumbers}`);\n", "\n", "// Array modification methods\n", "console.log(\"\\n=== Array Modification ===\");\n", "\n", "// Adding elements\n", "fruits.push('date');           // Add to end\n", "fruits.unshift('apricot');     // Add to beginning\n", "console.log(`After push/unshift: ${fruits}`);\n", "\n", "// Removing elements\n", "const lastFruit = fruits.pop();     // Remove from end\n", "const firstFruit = fruits.shift();  // Remove from beginning\n", "console.log(`Removed: ${firstFruit}, ${lastFruit}`);\n", "console.log(`After pop/shift: ${fruits}`);\n", "\n", "// Splice method (add/remove at any position)\n", "const removed = fruits.splice(1, 1, 'blueberry', 'grape');\n", "console.log(`Removed by splice: ${removed}`);\n", "console.log(`After splice: ${fruits}`);\n", "\n", "// Array iteration methods\n", "console.log(\"\\n=== Array Iteration ===\");\n", "\n", "const nums = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n", "\n", "// forEach - execute function for each element\n", "console.log('forEach:');\n", "nums.forEach((num, index) => {\n", "    if (index < 3) console.log(`Index ${index}: ${num}`);\n", "});\n", "\n", "// map - transform each element\n", "const squared = nums.map(num => num ** 2);\n", "console.log(`\\nSquared: ${squared.slice(0, 5)}...`);\n", "\n", "// filter - select elements that match condition\n", "const evenNums = nums.filter(num => num % 2 === 0);\n", "console.log(`Even numbers: ${evenNums}`);\n", "\n", "// reduce - accumulate values\n", "const sum = nums.reduce((acc, num) => acc + num, 0);\n", "const product = nums.reduce((acc, num) => acc * num, 1);\n", "console.log(`Sum: ${sum}, Product: ${product}`);\n", "\n", "// find and findIndex\n", "const found = nums.find(num => num > 5);\n", "const foundIndex = nums.findIndex(num => num > 5);\n", "console.log(`\\nFirst number > 5: ${found} at index ${foundIndex}`);\n", "\n", "// some and every\n", "const hasEven = nums.some(num => num % 2 === 0);\n", "const allPositive = nums.every(num => num > 0);\n", "console.log(`Has even numbers: ${hasEven}, All positive: ${allPositive}`);\n", "\n", "// Array searching and sorting\n", "console.log(\"\\n=== Array Searching and Sorting ===\");\n", "\n", "// includes and indexOf\n", "console.log(`Includes 5: ${nums.includes(5)}`);\n", "console.log(`Index of 5: ${nums.indexOf(5)}`);\n", "console.log(`Last index of 5: ${nums.lastIndexOf(5)}`);\n", "\n", "// Sorting\n", "const words = ['banana', 'apple', 'cherry', 'date'];\n", "const sortedWords = [...words].sort(); // Create copy before sorting\n", "console.log(`\\nOriginal: ${words}`);\n", "console.log(`Sorted: ${sortedWords}`);\n", "\n", "// Custom sort\n", "const people = [\n", "    { name: '<PERSON>', age: 30 },\n", "    { name: '<PERSON>', age: 25 },\n", "    { name: '<PERSON>', age: 35 }\n", "];\n", "\n", "const sortedByAge = [...people].sort((a, b) => a.age - b.age);\n", "console.log('\\nSorted by age:', sortedByAge.map(p => `${p.name}(${p.age})`));\n", "\n", "// Array utility methods\n", "console.log(\"\\n=== Array Utilities ===\");\n", "\n", "// join and split\n", "const joined = fruits.join(' | ');\n", "console.log(`Joined: ${joined}`);\n", "\n", "// slice (non-mutating)\n", "const sliced = nums.slice(2, 5);\n", "console.log(`Sliced (2-5): ${sliced}`);\n", "\n", "// concat\n", "const moreNums = [11, 12, 13];\n", "const combined = nums.concat(moreNums);\n", "console.log(`Combined length: ${combined.length}`);\n", "\n", "// flat and flatMap\n", "const nested = [[1, 2], [3, 4], [5, [6, 7]]];\n", "const flattened = nested.flat();\n", "const deepFlattened = nested.flat(2);\n", "console.log(`\\nNested: ${JSON.stringify(nested)}`);\n", "console.log(`Flattened: ${flattened}`);\n", "console.log(`Deep flattened: ${deepFlattened}`);\n", "\n", "// Array destructuring\n", "console.log(\"\\n=== Array Destructuring ===\");\n", "\n", "const [first, second, ...rest] = nums;\n", "console.log(`First: ${first}, Second: ${second}, Rest length: ${rest.length}`);\n", "\n", "// Swapping variables\n", "let a = 1, b = 2;\n", "[a, b] = [b, a];\n", "console.log(`After swap: a=${a}, b=${b}`);"]}, {"cell_type": "markdown", "id": "bc9108b2", "metadata": {}, "source": ["# DOM Manipulation and Events {#dom}\n", "\n", "The Document Object Model (DOM) allows JavaScript to interact with HTML elements. This section covers element selection, manipulation, and event handling."]}, {"cell_type": "code", "execution_count": null, "id": "e5eb596c", "metadata": {}, "outputs": [], "source": ["// Note: These examples work in a browser environment\n", "// For Node.js, you would need a DOM library like jsdom\n", "\n", "// Element selection\n", "console.log(\"=== DOM Element Selection ===\");\n", "\n", "// Basic selectors (browser only)\n", "/*\n", "// By ID\n", "const elementById = document.getElementById('myId');\n", "\n", "// By class name\n", "const elementsByClass = document.getElementsByClassName('myClass');\n", "\n", "// By tag name\n", "const elementsByTag = document.getElementsByTagName('div');\n", "\n", "// Query selectors (more flexible)\n", "const element = document.querySelector('.myClass');\n", "const elements = document.querySelectorAll('div.myClass');\n", "\n", "// Advanced selectors\n", "const firstChild = document.querySelector('ul > li:first-child');\n", "const dataElements = document.querySelectorAll('[data-type=\"example\"]');\n", "*/\n", "\n", "// Creating a mock DOM environment for demonstration\n", "class MockElement {\n", "    constructor(tagName) {\n", "        this.tagName = tagName;\n", "        this.textContent = '';\n", "        this.innerHTML = '';\n", "        this.style = {};\n", "        this.classList = new Set();\n", "        this.attributes = new Map();\n", "        this.children = [];\n", "        this.eventListeners = new Map();\n", "    }\n", "    \n", "    setAttribute(name, value) {\n", "        this.attributes.set(name, value);\n", "    }\n", "    \n", "    getAttribute(name) {\n", "        return this.attributes.get(name);\n", "    }\n", "    \n", "    addEventListener(event, handler) {\n", "        if (!this.eventListeners.has(event)) {\n", "            this.eventListeners.set(event, []);\n", "        }\n", "        this.eventListeners.get(event).push(handler);\n", "    }\n", "    \n", "    removeEventListener(event, handler) {\n", "        if (this.eventListeners.has(event)) {\n", "            const handlers = this.eventListeners.get(event);\n", "            const index = handlers.indexOf(handler);\n", "            if (index > -1) handlers.splice(index, 1);\n", "        }\n", "    }\n", "    \n", "    append<PERSON><PERSON><PERSON>(child) {\n", "        this.children.push(child);\n", "    }\n", "    \n", "    remove<PERSON><PERSON><PERSON>(child) {\n", "        const index = this.children.indexOf(child);\n", "        if (index > -1) this.children.splice(index, 1);\n", "    }\n", "}\n", "\n", "// Element manipulation examples\n", "console.log(\"\\n=== DOM Element Manipulation ===\");\n", "\n", "const element = new MockElement('div');\n", "\n", "// Text content\n", "element.textContent = 'Hello, <PERSON>!';\n", "console.log(`Text content: ${element.textContent}`);\n", "\n", "// HTML content\n", "element.innerHTML = '<span>Hello, <strong>World!</strong></span>';\n", "console.log(`HTML content: ${element.innerHTML}`);\n", "\n", "// Attributes\n", "element.setAttribute('id', 'myElement');\n", "element.setAttribute('data-value', '123');\n", "console.log(`ID attribute: ${element.getAttribute('id')}`);\n", "console.log(`Data attribute: ${element.getAttribute('data-value')}`);\n", "\n", "// CSS classes\n", "element.classList.add('active');\n", "element.classList.add('highlight');\n", "console.log(`Classes: ${Array.from(element.classList).join(', ')}`);\n", "\n", "// Styles\n", "element.style.color = 'blue';\n", "element.style.fontSize = '16px';\n", "element.style.backgroundColor = 'lightgray';\n", "console.log(`Styles:`, element.style);\n", "\n", "// Event handling examples\n", "console.log(\"\\n=== Event Handling ===\");\n", "\n", "// Event listener functions\n", "function handleClick(event) {\n", "    console.log('<PERSON><PERSON> clicked!');\n", "}\n", "\n", "function handleMouseOver(event) {\n", "    console.log('Mouse over element!');\n", "}\n", "\n", "// Adding event listeners\n", "element.addEventListener('click', handleClick);\n", "element.addEventListener('mouseover', handleMouseOver);\n", "\n", "// Arrow function event listener\n", "element.addEventListener('keydown', (event) => {\n", "    console.log(`Key pressed: ${event.key}`);\n", "});\n", "\n", "console.log(`Event listeners added: ${element.eventListeners.size} types`);\n", "\n", "// Event delegation example (conceptual)\n", "const parentElement = new MockElement('ul');\n", "const listItem1 = new MockElement('li');\n", "const listItem2 = new MockElement('li');\n", "\n", "listItem1.textContent = 'Item 1';\n", "listItem2.textContent = 'Item 2';\n", "\n", "parentElement.appendChild(listItem1);\n", "parentElement.appendChild(listItem2);\n", "\n", "// Event delegation - handle clicks on parent\n", "parentElement.addEventListener('click', (event) => {\n", "    if (event.target.tagName === 'LI') {\n", "        console.log(`Clicked on: ${event.target.textContent}`);\n", "    }\n", "});\n", "\n", "console.log(`\\nParent element has ${parentElement.children.length} children`);"]}, {"cell_type": "markdown", "id": "b1d7dad6", "metadata": {}, "source": ["# Asynchronous JavaScript (Promises, Async/Await) {#async}\n", "\n", "JavaScript handles asynchronous operations through callbacks, Promises, and async/await syntax. This enables non-blocking code execution."]}, {"cell_type": "code", "execution_count": null, "id": "6dbdc6d7", "metadata": {}, "outputs": [], "source": ["// Callbacks (traditional approach)\n", "console.log(\"=== Callbacks ===\");\n", "\n", "function fetchDataCallback(callback) {\n", "    setTimeout(() => {\n", "        const data = { id: 1, name: \"<PERSON>\" };\n", "        callback(null, data); // null for error, data for success\n", "    }, 1000);\n", "}\n", "\n", "console.log(\"Fetching data with callback...\");\n", "fetchDataCallback((error, data) => {\n", "    if (error) {\n", "        console.error(\"Error:\", error);\n", "    } else {\n", "        console.log(\"Callback data:\", data);\n", "    }\n", "});\n", "\n", "// Promises\n", "console.log(\"\\n=== Promises ===\");\n", "\n", "// Creating a Promise\n", "function fetchDataPromise() {\n", "    return new Promise((resolve, reject) => {\n", "        setTimeout(() => {\n", "            const success = Math.random() > 0.3; // 70% success rate\n", "            if (success) {\n", "                resolve({ id: 2, name: \"<PERSON>\" });\n", "            } else {\n", "                reject(new Error(\"Failed to fetch data\"));\n", "            }\n", "        }, 1000);\n", "    });\n", "}\n", "\n", "// Using Promises with .then() and .catch()\n", "console.log(\"Fetching data with Promise...\");\n", "fetchDataPromise()\n", "    .then(data => {\n", "        console.log(\"Promise data:\", data);\n", "        return data.id; // Return value for next .then()\n", "    })\n", "    .then(id => {\n", "        console.log(\"User ID:\", id);\n", "    })\n", "    .catch(error => {\n", "        console.error(\"Promise error:\", error.message);\n", "    })\n", "    .finally(() => {\n", "        console.log(\"Promise completed\");\n", "    });\n", "\n", "// Promise.all() - wait for all promises\n", "const promise1 = Promise.resolve(\"First\");\n", "const promise2 = Promise.resolve(\"Second\");\n", "const promise3 = new Promise(resolve => {\n", "    setTimeout(() => resolve(\"Third\"), 500);\n", "});\n", "\n", "Promise.all([promise1, promise2, promise3])\n", "    .then(values => {\n", "        console.log(\"\\nPromise.all results:\", values);\n", "    });\n", "\n", "// Promise.race() - first to complete\n", "const fastPromise = new Promise(resolve => setTimeout(() => resolve(\"Fast\"), 100));\n", "const slowPromise = new Promise(resolve => setTimeout(() => resolve(\"Slow\"), 500));\n", "\n", "Promise.race([fastPromise, slowPromise])\n", "    .then(value => {\n", "        console.log(\"Promise.race winner:\", value);\n", "    });\n", "\n", "// Async/Await\n", "console.log(\"\\n=== Async/Await ===\");\n", "\n", "// Async function declaration\n", "async function fetchUserData(userId) {\n", "    try {\n", "        console.log(`Fetching user ${userId}...`);\n", "        \n", "        // Simulate API call\n", "        const userData = await new Promise((resolve, reject) => {\n", "            setTimeout(() => {\n", "                if (userId > 0) {\n", "                    resolve({ id: userId, name: `User ${userId}`, email: `user${userId}@example.com` });\n", "                } else {\n", "                    reject(new Error(\"Invalid user ID\"));\n", "                }\n", "            }, 800);\n", "        });\n", "        \n", "        console.log(\"User data:\", userData);\n", "        return userData;\n", "        \n", "    } catch (error) {\n", "        console.error(\"Error fetching user:\", error.message);\n", "        throw error; // Re-throw if needed\n", "    }\n", "}\n", "\n", "// Using async function\n", "fetchUserData(1)\n", "    .then(user => {\n", "        console.log(\"Received user:\", user.name);\n", "    })\n", "    .catch(error => {\n", "        console.error(\"Failed to get user:\", error.message);\n", "    });\n", "\n", "// Async arrow function\n", "const processMultipleUsers = async (userIds) => {\n", "    console.log(\"\\nProcessing multiple users...\");\n", "    \n", "    try {\n", "        // Sequential processing\n", "        const users = [];\n", "        for (const id of userIds) {\n", "            const user = await fetchUserData(id);\n", "            users.push(user);\n", "        }\n", "        \n", "        console.log(\"All users processed:\", users.length);\n", "        return users;\n", "        \n", "    } catch (error) {\n", "        console.error(\"Error processing users:\", error.message);\n", "    }\n", "};\n", "\n", "// Parallel processing with Promise.all\n", "const processUsersParallel = async (userIds) => {\n", "    console.log(\"\\nProcessing users in parallel...\");\n", "    \n", "    try {\n", "        const userPromises = userIds.map(id => fetchUserData(id));\n", "        const users = await Promise.all(userPromises);\n", "        \n", "        console.log(\"All users processed in parallel:\", users.length);\n", "        return users;\n", "        \n", "    } catch (error) {\n", "        console.error(\"Error in parallel processing:\", error.message);\n", "    }\n", "};\n", "\n", "// Error handling with async/await\n", "async function demonstrateErrorHandling() {\n", "    try {\n", "        await fetchUserData(-1); // This will fail\n", "    } catch (error) {\n", "        console.log(\"\\nCaught error in async function:\", error.message);\n", "    }\n", "}\n", "\n", "demonstrateError<PERSON><PERSON>ling();\n", "\n", "// Promise utilities\n", "console.log(\"\\n=== Promise Utilities ===\");\n", "\n", "// Creating resolved/rejected promises\n", "const resolvedPromise = Promise.resolve(\"Already resolved\");\n", "const rejectedPromise = Promise.reject(new Error(\"Already rejected\"));\n", "\n", "resolvedPromise.then(value => console.log(\"Resolved:\", value));\n", "rejectedPromise.catch(error => console.log(\"Rejected:\", error.message));\n", "\n", "// Promise.allSettled() - wait for all, regardless of outcome\n", "const mixedPromises = [\n", "    Promise.resolve(\"Success 1\"),\n", "    Promise.reject(new Error(\"Error 1\")),\n", "    Promise.resolve(\"Success 2\")\n", "];\n", "\n", "Promise.allSettled(mixedPromises)\n", "    .then(results => {\n", "        console.log(\"\\nPromise.allSettled results:\");\n", "        results.forEach((result, index) => {\n", "            if (result.status === 'fulfilled') {\n", "                console.log(`  ${index}: Success - ${result.value}`);\n", "            } else {\n", "                console.log(`  ${index}: Failed - ${result.reason.message}`);\n", "            }\n", "        });\n", "    });"]}, {"cell_type": "markdown", "id": "09b7097e", "metadata": {}, "source": ["# ES6+ Features (Destructuring, Spread, Rest) {#es6}\n", "\n", "ES6 (ES2015) and later versions introduced many powerful features that make JavaScript more expressive and concise."]}, {"cell_type": "code", "execution_count": null, "id": "bdaf0635", "metadata": {}, "outputs": [], "source": ["// Destructuring Assignment\n", "console.log(\"=== Destructuring ===\");\n", "\n", "// Array destructuring\n", "const colors = ['red', 'green', 'blue', 'yellow'];\n", "const [primary, secondary, ...otherColors] = colors;\n", "\n", "console.log(`Primary: ${primary}`);\n", "console.log(`Secondary: ${secondary}`);\n", "console.log(`Other colors: ${otherColors}`);\n", "\n", "// Skipping elements\n", "const [first, , third] = colors;\n", "console.log(`First: ${first}, Third: ${third}`);\n", "\n", "// Default values\n", "const [a, b, c, d, e = 'default'] = colors;\n", "console.log(`Fifth color: ${e}`);\n", "\n", "// Object destructuring\n", "const person = {\n", "    name: '<PERSON>',\n", "    age: 30,\n", "    city: 'New York',\n", "    country: 'USA'\n", "};\n", "\n", "const { name, age, ...address } = person;\n", "console.log(`\\nName: ${name}, Age: ${age}`);\n", "console.log(`Address:`, address);\n", "\n", "// Renaming variables\n", "const { name: <PERSON><PERSON><PERSON>, age: years } = person;\n", "console.log(`Full name: ${fullName}, Years: ${years}`);\n", "\n", "// Nested destructuring\n", "const user = {\n", "    id: 1,\n", "    profile: {\n", "        name: '<PERSON>',\n", "        contact: {\n", "            email: '<EMAIL>',\n", "            phone: '************'\n", "        }\n", "    }\n", "};\n", "\n", "const { profile: { name: user<PERSON><PERSON>, contact: { email } } } = user;\n", "console.log(`\\nUser: ${userName}, Email: ${email}`);\n", "\n", "// Spread Operator\n", "console.log(\"\\n=== Spread Operator ===\");\n", "\n", "// Array spread\n", "const arr1 = [1, 2, 3];\n", "const arr2 = [4, 5, 6];\n", "const combined = [...arr1, ...arr2];\n", "const withExtra = [0, ...arr1, 3.5, ...arr2, 7];\n", "\n", "console.log(`Combined: ${combined}`);\n", "console.log(`With extra: ${withExtra}`);\n", "\n", "// Array copying\n", "const original = [1, 2, 3];\n", "const copy = [...original];\n", "copy.push(4);\n", "console.log(`Original: ${original}, Copy: ${copy}`);\n", "\n", "// Object spread\n", "const obj1 = { a: 1, b: 2 };\n", "const obj2 = { c: 3, d: 4 };\n", "const mergedObj = { ...obj1, ...obj2 };\n", "const extendedObj = { ...obj1, b: 20, e: 5 }; // Override and add\n", "\n", "console.log(`\\nMerged object:`, mergedObj);\n", "console.log(`Extended object:`, extendedObj);\n", "\n", "// Function arguments spread\n", "function sum(a, b, c) {\n", "    return a + b + c;\n", "}\n", "\n", "const numbers = [1, 2, 3];\n", "console.log(`Sum using spread: ${sum(...numbers)}`);\n", "\n", "// Rest Parameters\n", "console.log(\"\\n=== Rest Parameters ===\");\n", "\n", "// Function with rest parameters\n", "function multiply(multiplier, ...numbers) {\n", "    return numbers.map(num => num * multiplier);\n", "}\n", "\n", "console.log(`Multiply by 2: ${multiply(2, 1, 2, 3, 4, 5)}`);\n", "\n", "// Rest in destructuring\n", "const [head, ...tail] = [1, 2, 3, 4, 5];\n", "console.log(`Head: ${head}, Tail: ${tail}`);\n", "\n", "const { x, ...remaining } = { x: 1, y: 2, z: 3 };\n", "console.log(`x: ${x}, Remaining:`, remaining);\n", "\n", "// Template Literals\n", "console.log(\"\\n=== Template Literals ===\");\n", "\n", "const product = 'laptop';\n", "const price = 999;\n", "const discount = 0.1;\n", "\n", "// Multi-line strings\n", "const description = `\n", "Product: ${product}\n", "Price: $${price}\n", "Discount: ${discount * 100}%\n", "Final Price: $${price * (1 - discount)}\n", "`;\n", "\n", "console.log(description);\n", "\n", "// Tagged template literals\n", "function highlight(strings, ...values) {\n", "    return strings.reduce((result, string, i) => {\n", "        const value = values[i] ? `**${values[i]}**` : '';\n", "        return result + string + value;\n", "    }, '');\n", "}\n", "\n", "const highlighted = highlight`The price is ${price} with ${discount * 100}% discount`;\n", "console.log(`Highlighted: ${highlighted}`);\n", "\n", "// Enhanced Object Literals\n", "console.log(\"\\n=== Enhanced Object Literals ===\");\n", "\n", "const propName = 'dynamicProp';\n", "const value = 'dynamic value';\n", "\n", "const enhancedObj = {\n", "    // Shorthand property\n", "    name,\n", "    age,\n", "    \n", "    // Computed property names\n", "    [propName]: value,\n", "    [`${propName}2`]: 'another value',\n", "    \n", "    // Method shorthand\n", "    greet() {\n", "        return `Hello, I'm ${this.name}`;\n", "    },\n", "    \n", "    // Getter and setter\n", "    get info() {\n", "        return `${this.name} (${this.age})`;\n", "    },\n", "    \n", "    set info(value) {\n", "        [this.name, this.age] = value.split(' ');\n", "    }\n", "};\n", "\n", "console.log(`Enhanced object:`, enhancedObj);\n", "console.log(`Greeting: ${enhancedObj.greet()}`);\n", "console.log(`Info: ${enhancedObj.info}`);"]}, {"cell_type": "markdown", "id": "ded1ffb4", "metadata": {}, "source": ["# Classes and Inheritance {#classes}\n", "\n", "ES6 introduced class syntax that provides a cleaner way to create objects and handle inheritance, built on top of JavaScript's prototype system."]}, {"cell_type": "code", "execution_count": null, "id": "b35c08d6", "metadata": {}, "outputs": [], "source": ["// Basic class definition\n", "console.log(\"=== Basic Classes ===\");\n", "\n", "class Animal {\n", "    constructor(name, species) {\n", "        this.name = name;\n", "        this.species = species;\n", "    }\n", "    \n", "    // Instance method\n", "    makeSound() {\n", "        return `${this.name} makes a sound`;\n", "    }\n", "    \n", "    // <PERSON><PERSON>\n", "    get description() {\n", "        return `${this.name} is a ${this.species}`;\n", "    }\n", "    \n", "    // Setter\n", "    set name(newName) {\n", "        if (newName.length > 0) {\n", "            this._name = newName;\n", "        }\n", "    }\n", "    \n", "    get name() {\n", "        return this._name;\n", "    }\n", "    \n", "    // Static method\n", "    static getKingdom() {\n", "        return 'Animalia';\n", "    }\n", "}\n", "\n", "// Creating instances\n", "const animal = new Animal('<PERSON><PERSON>', 'Unknown');\n", "console.log(animal.description);\n", "console.log(animal.makeSound());\n", "console.log(`Kingdom: ${Animal.getKingdom()}`);\n", "\n", "// Inheritance\n", "console.log(\"\\n=== Inheritance ===\");\n", "\n", "class Dog extends Animal {\n", "    constructor(name, breed) {\n", "        super(name, '<PERSON>'); // Call parent constructor\n", "        this.breed = breed;\n", "    }\n", "    \n", "    // Override parent method\n", "    makeSound() {\n", "        return `${this.name} barks: Woof!`;\n", "    }\n", "    \n", "    // New method specific to <PERSON>\n", "    fetch(item) {\n", "        return `${this.name} fetches the ${item}`;\n", "    }\n", "    \n", "    // Override getter\n", "    get description() {\n", "        return `${super.description} (${this.breed} breed)`;\n", "    }\n", "}\n", "\n", "class Cat extends Animal {\n", "    constructor(name, color) {\n", "        super(name, '<PERSON>');\n", "        this.color = color;\n", "    }\n", "    \n", "    makeSound() {\n", "        return `${this.name} meows: Meow!`;\n", "    }\n", "    \n", "    scratch() {\n", "        return `${this.name} scratches with claws`;\n", "    }\n", "}\n", "\n", "// Using inherited classes\n", "const dog = new Dog('<PERSON>', 'German Shepherd');\n", "const cat = new Cat('Whiskers', '<PERSON>');\n", "\n", "console.log(dog.description);\n", "console.log(dog.makeSound());\n", "console.log(dog.fetch('ball'));\n", "\n", "console.log(`\\n${cat.description}`);\n", "console.log(cat.makeSound());\n", "console.log(cat.scratch());\n", "\n", "// Polymorphism\n", "console.log(\"\\n=== Polymorphism ===\");\n", "\n", "const animals = [dog, cat, new Animal('<PERSON>', 'Sparrow')];\n", "\n", "animals.forEach(animal => {\n", "    console.log(animal.makeSound());\n", "});\n", "\n", "// Private fields and methods (ES2022)\n", "console.log(\"\\n=== Private Fields ===\");\n", "\n", "class BankAccount {\n", "    #balance = 0; // Private field\n", "    #accountNumber; // Private field\n", "    \n", "    constructor(accountNumber, initialBalance = 0) {\n", "        this.#accountNumber = accountNumber;\n", "        this.#balance = initialBalance;\n", "    }\n", "    \n", "    // Private method\n", "    #validateAmount(amount) {\n", "        return amount > 0 && typeof amount === 'number';\n", "    }\n", "    \n", "    deposit(amount) {\n", "        if (this.#validateAmount(amount)) {\n", "            this.#balance += amount;\n", "            return true;\n", "        }\n", "        return false;\n", "    }\n", "    \n", "    withdraw(amount) {\n", "        if (this.#validateAmount(amount) && amount <= this.#balance) {\n", "            this.#balance -= amount;\n", "            return true;\n", "        }\n", "        return false;\n", "    }\n", "    \n", "    get balance() {\n", "        return this.#balance;\n", "    }\n", "    \n", "    get accountInfo() {\n", "        return `Account ${this.#accountNumber}: $${this.#balance}`;\n", "    }\n", "}\n", "\n", "const account = new BankAccount('12345', 1000);\n", "console.log(account.accountInfo);\n", "\n", "account.deposit(500);\n", "console.log(`After deposit: ${account.accountInfo}`);\n", "\n", "account.withdraw(200);\n", "console.log(`After withdrawal: ${account.accountInfo}`);\n", "\n", "// Static blocks (ES2022)\n", "console.log(\"\\n=== Static Blocks ===\");\n", "\n", "class Configuration {\n", "    static #config = {};\n", "    \n", "    // Static block for initialization\n", "    static {\n", "        this.#config = {\n", "            apiUrl: 'https://api.example.com',\n", "            timeout: 5000,\n", "            retries: 3\n", "        };\n", "        console.log('Configuration initialized');\n", "    }\n", "    \n", "    static getConfig(key) {\n", "        return this.#config[key];\n", "    }\n", "    \n", "    static getAllConfig() {\n", "        return { ...this.#config };\n", "    }\n", "}\n", "\n", "console.log('API URL:', Configuration.getConfig('apiUrl'));\n", "console.log('All config:', Configuration.getAllConfig());\n", "\n", "// Mixins pattern\n", "console.log(\"\\n=== Mixins ===\");\n", "\n", "// Mixin functions\n", "const Flyable = {\n", "    fly() {\n", "        return `${this.name} is flying!`;\n", "    }\n", "};\n", "\n", "const Swimmable = {\n", "    swim() {\n", "        return `${this.name} is swimming!`;\n", "    }\n", "};\n", "\n", "// Mixin helper function\n", "function mixin(target, ...sources) {\n", "    Object.assign(target.prototype, ...sources);\n", "}\n", "\n", "class Duck extends Animal {\n", "    constructor(name) {\n", "        super(name, '<PERSON>');\n", "    }\n", "    \n", "    makeSound() {\n", "        return `${this.name} quacks: Quack!`;\n", "    }\n", "}\n", "\n", "// Apply mixins\n", "mixin(Duck, Flyable, Swimmable);\n", "\n", "const duck = new <PERSON>('<PERSON>');\n", "console.log(duck.makeSound());\n", "console.log(duck.fly());\n", "console.log(duck.swim());"]}, {"cell_type": "markdown", "id": "70e66b75", "metadata": {}, "source": ["# Error Handling and Debugging {#errors}\n", "\n", "JavaScript provides robust error handling mechanisms and debugging tools to help identify and resolve issues in your code."]}, {"cell_type": "code", "execution_count": null, "id": "109dc761", "metadata": {}, "outputs": [], "source": ["// Basic try-catch-finally\n", "console.log(\"=== Error Handling ===\");\n", "\n", "function divide(a, b) {\n", "    try {\n", "        if (b === 0) {\n", "            throw new Error('Division by zero is not allowed');\n", "        }\n", "        return a / b;\n", "    } catch (error) {\n", "        console.error('Error:', error.message);\n", "        return null;\n", "    } finally {\n", "        console.log('Division operation completed');\n", "    }\n", "}\n", "\n", "console.log('Result:', divide(10, 2));\n", "console.log('Result:', divide(10, 0));\n", "\n", "// Custom error types\n", "class ValidationError extends Error {\n", "    constructor(message, field) {\n", "        super(message);\n", "        this.name = 'ValidationError';\n", "        this.field = field;\n", "    }\n", "}\n", "\n", "function validateAge(age) {\n", "    if (typeof age !== 'number') {\n", "        throw new ValidationError('Age must be a number', 'age');\n", "    }\n", "    if (age < 0 || age > 150) {\n", "        throw new ValidationError('Age must be between 0 and 150', 'age');\n", "    }\n", "    return true;\n", "}\n", "\n", "try {\n", "    validateAge('25');\n", "} catch (error) {\n", "    if (error instanceof ValidationError) {\n", "        console.log(`\\nValidation Error in ${error.field}: ${error.message}`);\n", "    }\n", "}\n", "\n", "// Debugging techniques\n", "console.log(\"\\n=== Debugging ===\");\n", "\n", "// Console methods\n", "console.log('Basic log message');\n", "console.warn('Warning message');\n", "console.error('Error message');\n", "console.info('Info message');\n", "\n", "// Console.table for objects/arrays\n", "const users = [\n", "    { name: '<PERSON>', age: 30, city: 'New York' },\n", "    { name: '<PERSON>', age: 25, city: 'London' },\n", "    { name: '<PERSON>', age: 35, city: 'Tokyo' }\n", "];\n", "console.table(users);\n", "\n", "// Console.group for organized logging\n", "console.group('User Processing');\n", "users.forEach(user => {\n", "    console.log(`Processing ${user.name}`);\n", "    console.log(`  Age: ${user.age}`);\n", "    console.log(`  City: ${user.city}`);\n", "});\n", "console.groupEnd();\n", "\n", "// Performance timing\n", "console.time('Array Processing');\n", "const largeArray = Array.from({length: 100000}, (_, i) => i);\n", "const processed = largeArray.map(x => x * 2).filter(x => x % 100 === 0);\n", "console.timeEnd('Array Processing');\n", "console.log(`Processed ${processed.length} items`);"]}, {"cell_type": "markdown", "id": "3ef56820", "metadata": {}, "source": ["# Best Practices and Code Style {#best-practices}\n", "\n", "Following best practices and consistent code style makes JavaScript code more readable, maintainable, and less prone to errors."]}, {"cell_type": "code", "execution_count": null, "id": "b8426e5b", "metadata": {}, "outputs": [], "source": ["// Variable declarations\n", "console.log(\"=== Best Practices ===\");\n", "\n", "// Use const for values that don't change\n", "const API_URL = 'https://api.example.com';\n", "const MAX_RETRIES = 3;\n", "\n", "// Use let for variables that will change\n", "let currentUser = null;\n", "let attemptCount = 0;\n", "\n", "// Avoid var (function-scoped, can cause issues)\n", "// var oldStyle = 'avoid this'; // Don't use\n", "\n", "// Meaningful variable names\n", "const userAccountBalance = 1000; // Good\n", "// const bal = 1000; // Avoid abbreviations\n", "\n", "// Function best practices\n", "// Pure functions (no side effects)\n", "function calculateTax(amount, rate) {\n", "    return amount * rate;\n", "}\n", "\n", "// Single responsibility\n", "function validateEmail(email) {\n", "    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n", "    return emailRegex.test(email);\n", "}\n", "\n", "function formatEmail(email) {\n", "    return email.toLowerCase().trim();\n", "}\n", "\n", "// Error handling patterns\n", "function safeParseJSON(jsonString) {\n", "    try {\n", "        return { success: true, data: JSON.parse(jsonString) };\n", "    } catch (error) {\n", "        return { success: false, error: error.message };\n", "    }\n", "}\n", "\n", "// Object and array best practices\n", "// Use object shorthand\n", "const name = '<PERSON>';\n", "const age = 30;\n", "const user = { name, age }; // Instead of { name: name, age: age }\n", "\n", "// Use array methods instead of loops when possible\n", "const numbers = [1, 2, 3, 4, 5];\n", "const doubled = numbers.map(n => n * 2); // Good\n", "// const doubled = []; for(let i = 0; i < numbers.length; i++) { doubled.push(numbers[i] * 2); } // Verbose\n", "\n", "// Async/await best practices\n", "async function fetchUserData(userId) {\n", "    try {\n", "        const response = await fetch(`/api/users/${userId}`);\n", "        if (!response.ok) {\n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "        }\n", "        return await response.json();\n", "    } catch (error) {\n", "        console.error('Failed to fetch user data:', error);\n", "        throw error; // Re-throw to let caller handle\n", "    }\n", "}\n", "\n", "// Code organization\n", "console.log(\"\\n=== Code Organization ===\");\n", "\n", "// Use modules to organize code\n", "// utils.js\n", "const utils = {\n", "    formatCurrency(amount, currency = 'USD') {\n", "        return new Intl.NumberFormat('en-US', {\n", "            style: 'currency',\n", "            currency\n", "        }).format(amount);\n", "    },\n", "    \n", "    debounce(func, wait) {\n", "        let timeout;\n", "        return function executedFunction(...args) {\n", "            const later = () => {\n", "                clearTimeout(timeout);\n", "                func(...args);\n", "            };\n", "            clearTimeout(timeout);\n", "            timeout = setTimeout(later, wait);\n", "        };\n", "    }\n", "};\n", "\n", "console.log('Formatted currency:', utils.formatCurrency(1234.56));\n", "\n", "// Performance tips\n", "console.log(\"\\n=== Performance Tips ===\");\n", "\n", "// Use object lookup instead of multiple if/else\n", "const statusMessages = {\n", "    200: 'Success',\n", "    404: 'Not Found',\n", "    500: 'Server Error'\n", "};\n", "\n", "function getStatusMessage(code) {\n", "    return statusMessages[code] || 'Unknown Status';\n", "}\n", "\n", "// Use Set for unique values\n", "const uniqueIds = new Set([1, 2, 3, 2, 1, 4]);\n", "console.log('Unique IDs:', Array.from(uniqueIds));\n", "\n", "// Use Map for key-value pairs with non-string keys\n", "const userPreferences = new Map();\n", "userPreferences.set(user, { theme: 'dark', language: 'en' });\n", "\n", "console.log('\\nJavaScript Manual Complete!');\n", "console.log('This manual covers the essential JavaScript concepts.');\n", "console.log('For more advanced topics, consider exploring:');\n", "console.log('- Web APIs (Fetch, WebSockets, Service Workers)');\n", "console.log('- Testing frameworks (<PERSON><PERSON>, <PERSON><PERSON>, Cypress)');\n", "console.log('- Build tools (Webpack, Vite, Rollup)');\n", "console.log('- Frameworks (<PERSON><PERSON>, <PERSON>ue, <PERSON><PERSON>)');\n", "console.log('- TypeScript for type safety');\n", "console.log('- Node.js for server-side development');"]}, {"cell_type": "markdown", "id": "ded1ffb5", "metadata": {}, "source": ["# Modules and Imports/Exports {#modules}\n", "\n", "JavaScript modules allow you to organize code into reusable components. ES6 modules provide a standardized way to import and export functionality between files."]}, {"cell_type": "code", "execution_count": null, "id": "bdaf0636", "metadata": {}, "outputs": [], "source": ["// ES6 Module Syntax Examples\n", "console.log(\"=== ES6 Modules ===\");\n", "\n", "// Note: These examples show module syntax but won't run in this notebook\n", "// They would work in separate .js files or with a module bundler\n", "\n", "// ===== math-utils.js =====\n", "/*\n", "// Named exports\n", "export function add(a, b) {\n", "    return a + b;\n", "}\n", "\n", "export function subtract(a, b) {\n", "    return a - b;\n", "}\n", "\n", "export const PI = 3.14159;\n", "\n", "// Export multiple items\n", "function multiply(a, b) {\n", "    return a * b;\n", "}\n", "\n", "function divide(a, b) {\n", "    if (b === 0) throw new Error('Division by zero');\n", "    return a / b;\n", "}\n", "\n", "export { multiply, divide };\n", "\n", "// Default export\n", "export default class Calculator {\n", "    constructor() {\n", "        this.result = 0;\n", "    }\n", "    \n", "    add(value) {\n", "        this.result += value;\n", "        return this;\n", "    }\n", "    \n", "    multiply(value) {\n", "        this.result *= value;\n", "        return this;\n", "    }\n", "    \n", "    getResult() {\n", "        return this.result;\n", "    }\n", "}\n", "*/\n", "\n", "// ===== main.js =====\n", "/*\n", "// Import named exports\n", "import { add, subtract, PI } from './math-utils.js';\n", "\n", "// Import with alias\n", "import { multiply as mult, divide as div } from './math-utils.js';\n", "\n", "// Import default export\n", "import Calculator from './math-utils.js';\n", "\n", "// Import everything as namespace\n", "import * as MathUtils from './math-utils.js';\n", "\n", "// Mixed imports\n", "import Calculator, { add, PI } from './math-utils.js';\n", "\n", "// Using imports\n", "console.log(add(5, 3));\n", "console.log(mult(4, 2));\n", "console.log(PI);\n", "\n", "const calc = new Calculator();\n", "const result = calc.add(10).multiply(2).getResult();\n", "console.log(result);\n", "\n", "// Using namespace import\n", "console.log(MathUtils.add(1, 2));\n", "console.log(MathUtils.PI);\n", "*/\n", "\n", "// Dynamic imports (ES2020)\n", "console.log(\"\\n=== Dynamic Imports ===\");\n", "\n", "/*\n", "// Dynamic import returns a Promise\n", "async function loadMathUtils() {\n", "    try {\n", "        const mathModule = await import('./math-utils.js');\n", "        \n", "        // Use the imported module\n", "        console.log(mathModule.add(5, 3));\n", "        \n", "        const Calculator = mathModule.default;\n", "        const calc = new Calculator();\n", "        console.log(calc.add(10).getResult());\n", "        \n", "    } catch (error) {\n", "        console.error('Failed to load module:', error);\n", "    }\n", "}\n", "\n", "// Conditional loading\n", "if (someCondition) {\n", "    import('./heavy-module.js')\n", "        .then(module => {\n", "            module.doSomething();\n", "        })\n", "        .catch(error => {\n", "            console.error('Module loading failed:', error);\n", "        });\n", "}\n", "*/\n", "\n", "// CommonJS (Node.js) - for comparison\n", "console.log(\"\\n=== CommonJS (Node.js) ===\");\n", "\n", "// Simulating CommonJS exports/requires\n", "const moduleSimulation = {\n", "    // ===== math-utils.js (CommonJS) =====\n", "    mathUtils: {\n", "        add: function(a, b) { return a + b; },\n", "        subtract: function(a, b) { return a - b; },\n", "        PI: 3.14159\n", "    },\n", "    \n", "    // ===== calculator.js (CommonJS) =====\n", "    Calculator: class {\n", "        constructor() {\n", "            this.result = 0;\n", "        }\n", "        \n", "        add(value) {\n", "            this.result += value;\n", "            return this;\n", "        }\n", "        \n", "        getResult() {\n", "            return this.result;\n", "        }\n", "    }\n", "};\n", "\n", "/*\n", "// CommonJS export patterns\n", "// Single export\n", "module.exports = Calculator;\n", "\n", "// Multiple exports\n", "module.exports = {\n", "    add,\n", "    subtract,\n", "    PI\n", "};\n", "\n", "// Individual exports\n", "exports.add = add;\n", "exports.PI = PI;\n", "\n", "// CommonJS require\n", "const { add, subtract } = require('./math-utils');\n", "const Calculator = require('./calculator');\n", "const mathUtils = require('./math-utils');\n", "*/\n", "\n", "// Using the simulated modules\n", "const { add, subtract, PI } = moduleSimulation.mathUtils;\n", "const Calculator = moduleSimulation.Calculator;\n", "\n", "console.log(`Add: ${add(5, 3)}`);\n", "console.log(`Subtract: ${subtract(10, 4)}`);\n", "console.log(`PI: ${PI}`);\n", "\n", "const calc = new Calculator();\n", "console.log(`Calculator result: ${calc.add(15).add(5).getResult()}`);\n", "\n", "// Module patterns and best practices\n", "console.log(\"\\n=== Module Best Practices ===\");\n", "\n", "// 1. Use descriptive names\n", "// Good: import { validateEmail } from './validators.js';\n", "// Bad: import { v } from './validators.js';\n", "\n", "// 2. Group related functionality\n", "const userUtils = {\n", "    validateEmail(email) {\n", "        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n", "    },\n", "    \n", "    formatName(firstName, lastName) {\n", "        return `${firstName} ${lastName}`.trim();\n", "    },\n", "    \n", "    generateUsername(email) {\n", "        return email.split('@')[0].toLowerCase();\n", "    }\n", "};\n", "\n", "// 3. Avoid circular dependencies\n", "// File A imports File B, File B imports File A = circular dependency\n", "\n", "// 4. Use barrel exports for clean imports\n", "/*\n", "// ===== index.js (barrel file) =====\n", "export { default as Calculator } from './calculator.js';\n", "export { add, subtract, multiply, divide } from './math-utils.js';\n", "export { validateEmail, formatName } from './user-utils.js';\n", "\n", "// Now you can import everything from one place:\n", "import { Calculator, add, validateEmail } from './utils/index.js';\n", "*/\n", "\n", "console.log('Email validation:', userUtils.validateEmail('<EMAIL>'));\n", "console.log('Formatted name:', userUtils.formatName('<PERSON>', '<PERSON><PERSON>'));\n", "console.log('Username:', userUtils.generateUsername('<EMAIL>'));"]}, {"cell_type": "markdown", "id": "bc9108b3", "metadata": {}, "source": ["# Closures and Scope {#closures}\n", "\n", "Closures are a fundamental concept in JavaScript where inner functions have access to variables from their outer scope even after the outer function has returned."]}, {"cell_type": "code", "execution_count": null, "id": "e5eb596d", "metadata": {}, "outputs": [], "source": ["// <PERSON>ope\n", "console.log(\"=== Scope in JavaScript ===\");\n", "\n", "// Global scope\n", "var globalVar = 'I am global';\n", "let globalLet = 'I am also global';\n", "const globalConst = 'I am global too';\n", "\n", "function demonstrateScope() {\n", "    // Function scope\n", "    var functionVar = 'I am function scoped';\n", "    \n", "    if (true) {\n", "        // Block scope\n", "        let blockLet = 'I am block scoped';\n", "        const blockConst = 'I am also block scoped';\n", "        var blockVar = 'I am function scoped, not block scoped';\n", "        \n", "        console.log('Inside block:', blockLet);\n", "        console.log('Inside block:', blockConst);\n", "    }\n", "    \n", "    console.log('Outside block:', blockVar); // Works - var is function scoped\n", "    // console.log(blockLet); // Error - let is block scoped\n", "    // console.log(blockConst); // Error - const is block scoped\n", "}\n", "\n", "demonstrateScope();\n", "\n", "// Basic Closures\n", "console.log(\"\\n=== Basic Closures ===\");\n", "\n", "function outerFunction(x) {\n", "    // Outer function's variable\n", "    const outerVariable = x;\n", "    \n", "    // Inner function has access to outer function's variables\n", "    function innerFunction(y) {\n", "        return outerVariable + y;\n", "    }\n", "    \n", "    return innerFunction;\n", "}\n", "\n", "const addFive = outerFunction(5);\n", "console.log('Closure result:', addFive(3)); // 8\n", "\n", "// The outer function has finished executing, but the inner function\n", "// still has access to outerVariable through closure\n", "\n", "// Practical Closure Examples\n", "console.log(\"\\n=== Practical Closures ===\");\n", "\n", "// 1. Counter function\n", "function createCounter() {\n", "    let count = 0;\n", "    \n", "    return {\n", "        increment() {\n", "            count++;\n", "            return count;\n", "        },\n", "        decrement() {\n", "            count--;\n", "            return count;\n", "        },\n", "        getCount() {\n", "            return count;\n", "        },\n", "        reset() {\n", "            count = 0;\n", "            return count;\n", "        }\n", "    };\n", "}\n", "\n", "const counter1 = createCounter();\n", "const counter2 = createCounter();\n", "\n", "console.log('Counter 1:', counter1.increment()); // 1\n", "console.log('Counter 1:', counter1.increment()); // 2\n", "console.log('Counter 2:', counter2.increment()); // 1 (independent)\n", "console.log('Counter 1 current:', counter1.getCount()); // 2\n", "\n", "// 2. Function factory\n", "function createMultiplier(multiplier) {\n", "    return function(number) {\n", "        return number * multiplier;\n", "    };\n", "}\n", "\n", "const double = createMultiplier(2);\n", "const triple = createMultiplier(3);\n", "const quadruple = createMultiplier(4);\n", "\n", "console.log('\\nMultipliers:');\n", "console.log('Double 5:', double(5)); // 10\n", "console.log('Triple 5:', triple(5)); // 15\n", "console.log('Quadruple 5:', quadruple(5)); // 20\n", "\n", "// 3. Private variables pattern\n", "function createBankAccount(initialBalance) {\n", "    let balance = initialBalance;\n", "    let transactionHistory = [];\n", "    \n", "    function addTransaction(type, amount) {\n", "        transactionHistory.push({\n", "            type,\n", "            amount,\n", "            date: new Date(),\n", "            balance: balance\n", "        });\n", "    }\n", "    \n", "    return {\n", "        deposit(amount) {\n", "            if (amount > 0) {\n", "                balance += amount;\n", "                addTransaction('deposit', amount);\n", "                return balance;\n", "            }\n", "            throw new Error('Deposit amount must be positive');\n", "        },\n", "        \n", "        withdraw(amount) {\n", "            if (amount > 0 && amount <= balance) {\n", "                balance -= amount;\n", "                addTransaction('withdrawal', amount);\n", "                return balance;\n", "            }\n", "            throw new Error('Invalid withdrawal amount');\n", "        },\n", "        \n", "        getBalance() {\n", "            return balance;\n", "        },\n", "        \n", "        getTransactionHistory() {\n", "            return [...transactionHistory]; // Return copy to prevent mutation\n", "        }\n", "    };\n", "}\n", "\n", "const account = createBankAccount(1000);\n", "console.log('\\nBank Account:');\n", "console.log('Initial balance:', account.getBalance());\n", "console.log('After deposit 500:', account.deposit(500));\n", "console.log('After withdrawal 200:', account.withdraw(200));\n", "console.log('Transaction count:', account.getTransactionHistory().length);\n", "\n", "// Common Closure Pitfalls\n", "console.log(\"\\n=== Closure Pitfalls ===\");\n", "\n", "// Problem: Loop with var\n", "console.log('Problem with var in loops:');\n", "for (var i = 0; i < 3; i++) {\n", "    setTimeout(() => {\n", "        console.log('var i:', i); // Will print 3, 3, 3\n", "    }, 100);\n", "}\n", "\n", "// Solution 1: Use let\n", "console.log('\\nSolution with let:');\n", "for (let j = 0; j < 3; j++) {\n", "    setTimeout(() => {\n", "        console.log('let j:', j); // Will print 0, 1, 2\n", "    }, 200);\n", "}\n", "\n", "// Solution 2: IIFE (Immediately Invoked Function Expression)\n", "console.log('\\nSolution with IIFE:');\n", "for (var k = 0; k < 3; k++) {\n", "    (function(index) {\n", "        setTimeout(() => {\n", "            console.log('IIFE index:', index); // Will print 0, 1, 2\n", "        }, 300);\n", "    })(k);\n", "}\n", "\n", "// Advanced Closure Patterns\n", "console.log(\"\\n=== Advanced Patterns ===\");\n", "\n", "// Module pattern\n", "const Calculator = (function() {\n", "    let result = 0;\n", "    \n", "    return {\n", "        add(x) {\n", "            result += x;\n", "            return this;\n", "        },\n", "        subtract(x) {\n", "            result -= x;\n", "            return this;\n", "        },\n", "        multiply(x) {\n", "            result *= x;\n", "            return this;\n", "        },\n", "        getResult() {\n", "            return result;\n", "        },\n", "        reset() {\n", "            result = 0;\n", "            return this;\n", "        }\n", "    };\n", "})();\n", "\n", "const calcResult = Calculator\n", "    .add(10)\n", "    .multiply(2)\n", "    .subtract(5)\n", "    .getResult();\n", "\n", "console.log('Calculator result:', calcResult); // 15\n", "\n", "// Memoization using closures\n", "function memoize(fn) {\n", "    const cache = new Map();\n", "    \n", "    return function(...args) {\n", "        const key = JSON.stringify(args);\n", "        \n", "        if (cache.has(key)) {\n", "            console.log('<PERSON><PERSON> hit for:', args);\n", "            return cache.get(key);\n", "        }\n", "        \n", "        console.log('Computing for:', args);\n", "        const result = fn.apply(this, args);\n", "        cache.set(key, result);\n", "        return result;\n", "    };\n", "}\n", "\n", "const expensiveFunction = memoize(function(n) {\n", "    // Simulate expensive computation\n", "    let result = 0;\n", "    for (let i = 0; i < n; i++) {\n", "        result += i;\n", "    }\n", "    return result;\n", "});\n", "\n", "console.log('\\nMemoization example:');\n", "console.log('First call:', expensiveFunction(1000));\n", "console.log('Second call:', expensiveFunction(1000)); // Uses cache\n", "console.log('Third call:', expensiveFunction(2000)); // New computation"]}, {"cell_type": "markdown", "id": "b1d7dad7", "metadata": {}, "source": ["# Event Loop and Concurrency {#event-loop}\n", "\n", "Understanding the JavaScript event loop is crucial for writing efficient asynchronous code. JavaScript is single-threaded but can handle concurrent operations through the event loop."]}, {"cell_type": "code", "execution_count": null, "id": "6dbdc6d8", "metadata": {}, "outputs": [], "source": ["// Understanding the Call Stack\n", "console.log(\"=== Call Stack ===\");\n", "\n", "function first() {\n", "    console.log('First function');\n", "    second();\n", "    console.log('First function end');\n", "}\n", "\n", "function second() {\n", "    console.log('Second function');\n", "    third();\n", "    console.log('Second function end');\n", "}\n", "\n", "function third() {\n", "    console.log('Third function');\n", "}\n", "\n", "console.log('Start');\n", "first();\n", "console.log('End');\n", "\n", "// Execution order demonstrates the call stack:\n", "// Start -> First -> Second -> Third -> Second end -> First end -> End\n", "\n", "// Asynchronous Execution\n", "console.log(\"\\n=== Asynchronous Execution ===\");\n", "\n", "console.log('Synchronous 1');\n", "\n", "setTimeout(() => {\n", "    console.log('Timeout 1 (0ms)');\n", "}, 0);\n", "\n", "setTimeout(() => {\n", "    console.log('Timeout 2 (100ms)');\n", "}, 100);\n", "\n", "console.log('Synchronous 2');\n", "\n", "// Even with 0ms timeout, async code runs after synchronous code\n", "// Output: Synchronous 1 -> Synchronous 2 -> Timeout 1 -> Timeout 2\n", "\n", "// Microtasks vs Macrotasks\n", "console.log(\"\\n=== Microtasks vs Macrotasks ===\");\n", "\n", "console.log('Start');\n", "\n", "// <PERSON><PERSON><PERSON> (Timer)\n", "setTimeout(() => {\n", "    console.log('Macrotask: setTimeout');\n", "}, 0);\n", "\n", "// Microtask (Promise)\n", "Promise.resolve().then(() => {\n", "    console.log('Microtask: Promise.then');\n", "});\n", "\n", "// Microtask (queueMicrotask)\n", "queueMicrotask(() => {\n", "    console.log('Microtask: queueMicrotask');\n", "});\n", "\n", "console.log('End');\n", "\n", "// Execution order: Start -> End -> Microtasks -> Macrotasks\n", "// Microtasks have higher priority than macrotasks\n", "\n", "// Complex Event Loop Example\n", "console.log(\"\\n=== Complex Event Loop ===\");\n", "\n", "function demonstrateEventLoop() {\n", "    console.log('1: Start');\n", "    \n", "    setTimeout(() => {\n", "        console.log('2: setTimeout 1');\n", "        Promise.resolve().then(() => {\n", "            console.log('3: Promise in setTimeout');\n", "        });\n", "    }, 0);\n", "    \n", "    Promise.resolve().then(() => {\n", "        console.log('4: Promise 1');\n", "        setTimeout(() => {\n", "            console.log('5: setTimeout in Promise');\n", "        }, 0);\n", "    });\n", "    \n", "    setTimeout(() => {\n", "        console.log('6: setTimeout 2');\n", "    }, 0);\n", "    \n", "    Promise.resolve().then(() => {\n", "        console.log('7: Promise 2');\n", "    });\n", "    \n", "    console.log('8: End');\n", "}\n", "\n", "demonstrateEventLoop();\n", "\n", "// Simulating Concurrent Operations\n", "console.log(\"\\n=== Simulating Concurrency ===\");\n", "\n", "// Simulating multiple API calls\n", "function simulateApiCall(name, delay) {\n", "    return new Promise(resolve => {\n", "        setTimeout(() => {\n", "            console.log(`API call ${name} completed`);\n", "            resolve(`Data from ${name}`);\n", "        }, delay);\n", "    });\n", "}\n", "\n", "// Sequential execution (slower)\n", "async function sequentialExecution() {\n", "    console.log('\\nSequential execution started');\n", "    const start = Date.now();\n", "    \n", "    const result1 = await simulateApiCall('API-1', 200);\n", "    const result2 = await simulateApiCall('API-2', 300);\n", "    const result3 = await simulateApiCall('API-3', 100);\n", "    \n", "    const end = Date.now();\n", "    console.log(`Sequential completed in ${end - start}ms`);\n", "    return [result1, result2, result3];\n", "}\n", "\n", "// Parallel execution (faster)\n", "async function parallelExecution() {\n", "    console.log('\\nParallel execution started');\n", "    const start = Date.now();\n", "    \n", "    const [result1, result2, result3] = await Promise.all([\n", "        simulateApiCall('API-A', 200),\n", "        simulateApiCall('API-B', 300),\n", "        simulateApiCall('API-C', 100)\n", "    ]);\n", "    \n", "    const end = Date.now();\n", "    console.log(`<PERSON>lle<PERSON> completed in ${end - start}ms`);\n", "    return [result1, result2, result3];\n", "}\n", "\n", "// Run both examples\n", "sequentialExecution().then(() => {\n", "    return parallelExecution();\n", "});\n", "\n", "// Non-blocking Operations\n", "console.log(\"\\n=== Non-blocking Operations ===\");\n", "\n", "// Blocking operation (avoid this)\n", "function blockingOperation() {\n", "    console.log('Blocking operation started');\n", "    const start = Date.now();\n", "    \n", "    // Simulate heavy computation\n", "    while (Date.now() - start < 1000) {\n", "        // Busy waiting - blocks the event loop\n", "    }\n", "    \n", "    console.log('Blocking operation completed');\n", "}\n", "\n", "// Non-blocking alternative\n", "function nonBlockingOperation() {\n", "    console.log('Non-blocking operation started');\n", "    \n", "    return new Promise(resolve => {\n", "        const start = Date.now();\n", "        \n", "        function processChunk() {\n", "            const chunkStart = Date.now();\n", "            \n", "            // Process for small chunks of time\n", "            while (Date.now() - chunkStart < 10) {\n", "                // Do some work\n", "            }\n", "            \n", "            if (Date.now() - start < 1000) {\n", "                // Schedule next chunk\n", "                setTimeout(processChunk, 0);\n", "            } else {\n", "                console.log('Non-blocking operation completed');\n", "                resolve();\n", "            }\n", "        }\n", "        \n", "        processChunk();\n", "    });\n", "}\n", "\n", "// Worker Threads Concept (Node.js)\n", "console.log(\"\\n=== Worker Threads Concept ===\");\n", "\n", "// In Node.js, you can use worker threads for CPU-intensive tasks\n", "/*\n", "// main.js\n", "const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');\n", "\n", "if (isMainThread) {\n", "    // Main thread\n", "    const worker = new Worker(__filename, {\n", "        workerData: { numbers: [1, 2, 3, 4, 5] }\n", "    });\n", "    \n", "    worker.on('message', (result) => {\n", "        console.log('Result from worker:', result);\n", "    });\n", "    \n", "    worker.on('error', (error) => {\n", "        console.error('Worker error:', error);\n", "    });\n", "    \n", "} else {\n", "    // Worker thread\n", "    const { numbers } = workerData;\n", "    const result = numbers.reduce((sum, num) => sum + num, 0);\n", "    parentPort.postMessage(result);\n", "}\n", "*/\n", "\n", "// Event Loop Best Practices\n", "console.log(\"\\n=== Event Loop Best Practices ===\");\n", "\n", "// 1. Avoid blocking the event loop\n", "function goodAsyncFunction() {\n", "    return new Promise(resolve => {\n", "        // Use setTimeout to yield control\n", "        setTimeout(() => {\n", "            resolve('Non-blocking result');\n", "        }, 0);\n", "    });\n", "}\n", "\n", "// 2. Use Promise.all for parallel operations\n", "async function efficientDataFetching() {\n", "    const promises = [\n", "        fetch('/api/users'),\n", "        fetch('/api/posts'),\n", "        fetch('/api/comments')\n", "    ];\n", "    \n", "    try {\n", "        const results = await Promise.all(promises);\n", "        return results;\n", "    } catch (error) {\n", "        console.error('One or more requests failed:', error);\n", "    }\n", "}\n", "\n", "// 3. <PERSON><PERSON> errors properly in async code\n", "async function robustAsyncFunction() {\n", "    try {\n", "        const result = await someAsyncOperation();\n", "        return result;\n", "    } catch (error) {\n", "        console.error('Async operation failed:', error);\n", "        throw error; // Re-throw if needed\n", "    }\n", "}\n", "\n", "function someAsyncOperation() {\n", "    return Promise.resolve('Success');\n", "}\n", "\n", "console.log('Event loop concepts demonstrated!');"]}, {"cell_type": "markdown", "id": "ded1ffb6", "metadata": {}, "source": ["# Web APIs and Fetch {#web-apis}\n", "\n", "Web APIs provide powerful functionality for interacting with servers, handling data, and creating rich web applications. The Fetch API is the modern way to make HTTP requests."]}, {"cell_type": "code", "execution_count": null, "id": "bdaf0637", "metadata": {}, "outputs": [], "source": ["// Fetch API Basics\n", "console.log(\"=== Fetch API ===\");\n", "\n", "// Note: These examples show fetch syntax but may not work in all environments\n", "// They work in browsers and Node.js 18+ (with --experimental-fetch flag)\n", "\n", "// Basic GET request\n", "async function basicFetch() {\n", "    try {\n", "        const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');\n", "        \n", "        // Check if request was successful\n", "        if (!response.ok) {\n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "        }\n", "        \n", "        const data = await response.json();\n", "        console.log('Fetched data:', data);\n", "        return data;\n", "        \n", "    } catch (error) {\n", "        console.error('Fetch error:', error.message);\n", "        throw error;\n", "    }\n", "}\n", "\n", "// POST request with JSON data\n", "async function postData() {\n", "    const postData = {\n", "        title: 'New Post',\n", "        body: 'This is the content of the new post',\n", "        userId: 1\n", "    };\n", "    \n", "    try {\n", "        const response = await fetch('https://jsonplaceholder.typicode.com/posts', {\n", "            method: 'POST',\n", "            headers: {\n", "                'Content-Type': 'application/json',\n", "                'Authorization': 'Bearer your-token-here'\n", "            },\n", "            body: JSON.stringify(postData)\n", "        });\n", "        \n", "        if (!response.ok) {\n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "        }\n", "        \n", "        const result = await response.json();\n", "        console.log('Posted data:', result);\n", "        return result;\n", "        \n", "    } catch (error) {\n", "        console.error('POST error:', error.message);\n", "        throw error;\n", "    }\n", "}\n", "\n", "// PUT request (update)\n", "async function updateData(id, updateData) {\n", "    try {\n", "        const response = await fetch(`https://jsonplaceholder.typicode.com/posts/${id}`, {\n", "            method: 'PUT',\n", "            headers: {\n", "                'Content-Type': 'application/json'\n", "            },\n", "            body: JSON.stringify(updateData)\n", "        });\n", "        \n", "        if (!response.ok) {\n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "        }\n", "        \n", "        const result = await response.json();\n", "        console.log('Updated data:', result);\n", "        return result;\n", "        \n", "    } catch (error) {\n", "        console.error('PUT error:', error.message);\n", "        throw error;\n", "    }\n", "}\n", "\n", "// DELETE request\n", "async function deleteData(id) {\n", "    try {\n", "        const response = await fetch(`https://jsonplaceholder.typicode.com/posts/${id}`, {\n", "            method: 'DELETE'\n", "        });\n", "        \n", "        if (!response.ok) {\n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "        }\n", "        \n", "        console.log('Data deleted successfully');\n", "        return true;\n", "        \n", "    } catch (error) {\n", "        console.error('DELETE error:', error.message);\n", "        throw error;\n", "    }\n", "}\n", "\n", "// Advanced Fetch Options\n", "console.log(\"\\n=== Advanced Fetch ===\");\n", "\n", "// Fetch with timeout\n", "async function fetchWithTimeout(url, options = {}, timeout = 5000) {\n", "    const controller = new AbortController();\n", "    const timeoutId = setTimeout(() => controller.abort(), timeout);\n", "    \n", "    try {\n", "        const response = await fetch(url, {\n", "            ...options,\n", "            signal: controller.signal\n", "        });\n", "        \n", "        clearTimeout(timeoutId);\n", "        return response;\n", "        \n", "    } catch (error) {\n", "        clearTimeout(timeoutId);\n", "        if (error.name === 'AbortError') {\n", "            throw new Error('Request timed out');\n", "        }\n", "        throw error;\n", "    }\n", "}\n", "\n", "// Fetch with retry logic\n", "async function fetchWithRetry(url, options = {}, maxRetries = 3) {\n", "    let lastError;\n", "    \n", "    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n", "        try {\n", "            console.log(`Attempt ${attempt} of ${maxRetries}`);\n", "            const response = await fetch(url, options);\n", "            \n", "            if (response.ok) {\n", "                return response;\n", "            }\n", "            \n", "            // If it's a server error (5xx), retry\n", "            if (response.status >= 500 && attempt < maxRetries) {\n", "                console.log(`Server error ${response.status}, retrying...`);\n", "                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));\n", "                continue;\n", "            }\n", "            \n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "            \n", "        } catch (error) {\n", "            lastError = error;\n", "            \n", "            if (attempt < maxRetries) {\n", "                console.log(`Request failed, retrying in ${attempt} seconds...`);\n", "                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));\n", "            }\n", "        }\n", "    }\n", "    \n", "    throw lastError;\n", "}\n", "\n", "// File upload with FormData\n", "async function uploadFile(file) {\n", "    const formData = new FormData();\n", "    formData.append('file', file);\n", "    formData.append('description', 'Uploaded file');\n", "    \n", "    try {\n", "        const response = await fetch('/api/upload', {\n", "            method: 'POST',\n", "            body: formData // Don't set Content-Type header, let browser set it\n", "        });\n", "        \n", "        if (!response.ok) {\n", "            throw new Error(`Upload failed: ${response.status}`);\n", "        }\n", "        \n", "        const result = await response.json();\n", "        console.log('File uploaded:', result);\n", "        return result;\n", "        \n", "    } catch (error) {\n", "        console.error('Upload error:', error.message);\n", "        throw error;\n", "    }\n", "}\n", "\n", "// Response handling\n", "console.log(\"\\n=== Response Handling ===\");\n", "\n", "async function handleDifferentResponseTypes(url) {\n", "    try {\n", "        const response = await fetch(url);\n", "        \n", "        if (!response.ok) {\n", "            throw new Error(`HTTP error! status: ${response.status}`);\n", "        }\n", "        \n", "        // Check content type\n", "        const contentType = response.headers.get('content-type');\n", "        \n", "        if (contentType?.includes('application/json')) {\n", "            const data = await response.json();\n", "            console.log('JSON data:', data);\n", "            return data;\n", "        }\n", "        \n", "        if (contentType?.includes('text/')) {\n", "            const text = await response.text();\n", "            console.log('Text data:', text);\n", "            return text;\n", "        }\n", "        \n", "        if (contentType?.includes('image/')) {\n", "            const blob = await response.blob();\n", "            console.log('Image blob:', blob);\n", "            return blob;\n", "        }\n", "        \n", "        // Default to array buffer\n", "        const buffer = await response.arrayBuffer();\n", "        console.log('Array buffer:', buffer);\n", "        return buffer;\n", "        \n", "    } catch (error) {\n", "        console.error('Response handling error:', error.message);\n", "        throw error;\n", "    }\n", "}\n", "\n", "// API Client Class\n", "console.log(\"\\n=== API Client Class ===\");\n", "\n", "class ApiClient {\n", "    constructor(baseURL, defaultHeaders = {}) {\n", "        this.baseURL = baseURL;\n", "        this.defaultHeaders = {\n", "            'Content-Type': 'application/json',\n", "            ...defaultHeaders\n", "        };\n", "    }\n", "    \n", "    async request(endpoint, options = {}) {\n", "        const url = `${this.baseURL}${endpoint}`;\n", "        const config = {\n", "            headers: {\n", "                ...this.defaultHeaders,\n", "                ...options.headers\n", "            },\n", "            ...options\n", "        };\n", "        \n", "        try {\n", "            const response = await fetch(url, config);\n", "            \n", "            if (!response.ok) {\n", "                throw new Error(`HTTP error! status: ${response.status}`);\n", "            }\n", "            \n", "            // Try to parse as <PERSON><PERSON><PERSON>, fallback to text\n", "            const contentType = response.headers.get('content-type');\n", "            if (contentType?.includes('application/json')) {\n", "                return await response.json();\n", "            }\n", "            \n", "            return await response.text();\n", "            \n", "        } catch (error) {\n", "            console.error(`API request failed: ${error.message}`);\n", "            throw error;\n", "        }\n", "    }\n", "    \n", "    async get(endpoint, params = {}) {\n", "        const queryString = new URLSearchParams(params).toString();\n", "        const url = queryString ? `${endpoint}?${queryString}` : endpoint;\n", "        return this.request(url, { method: 'GET' });\n", "    }\n", "    \n", "    async post(endpoint, data) {\n", "        return this.request(endpoint, {\n", "            method: 'POST',\n", "            body: JSON.stringify(data)\n", "        });\n", "    }\n", "    \n", "    async put(endpoint, data) {\n", "        return this.request(endpoint, {\n", "            method: 'PUT',\n", "            body: JSON.stringify(data)\n", "        });\n", "    }\n", "    \n", "    async delete(endpoint) {\n", "        return this.request(endpoint, { method: 'DELETE' });\n", "    }\n", "    \n", "    setAuthToken(token) {\n", "        this.defaultHeaders['Authorization'] = `Bearer ${token}`;\n", "    }\n", "}\n", "\n", "// Usage example\n", "const api = new ApiClient('https://jsonplaceholder.typicode.com');\n", "\n", "// Example usage (commented out to avoid actual network calls)\n", "/*\n", "async function demonstrateApiClient() {\n", "    try {\n", "        // GET request\n", "        const posts = await api.get('/posts', { userId: 1 });\n", "        console.log('Posts:', posts);\n", "        \n", "        // POST request\n", "        const newPost = await api.post('/posts', {\n", "            title: 'New Post',\n", "            body: 'Post content',\n", "            userId: 1\n", "        });\n", "        console.log('New post:', newPost);\n", "        \n", "        // PUT request\n", "        const updatedPost = await api.put('/posts/1', {\n", "            id: 1,\n", "            title: 'Updated Post',\n", "            body: 'Updated content',\n", "            userId: 1\n", "        });\n", "        console.log('Updated post:', updatedPost);\n", "        \n", "        // DELETE request\n", "        await api.delete('/posts/1');\n", "        console.log('Post deleted');\n", "        \n", "    } catch (error) {\n", "        console.error('API demonstration failed:', error.message);\n", "    }\n", "}\n", "*/\n", "\n", "console.log('API Client created successfully');\n", "console.log('Web APIs and Fetch concepts demonstrated!');"]}, {"cell_type": "markdown", "id": "bc9108b4", "metadata": {}, "source": ["# Local Storage and Cookies {#storage}\n", "\n", "Web storage APIs allow you to store data in the browser. This includes localStorage, sessionStorage, and cookies for different persistence needs."]}, {"cell_type": "code", "execution_count": null, "id": "e5eb596e", "metadata": {}, "outputs": [], "source": ["// Local Storage\n", "console.log(\"=== Local Storage ===\");\n", "\n", "// Note: These examples work in browser environments\n", "// For Node.js, you would need a polyfill or alternative storage\n", "\n", "// Simulating localStorage for demonstration\n", "const mockLocalStorage = {\n", "    storage: new Map(),\n", "    \n", "    setItem(key, value) {\n", "        this.storage.set(key, String(value));\n", "        console.log(`localStorage.setItem('${key}', '${value}')`);\n", "    },\n", "    \n", "    getItem(key) {\n", "        const value = this.storage.get(key) || null;\n", "        console.log(`localStorage.getItem('${key}') -> ${value}`);\n", "        return value;\n", "    },\n", "    \n", "    removeItem(key) {\n", "        this.storage.delete(key);\n", "        console.log(`localStorage.removeItem('${key}')`);\n", "    },\n", "    \n", "    clear() {\n", "        this.storage.clear();\n", "        console.log('localStorage.clear()');\n", "    },\n", "    \n", "    get length() {\n", "        return this.storage.size;\n", "    },\n", "    \n", "    key(index) {\n", "        const keys = Array.from(this.storage.keys());\n", "        return keys[index] || null;\n", "    }\n", "};\n", "\n", "// Basic localStorage operations\n", "mockLocalStorage.setItem('username', 'john_doe');\n", "mockLocalStorage.setItem('theme', 'dark');\n", "mockLocalStorage.setItem('language', 'en');\n", "\n", "// Retrieving data\n", "const username = mockLocalStorage.getItem('username');\n", "const theme = mockLocalStorage.getItem('theme');\n", "\n", "console.log(`Retrieved username: ${username}`);\n", "console.log(`Retrieved theme: ${theme}`);\n", "\n", "// Storing objects (must be serialized)\n", "const userPreferences = {\n", "    theme: 'dark',\n", "    language: 'en',\n", "    notifications: true,\n", "    autoSave: false\n", "};\n", "\n", "mockLocalStorage.setItem('userPreferences', JSON.stringify(userPreferences));\n", "\n", "// Retrieving and parsing objects\n", "const storedPreferences = mockLocalStorage.getItem('userPreferences');\n", "const parsedPreferences = storedPreferences ? JSON.parse(storedPreferences) : null;\n", "console.log('Parsed preferences:', parsedPreferences);\n", "\n", "// Storage utility functions\n", "console.log(\"\\n=== Storage Utilities ===\");\n", "\n", "class StorageManager {\n", "    constructor(storage = mockLocalStorage) {\n", "        this.storage = storage;\n", "    }\n", "    \n", "    // Set item with optional expiration\n", "    setItem(key, value, expirationMinutes = null) {\n", "        const item = {\n", "            value: value,\n", "            timestamp: Date.now(),\n", "            expiration: expirationMinutes ? Date.now() + (expirationMinutes * 60 * 1000) : null\n", "        };\n", "        \n", "        this.storage.setItem(key, JSON.stringify(item));\n", "    }\n", "    \n", "    // Get item with expiration check\n", "    getItem(key) {\n", "        const itemStr = this.storage.getItem(key);\n", "        if (!itemStr) return null;\n", "        \n", "        try {\n", "            const item = JSON.parse(itemStr);\n", "            \n", "            // Check if item has expired\n", "            if (item.expiration && Date.now() > item.expiration) {\n", "                this.storage.removeItem(key);\n", "                return null;\n", "            }\n", "            \n", "            return item.value;\n", "        } catch (error) {\n", "            console.error('Error parsing stored item:', error);\n", "            return null;\n", "        }\n", "    }\n", "    \n", "    // Remove item\n", "    removeItem(key) {\n", "        this.storage.removeItem(key);\n", "    }\n", "    \n", "    // Clear all items\n", "    clear() {\n", "        this.storage.clear();\n", "    }\n", "    \n", "    // Get all keys\n", "    getAllKeys() {\n", "        const keys = [];\n", "        for (let i = 0; i < this.storage.length; i++) {\n", "            keys.push(this.storage.key(i));\n", "        }\n", "        return keys;\n", "    }\n", "    \n", "    // Get storage size (approximate)\n", "    getStorageSize() {\n", "        let total = 0;\n", "        for (let key in this.storage.storage) {\n", "            if (this.storage.storage.hasOwnProperty(key)) {\n", "                total += this.storage.storage.get(key).length + key.length;\n", "            }\n", "        }\n", "        return total;\n", "    }\n", "}\n", "\n", "// Using the storage manager\n", "const storageManager = new StorageManager();\n", "\n", "// Store item with 5-minute expiration\n", "storageManager.setItem('tempData', { message: 'This will expire' }, 5);\n", "\n", "// Store permanent item\n", "storageManager.setItem('permanentData', { message: 'This will not expire' });\n", "\n", "console.log('Temp data:', storageManager.getItem('tempData'));\n", "console.log('Permanent data:', storageManager.getItem('permanentData'));\n", "\n", "// Session Storage (similar to localStorage but session-scoped)\n", "console.log(\"\\n=== Session Storage ===\");\n", "\n", "// sessionStorage works the same way as localStorage\n", "// but data is cleared when the tab is closed\n", "\n", "/*\n", "// Browser example:\n", "sessionStorage.setItem('sessionData', 'This persists only for the session');\n", "const sessionData = sessionStorage.getItem('sessionData');\n", "sessionStorage.removeItem('sessionData');\n", "*/\n", "\n", "// Cookies\n", "console.log(\"\\n=== Cookies ===\");\n", "\n", "// Cookie utility class\n", "class CookieManager {\n", "    // Set cookie\n", "    static setCookie(name, value, days = 7, path = '/', secure = false, sameSite = 'Lax') {\n", "        let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;\n", "        \n", "        if (days) {\n", "            const date = new Date();\n", "            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));\n", "            cookieString += `; expires=${date.toUTCString()}`;\n", "        }\n", "        \n", "        cookieString += `; path=${path}`;\n", "        \n", "        if (secure) {\n", "            cookieString += '; secure';\n", "        }\n", "        \n", "        cookieString += `; samesite=${sameSite}`;\n", "        \n", "        // In browser: document.cookie = cookieString;\n", "        console.log(`Setting cookie: ${cookieString}`);\n", "        \n", "        return cookieString;\n", "    }\n", "    \n", "    // Get cookie\n", "    static getCookie(name) {\n", "        // In browser environment:\n", "        /*\n", "        const nameEQ = encodeURIComponent(name) + '=';\n", "        const cookies = document.cookie.split(';');\n", "        \n", "        for (let cookie of cookies) {\n", "            cookie = cookie.trim();\n", "            if (cookie.indexOf(nameEQ) === 0) {\n", "                return decodeURIComponent(cookie.substring(nameEQ.length));\n", "            }\n", "        }\n", "        */\n", "        \n", "        console.log(`Getting cookie: ${name}`);\n", "        return null; // Simulated for demo\n", "    }\n", "    \n", "    // Delete cookie\n", "    static deleteCookie(name, path = '/') {\n", "        // Set cookie with past expiration date\n", "        const cookieString = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`;\n", "        \n", "        // In browser: document.cookie = cookieString;\n", "        console.log(`Deleting cookie: ${cookieString}`);\n", "        \n", "        return cookieString;\n", "    }\n", "    \n", "    // Get all cookies\n", "    static getAllCookies() {\n", "        // In browser environment:\n", "        /*\n", "        const cookies = {};\n", "        const cookieArray = document.cookie.split(';');\n", "        \n", "        for (let cookie of cookieArray) {\n", "            cookie = cookie.trim();\n", "            const [name, value] = cookie.split('=');\n", "            if (name && value) {\n", "                cookies[decodeURIComponent(name)] = decodeURIComponent(value);\n", "            }\n", "        }\n", "        \n", "        return cookies;\n", "        */\n", "        \n", "        console.log('Getting all cookies');\n", "        return {}; // Simulated for demo\n", "    }\n", "}\n", "\n", "// Using cookies\n", "CookieManager.setCookie('userToken', 'abc123', 30); // 30 days\n", "CookieManager.setCookie('preferences', JSON.stringify({ theme: 'dark' }), 365); // 1 year\n", "CookieManager.getCookie('userToken');\n", "CookieManager.deleteCookie('userToken');\n", "\n", "// Storage comparison and best practices\n", "console.log(\"\\n=== Storage Best Practices ===\");\n", "\n", "const storageComparison = {\n", "    localStorage: {\n", "        capacity: '5-10MB',\n", "        persistence: 'Until manually cleared',\n", "        scope: 'Origin (protocol + domain + port)',\n", "        accessibility: 'Client-side only',\n", "        useCase: 'User preferences, app state, cached data'\n", "    },\n", "    sessionStorage: {\n", "        capacity: '5-10MB',\n", "        persistence: 'Until tab is closed',\n", "        scope: 'Tab/window specific',\n", "        accessibility: 'Client-side only',\n", "        useCase: 'Temporary data, form data, session state'\n", "    },\n", "    cookies: {\n", "        capacity: '4KB per cookie',\n", "        persistence: 'Configurable expiration',\n", "        scope: 'Domain and path specific',\n", "        accessibility: 'Sent with HTTP requests',\n", "        useCase: 'Authentication tokens, tracking, small config data'\n", "    }\n", "};\n", "\n", "console.table(storageComparison);\n", "\n", "// Storage event handling (browser only)\n", "/*\n", "// Listen for storage changes in other tabs\n", "window.addEventListener('storage', (event) => {\n", "    console.log('Storage changed:', {\n", "        key: event.key,\n", "        oldValue: event.oldValue,\n", "        newValue: event.newValue,\n", "        url: event.url\n", "    });\n", "});\n", "*/\n", "\n", "console.log('Storage concepts demonstrated!');"]}, {"cell_type": "markdown", "id": "70e66b76", "metadata": {}, "source": ["# Testing (<PERSON><PERSON>, <PERSON><PERSON>) {#testing}\n", "\n", "Testing is crucial for maintaining code quality. JavaScript has several popular testing frameworks including Jest and Mocha."]}, {"cell_type": "code", "execution_count": null, "id": "109dc762", "metadata": {}, "outputs": [], "source": ["// Testing Concepts and Examples\n", "console.log(\"=== Testing Fundamentals ===\");\n", "\n", "// Simple functions to test\n", "function add(a, b) {\n", "    return a + b;\n", "}\n", "\n", "function multiply(a, b) {\n", "    return a * b;\n", "}\n", "\n", "function divide(a, b) {\n", "    if (b === 0) {\n", "        throw new Error('Division by zero');\n", "    }\n", "    return a / b;\n", "}\n", "\n", "class Calculator {\n", "    constructor() {\n", "        this.result = 0;\n", "    }\n", "    \n", "    add(value) {\n", "        this.result += value;\n", "        return this;\n", "    }\n", "    \n", "    multiply(value) {\n", "        this.result *= value;\n", "        return this;\n", "    }\n", "    \n", "    getResult() {\n", "        return this.result;\n", "    }\n", "    \n", "    reset() {\n", "        this.result = 0;\n", "        return this;\n", "    }\n", "}\n", "\n", "// Simple assertion function for demonstration\n", "function assert(condition, message) {\n", "    if (!condition) {\n", "        throw new Error(message || '<PERSON><PERSON><PERSON> failed');\n", "    }\n", "    console.log('✓ Test passed:', message);\n", "}\n", "\n", "function assertEqual(actual, expected, message) {\n", "    if (actual !== expected) {\n", "        throw new Error(`${message || 'Asser<PERSON> failed'}: expected ${expected}, got ${actual}`);\n", "    }\n", "    console.log('✓ Test passed:', message);\n", "}\n", "\n", "// Basic tests\n", "console.log('\\nRunning basic tests:');\n", "assertEqual(add(2, 3), 5, 'add(2, 3) should equal 5');\n", "assertEqual(multiply(4, 5), 20, 'multiply(4, 5) should equal 20');\n", "\n", "try {\n", "    divide(10, 0);\n", "    assert(false, 'divide(10, 0) should throw an error');\n", "} catch (error) {\n", "    assert(error.message === 'Division by zero', 'Should throw division by zero error');\n", "}\n", "\n", "// Jest-style syntax examples\n", "console.log(\"\\n=== Jest-style Testing ===\");\n", "\n", "// Note: These are examples of Jest syntax, not actual Jest tests\n", "/*\n", "// Basic Jest test structure\n", "describe('Math functions', () => {\n", "    test('adds 1 + 2 to equal 3', () => {\n", "        expect(add(1, 2)).toBe(3);\n", "    });\n", "    \n", "    test('multiplies 3 * 4 to equal 12', () => {\n", "        expect(multiply(3, 4)).toBe(12);\n", "    });\n", "    \n", "    test('throws error when dividing by zero', () => {\n", "        expect(() => {\n", "            divide(10, 0);\n", "        }).toThrow('Division by zero');\n", "    });\n", "});\n", "\n", "// Testing objects and arrays\n", "describe('Object and array testing', () => {\n", "    test('object equality', () => {\n", "        const user = { name: '<PERSON>', age: 30 };\n", "        expect(user).toEqual({ name: '<PERSON>', age: 30 });\n", "        expect(user).toHaveProperty('name', '<PERSON>');\n", "    });\n", "    \n", "    test('array contains item', () => {\n", "        const fruits = ['apple', 'banana', 'orange'];\n", "        expect(fruits).toContain('banana');\n", "        expect(fruits).toHaveLength(3);\n", "    });\n", "});\n", "\n", "// Async testing\n", "describe('Async functions', () => {\n", "    test('async function resolves', async () => {\n", "        const result = await fetchData();\n", "        expect(result).toBe('data');\n", "    });\n", "    \n", "    test('promise resolves', () => {\n", "        return expect(fetchData()).resolves.toBe('data');\n", "    });\n", "    \n", "    test('promise rejects', () => {\n", "        return expect(fetchDataWithError()).rejects.toThrow('Error message');\n", "    });\n", "});\n", "\n", "// Setup and teardown\n", "describe('Calculator class', () => {\n", "    let calculator;\n", "    \n", "    beforeEach(() => {\n", "        calculator = new Calculator();\n", "    });\n", "    \n", "    afterEach(() => {\n", "        calculator = null;\n", "    });\n", "    \n", "    test('should start with result 0', () => {\n", "        expect(calculator.getResult()).toBe(0);\n", "    });\n", "    \n", "    test('should add numbers correctly', () => {\n", "        calculator.add(5).add(3);\n", "        expect(calculator.getResult()).toBe(8);\n", "    });\n", "    \n", "    test('should reset to 0', () => {\n", "        calculator.add(10).reset();\n", "        expect(calculator.getResult()).toBe(0);\n", "    });\n", "});\n", "*/\n", "\n", "// Mocha-style syntax examples\n", "console.log(\"\\n=== Mocha-style Testing ===\");\n", "\n", "/*\n", "// <PERSON><PERSON> with <PERSON><PERSON> assertions\n", "const { expect } = require('chai');\n", "\n", "describe('Math functions', function() {\n", "    it('should add two numbers correctly', function() {\n", "        expect(add(2, 3)).to.equal(5);\n", "    });\n", "    \n", "    it('should multiply two numbers correctly', function() {\n", "        expect(multiply(4, 5)).to.equal(20);\n", "    });\n", "    \n", "    it('should throw error for division by zero', function() {\n", "        expect(() => divide(10, 0)).to.throw('Division by zero');\n", "    });\n", "});\n", "\n", "// Async testing in Mocha\n", "describe('Async operations', function() {\n", "    it('should handle promises', function() {\n", "        return fetchData().then(result => {\n", "            expect(result).to.equal('data');\n", "        });\n", "    });\n", "    \n", "    it('should handle async/await', async function() {\n", "        const result = await fetchData();\n", "        expect(result).to.equal('data');\n", "    });\n", "    \n", "    it('should handle callbacks', function(done) {\n", "        fetchDataWithCallback((err, result) => {\n", "            if (err) return done(err);\n", "            expect(result).to.equal('data');\n", "            done();\n", "        });\n", "    });\n", "});\n", "*/\n", "\n", "// Mocking and Spying\n", "console.log(\"\\n=== Mocking and Spying ===\");\n", "\n", "// Simple mock implementation\n", "class MockFunction {\n", "    constructor(returnValue) {\n", "        this.returnValue = returnValue;\n", "        this.calls = [];\n", "        this.callCount = 0;\n", "    }\n", "    \n", "    call(...args) {\n", "        this.calls.push(args);\n", "        this.callCount++;\n", "        return this.returnValue;\n", "    }\n", "    \n", "    wasCalledWith(...args) {\n", "        return this.calls.some(call => \n", "            call.length === args.length && \n", "            call.every((arg, index) => arg === args[index])\n", "        );\n", "    }\n", "    \n", "    getCallCount() {\n", "        return this.callCount;\n", "    }\n", "}\n", "\n", "// Using mocks\n", "const mockApiCall = new MockFunction({ data: 'mocked data' });\n", "const result = mockApiCall.call('user', 123);\n", "console.log('Mock result:', result);\n", "console.log('Was called with user, 123:', mockApiCall.wasCalledWith('user', 123));\n", "console.log('Call count:', mockApiCall.getCallCount());\n", "\n", "/*\n", "// Jest mocking examples\n", "describe('Mocking with Je<PERSON>', () => {\n", "    test('should mock a function', () => {\n", "        const mockFn = jest.fn();\n", "        mockFn('arg1', 'arg2');\n", "        \n", "        expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');\n", "        expect(mockFn).toHaveBeenCalledTimes(1);\n", "    });\n", "    \n", "    test('should mock module', () => {\n", "        jest.mock('./api', () => ({\n", "            fetchUser: jest.fn(() => Promise.resolve({ id: 1, name: '<PERSON>' }))\n", "        }));\n", "        \n", "        const api = require('./api');\n", "        return api.fetchUser().then(user => {\n", "            expect(user.name).to<PERSON>e('<PERSON>');\n", "        });\n", "    });\n", "    \n", "    test('should spy on method', () => {\n", "        const calculator = new Calculator();\n", "        const spy = jest.spyOn(calculator, 'add');\n", "        \n", "        calculator.add(5);\n", "        \n", "        expect(spy).toHaveBeenCalledWith(5);\n", "        spy.mockRestore();\n", "    });\n", "});\n", "*/\n", "\n", "// Test-Driven Development (TDD) example\n", "console.log(\"\\n=== TDD Example ===\");\n", "\n", "// Step 1: Write failing test\n", "// Step 2: Write minimal code to pass\n", "// Step 3: Refactor\n", "\n", "// Example: String utility function\n", "function capitalize(str) {\n", "    if (typeof str !== 'string' || str.length === 0) {\n", "        return str;\n", "    }\n", "    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n", "}\n", "\n", "// Tests for capitalize function\n", "console.log('Testing capitalize function:');\n", "assertEqual(capitalize('hello'), 'Hello', 'Should capitalize first letter');\n", "assertEqual(capitalize('WORLD'), 'World', 'Should lowercase other letters');\n", "assertEqual(capitalize(''), '', 'Should handle empty string');\n", "assertEqual(capitalize('a'), 'A', 'Should handle single character');\n", "\n", "// Testing best practices\n", "console.log(\"\\n=== Testing Best Practices ===\");\n", "\n", "const testingBestPractices = [\n", "    '1. Write tests first (TDD) or alongside code',\n", "    '2. Keep tests simple and focused',\n", "    '3. Use descriptive test names',\n", "    '4. Test edge cases and error conditions',\n", "    '5. Mock external dependencies',\n", "    '6. Maintain good test coverage',\n", "    '7. Keep tests independent and isolated',\n", "    '8. Use setup and teardown appropriately',\n", "    '9. Test behavior, not implementation',\n", "    '10. Run tests frequently and in CI/CD'\n", "];\n", "\n", "testingBestPractices.forEach(practice => console.log(practice));\n", "\n", "console.log('\\nTesting concepts demonstrated!');"]}, {"cell_type": "markdown", "id": "3ef56821", "metadata": {}, "source": ["# TypeScript Basics {#typescript}\n", "\n", "TypeScript is a superset of JavaScript that adds static type checking. It helps catch errors at compile time and improves code maintainability."]}, {"cell_type": "code", "execution_count": null, "id": "b8426e5c", "metadata": {}, "outputs": [], "source": ["// TypeScript Examples (shown as comments since this is a JS notebook)\n", "console.log(\"=== TypeScript Basics ===\");\n", "\n", "/*\n", "// Basic type annotations\n", "let name: string = \"<PERSON>\";\n", "let age: number = 30;\n", "let isActive: boolean = true;\n", "let items: string[] = [\"apple\", \"banana\", \"cherry\"];\n", "let numbers: Array<number> = [1, 2, 3, 4, 5];\n", "\n", "// Function with type annotations\n", "function greet(name: string, age?: number): string {\n", "    if (age) {\n", "        return `Hello ${name}, you are ${age} years old`;\n", "    }\n", "    return `Hello ${name}`;\n", "}\n", "\n", "// Interface definitions\n", "interface User {\n", "    id: number;\n", "    name: string;\n", "    email: string;\n", "    age?: number; // Optional property\n", "    readonly createdAt: Date; // Read-only property\n", "}\n", "\n", "interface UserMethods {\n", "    updateEmail(newEmail: string): void;\n", "    getFullInfo(): string;\n", "}\n", "\n", "// Type aliases\n", "type Status = \"pending\" | \"approved\" | \"rejected\";\n", "type ID = string | number;\n", "type UserWithMethods = User & UserMethods;\n", "\n", "// Class with TypeScript\n", "class UserAccount implements User, UserMethods {\n", "    readonly id: number;\n", "    name: string;\n", "    email: string;\n", "    age?: number;\n", "    readonly createdAt: Date;\n", "    private _status: Status = \"pending\";\n", "    \n", "    constructor(id: number, name: string, email: string, age?: number) {\n", "        this.id = id;\n", "        this.name = name;\n", "        this.email = email;\n", "        this.age = age;\n", "        this.createdAt = new Date();\n", "    }\n", "    \n", "    updateEmail(newEmail: string): void {\n", "        this.email = newEmail;\n", "    }\n", "    \n", "    getFullInfo(): string {\n", "        return `${this.name} (${this.email}) - Status: ${this._status}`;\n", "    }\n", "    \n", "    get status(): Status {\n", "        return this._status;\n", "    }\n", "    \n", "    set status(newStatus: Status) {\n", "        this._status = newStatus;\n", "    }\n", "}\n", "\n", "// Generic types\n", "interface ApiResponse<T> {\n", "    data: T;\n", "    status: number;\n", "    message: string;\n", "}\n", "\n", "function fetchData<T>(url: string): Promise<ApiResponse<T>> {\n", "    return fetch(url).then(response => response.json());\n", "}\n", "\n", "// Usage\n", "const userResponse: Promise<ApiResponse<User>> = fetchData<User>('/api/user/1');\n", "const usersResponse: Promise<ApiResponse<User[]>> = fetchData<User[]>('/api/users');\n", "\n", "// Utility types\n", "type PartialUser = Partial<User>; // All properties optional\n", "type RequiredUser = Required<User>; // All properties required\n", "type UserEmail = Pick<User, 'email'>; // Only email property\n", "type UserWithoutId = Omit<User, 'id'>; // All properties except id\n", "\n", "// Enum\n", "enum UserRole {\n", "    ADMIN = \"admin\",\n", "    USER = \"user\",\n", "    MODERATOR = \"moderator\"\n", "}\n", "\n", "// Advanced types\n", "type EventHandler<T> = (event: T) => void;\n", "type AsyncFunction<T, R> = (arg: T) => Promise<R>;\n", "\n", "// Conditional types\n", "type NonNullable<T> = T extends null | undefined ? never : T;\n", "type ReturnType<T> = T extends (...args: any[]) => infer R ? R : any;\n", "*/\n", "\n", "// JavaScript equivalent implementations\n", "console.log(\"JavaScript implementations of TypeScript concepts:\");\n", "\n", "// Simulating TypeScript interfaces with JSDoc\n", "/**\n", " * @typedef {Object} User\n", " * @property {number} id\n", " * @property {string} name\n", " * @property {string} email\n", " * @property {number} [age]\n", " * @property {Date} createdAt\n", " */\n", "\n", "/**\n", " * Creates a user object\n", " * @param {number} id\n", " * @param {string} name\n", " * @param {string} email\n", " * @param {number} [age]\n", " * @returns {User}\n", " */\n", "function createUser(id, name, email, age) {\n", "    return {\n", "        id,\n", "        name,\n", "        email,\n", "        age,\n", "        createdAt: new Date()\n", "    };\n", "}\n", "\n", "// Runtime type checking (what TypeScript prevents at compile time)\n", "function validateUser(user) {\n", "    if (typeof user.id !== 'number') {\n", "        throw new Error('User ID must be a number');\n", "    }\n", "    if (typeof user.name !== 'string') {\n", "        throw new Error('User name must be a string');\n", "    }\n", "    if (typeof user.email !== 'string') {\n", "        throw new Error('User email must be a string');\n", "    }\n", "    return true;\n", "}\n", "\n", "const user = createUser(1, \"<PERSON>\", \"<EMAIL>\", 30);\n", "console.log('Created user:', user);\n", "console.log('User validation:', validate<PERSON>ser(user));\n", "\n", "console.log('\\nTypeScript provides compile-time type checking that JavaScript lacks.');\n", "console.log('Benefits of TypeScript:');\n", "console.log('- Catch errors at compile time');\n", "console.log('- Better IDE support and autocomplete');\n", "console.log('- Improved code documentation');\n", "console.log('- Easier refactoring');\n", "console.log('- Better team collaboration');"]}, {"cell_type": "markdown", "id": "ded1ffb7", "metadata": {}, "source": ["# CLI and Node.js Basics {#nodejs}\n", "\n", "Node.js allows JavaScript to run outside the browser, enabling server-side development and command-line tools."]}, {"cell_type": "code", "execution_count": null, "id": "bdaf0638", "metadata": {}, "outputs": [], "source": ["// Node.js Basics\n", "console.log(\"=== Node.js Fundamentals ===\");\n", "\n", "// Note: These examples show Node.js concepts but may not all work in this environment\n", "\n", "// Global objects in Node.js\n", "console.log('Node.js global objects:');\n", "console.log('- global: Global namespace object');\n", "console.log('- process: Information about current process');\n", "console.log('- Buffer: Binary data handling');\n", "console.log('- __dirname: Current directory path');\n", "console.log('- __filename: Current file path');\n", "console.log('- require: Import modules');\n", "console.log('- module: Current module information');\n", "console.log('- exports: Export from current module');\n", "\n", "// Process object examples\n", "if (typeof process !== 'undefined') {\n", "    console.log('\\nProcess information:');\n", "    console.log('Node version:', process.version);\n", "    console.log('Platform:', process.platform);\n", "    console.log('Architecture:', process.arch);\n", "    console.log('Process ID:', process.pid);\n", "    console.log('Current working directory:', process.cwd());\n", "    console.log('Command line arguments:', process.argv.slice(2));\n", "}\n", "\n", "// File System operations (Node.js)\n", "console.log(\"\\n=== File System Operations ===\");\n", "\n", "/*\n", "// CommonJS require syntax\n", "const fs = require('fs');\n", "const path = require('path');\n", "\n", "// Synchronous file operations\n", "try {\n", "    const data = fs.readFileSync('example.txt', 'utf8');\n", "    console.log('File content:', data);\n", "} catch (error) {\n", "    console.error('Error reading file:', error.message);\n", "}\n", "\n", "// Asynchronous file operations\n", "fs.readFile('example.txt', 'utf8', (err, data) => {\n", "    if (err) {\n", "        console.error('Error reading file:', err.message);\n", "        return;\n", "    }\n", "    console.log('File content:', data);\n", "});\n", "\n", "// Promise-based file operations\n", "const fsPromises = require('fs').promises;\n", "\n", "async function readFileAsync() {\n", "    try {\n", "        const data = await fsPromises.readFile('example.txt', 'utf8');\n", "        console.log('File content:', data);\n", "    } catch (error) {\n", "        console.error('Error reading file:', error.message);\n", "    }\n", "}\n", "\n", "// Writing files\n", "const content = 'Hello, Node.js!';\n", "fs.writeFile('output.txt', content, 'utf8', (err) => {\n", "    if (err) {\n", "        console.error('Error writing file:', err.message);\n", "        return;\n", "    }\n", "    console.log('File written successfully');\n", "});\n", "\n", "// Directory operations\n", "fs.readdir('.', (err, files) => {\n", "    if (err) {\n", "        console.error('Error reading directory:', err.message);\n", "        return;\n", "    }\n", "    console.log('Directory contents:', files);\n", "});\n", "*/\n", "\n", "// HTTP Server (Node.js)\n", "console.log(\"\\n=== HTTP Server Example ===\");\n", "\n", "/*\n", "const http = require('http');\n", "const url = require('url');\n", "\n", "// Create HTTP server\n", "const server = http.createServer((req, res) => {\n", "    const parsedUrl = url.parse(req.url, true);\n", "    const path = parsedUrl.pathname;\n", "    const method = req.method;\n", "    \n", "    // Set CORS headers\n", "    res.set<PERSON><PERSON><PERSON>('Access-Control-Allow-Origin', '*');\n", "    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');\n", "    res.set<PERSON><PERSON><PERSON>('Access-Control-Allow-Headers', 'Content-Type');\n", "    \n", "    // Handle different routes\n", "    if (path === '/' && method === 'GET') {\n", "        res.writeHead(200, { 'Content-Type': 'text/html' });\n", "        res.end('<h1>Welcome to Node.js Server</h1>');\n", "    } else if (path === '/api/users' && method === 'GET') {\n", "        res.writeHead(200, { 'Content-Type': 'application/json' });\n", "        res.end(JSON.stringify([\n", "            { id: 1, name: '<PERSON>' },\n", "            { id: 2, name: '<PERSON>' }\n", "        ]));\n", "    } else if (path === '/api/users' && method === 'POST') {\n", "        let body = '';\n", "        req.on('data', chunk => {\n", "            body += chunk.toString();\n", "        });\n", "        req.on('end', () => {\n", "            try {\n", "                const userData = JSON.parse(body);\n", "                res.writeHead(201, { 'Content-Type': 'application/json' });\n", "                res.end(JSON.stringify({ id: 3, ...userData }));\n", "            } catch (error) {\n", "                res.writeHead(400, { 'Content-Type': 'application/json' });\n", "                res.end(JSON.stringify({ error: 'Invalid JSON' }));\n", "            }\n", "        });\n", "    } else {\n", "        res.writeHead(404, { 'Content-Type': 'text/plain' });\n", "        res.end('Not Found');\n", "    }\n", "});\n", "\n", "// Start server\n", "const PORT = process.env.PORT || 3000;\n", "server.listen(PORT, () => {\n", "    console.log(`Server running on port ${PORT}`);\n", "});\n", "*/\n", "\n", "// Command Line Interface (CLI) example\n", "console.log(\"\\n=== CLI Application Example ===\");\n", "\n", "// Simulating command line arguments\n", "const mockArgv = ['node', 'script.js', '--name', '<PERSON>', '--age', '30', '--verbose'];\n", "\n", "function parseArguments(argv) {\n", "    const args = {};\n", "    const flags = [];\n", "    \n", "    for (let i = 2; i < argv.length; i++) {\n", "        const arg = argv[i];\n", "        \n", "        if (arg.startsWith('--')) {\n", "            const key = arg.slice(2);\n", "            const nextArg = argv[i + 1];\n", "            \n", "            if (nextArg && !nextArg.startsWith('--')) {\n", "                args[key] = nextArg;\n", "                i++; // Skip next argument\n", "            } else {\n", "                flags.push(key);\n", "            }\n", "        }\n", "    }\n", "    \n", "    return { args, flags };\n", "}\n", "\n", "const { args, flags } = parseArguments(mockArgv);\n", "console.log('Parsed arguments:', args);\n", "console.log('Parsed flags:', flags);\n", "\n", "// Simple CLI application\n", "function cliApp() {\n", "    const { name, age } = args;\n", "    const verbose = flags.includes('verbose');\n", "    \n", "    if (!name) {\n", "        console.error('Error: --name is required');\n", "        process.exit(1);\n", "    }\n", "    \n", "    if (verbose) {\n", "        console.log('Running in verbose mode');\n", "    }\n", "    \n", "    console.log(`Hello, ${name}!`);\n", "    \n", "    if (age) {\n", "        console.log(`You are ${age} years old.`);\n", "    }\n", "}\n", "\n", "cliApp();\n", "\n", "// Package.json and NPM\n", "console.log(\"\\n=== Package Management ===\");\n", "\n", "/*\n", "// Example package.json\n", "{\n", "  \"name\": \"my-node-app\",\n", "  \"version\": \"1.0.0\",\n", "  \"description\": \"A sample Node.js application\",\n", "  \"main\": \"index.js\",\n", "  \"scripts\": {\n", "    \"start\": \"node index.js\",\n", "    \"dev\": \"nodemon index.js\",\n", "    \"test\": \"jest\",\n", "    \"build\": \"webpack --mode production\"\n", "  },\n", "  \"dependencies\": {\n", "    \"express\": \"^4.18.0\",\n", "    \"lodash\": \"^4.17.21\"\n", "  },\n", "  \"devDependencies\": {\n", "    \"nodemon\": \"^2.0.15\",\n", "    \"jest\": \"^27.5.1\"\n", "  },\n", "  \"keywords\": [\"node\", \"javascript\", \"api\"],\n", "  \"author\": \"Your Name\",\n", "  \"license\": \"MIT\"\n", "}\n", "\n", "// NPM commands:\n", "// npm init - Initialize new package\n", "// npm install <package> - Install package\n", "// npm install <package> --save-dev - Install as dev dependency\n", "// npm uninstall <package> - Remove package\n", "// npm update - Update packages\n", "// npm run <script> - Run script from package.json\n", "// npm publish - Publish package to NPM registry\n", "*/\n", "\n", "// Environment Variables\n", "console.log(\"\\n=== Environment Variables ===\");\n", "\n", "// Simulating environment variables\n", "const mockEnv = {\n", "    NODE_ENV: 'development',\n", "    PORT: '3000',\n", "    DATABASE_URL: 'mongodb://localhost:27017/myapp',\n", "    API_KEY: 'your-secret-api-key'\n", "};\n", "\n", "function getConfig() {\n", "    return {\n", "        environment: mockEnv.NODE_ENV || 'production',\n", "        port: parseInt(mockEnv.PORT) || 8080,\n", "        databaseUrl: mockEnv.DATABASE_URL,\n", "        apiKey: mockEnv.API_KEY,\n", "        isDevelopment: mockEnv.NODE_ENV === 'development'\n", "    };\n", "}\n", "\n", "const config = getConfig();\n", "console.log('Application config:', config);\n", "\n", "console.log('\\n=== Node.js Best Practices ===\");\n", "const bestPractices = [\n", "    '1. Use environment variables for configuration',\n", "    '2. <PERSON><PERSON> errors properly with try-catch and error events',\n", "    '3. Use async/await for better async code',\n", "    '4. Implement proper logging',\n", "    '5. Use process managers like PM2 for production',\n", "    '6. Keep dependencies up to date',\n", "    '7. Use ESLint and Prettier for code quality',\n", "    '8. Implement proper security measures',\n", "    '9. Use clustering for CPU-intensive applications',\n", "    '10. Monitor application performance'\n", "];\n", "\n", "bestPractices.forEach(practice => console.log(practice));\n", "\n", "console.log('\\n🎉 JavaScript Manual Complete!');\n", "console.log('You now have a comprehensive reference for JavaScript development.');\n", "console.log('Continue learning by building projects and exploring advanced topics!');"]}], "metadata": {"kernelspec": {"display_name": "JavaScript (Node.js)", "language": "javascript", "name": "javascript"}, "language_info": {"file_extension": ".js", "mimetype": "application/javascript", "name": "javascript", "version": "18.0.0"}}, "nbformat": 4, "nbformat_minor": 5}