{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "c47163a8",
   "metadata": {},
   "source": [
    "# Comprehensive JavaScript Programming Manual\n",
    "\n",
    "This manual serves as a complete reference for JavaScript programming, from basics to advanced concepts. Each section includes concise explanations and practical code examples that you can run directly in this notebook or in a browser console.\n",
    "\n",
    "## Table of Contents\n",
    "1. [Basic Syntax and Data Types](#basic-syntax)\n",
    "2. [Control Flow and Loops](#control-flow)\n",
    "3. [Functions and Arrow Functions](#functions)\n",
    "4. [Objects and Prototypes](#objects)\n",
    "5. [Arrays and Array Methods](#arrays)\n",
    "6. [DOM Manipulation and Events](#dom)\n",
    "7. [Asynchronous JavaScript (Promises, Async/Await)](#async)\n",
    "8. [Modules and Imports/Exports](#modules)\n",
    "9. [Error Handling and Debugging](#errors)\n",
    "10. [ES6+ Features (Destructuring, Spread, Rest)](#es6)\n",
    "11. [Closures and Scope](#closures)\n",
    "12. [Classes and Inheritance](#classes)\n",
    "13. [Event Loop and Concurrency](#event-loop)\n",
    "14. [Web APIs and Fetch](#web-apis)\n",
    "15. [Local Storage and Cookies](#storage)\n",
    "16. [Testing (Jest, Mocha)](#testing)\n",
    "17. [Build Tools and Bundlers (Webpack, Rollup)](#build-tools)\n",
    "18. [Frameworks Overview (React, Vue, Angular basics)](#frameworks)\n",
    "19. [TypeScript Basics](#typescript)\n",
    "20. [Best Practices and Code Style](#best-practices)\n",
    "21. [CLI and Node.js Basics](#nodejs)"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "73fefecc",
   "metadata": {},
   "source": [
    "# Basic Syntax and Data Types {#basic-syntax}\n",
    "\n",
    "JavaScript is a dynamically typed language with flexible syntax. Let's explore variables, operators, and fundamental data types."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e84d17fb",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Variables and basic data types\n",
    "let x = 42;                    // Number\n",
    "const pi = 3.14159;           // Number (constant)\n",
    "var name = \"JavaScript\";       // String (avoid var in modern JS)\n",
    "let isAwesome = true;         // Boolean\n",
    "let nothing = null;           // Null\n",
    "let notDefined;               // Undefined\n",
    "let bigNumber = 123n;         // BigInt\n",
    "let symbol = Symbol('id');    // Symbol\n",
    "\n",
    "// Basic operators\n",
    "let sum = 10 + 5;             // Addition\n",
    "let diff = 10 - 5;            // Subtraction\n",
    "let product = 10 * 5;         // Multiplication\n",
    "let quotient = 10 / 5;        // Division\n",
    "let remainder = 10 % 3;       // Modulo\n",
    "let power = 2 ** 3;           // Exponentiation\n",
    "\n",
    "// String operations\n",
    "let greeting = \"Hello\";\n",
    "let target = \"World\";\n",
    "let message = `${greeting}, ${target}!`;  // Template literals\n",
    "console.log(message);\n",
    "\n",
    "// Type conversion\n",
    "let strNum = \"123\";\n",
    "let num = Number(strNum);     // String to number\n",
    "let floatNum = parseFloat(strNum); // String to float\n",
    "let strBack = String(num);    // Number to string\n",
    "\n",
    "// Display types and values\n",
    "console.log(`Type of x: ${typeof x}, Value: ${x}`);\n",
    "console.log(`Type of pi: ${typeof pi}, Value: ${pi}`);\n",
    "console.log(`Type of name: ${typeof name}, Value: ${name}`);\n",
    "console.log(`Type of isAwesome: ${typeof isAwesome}, Value: ${isAwesome}`);\n",
    "console.log(`Type of nothing: ${typeof nothing}, Value: ${nothing}`);\n",
    "console.log(`Type of notDefined: ${typeof notDefined}, Value: ${notDefined}`);\n",
    "\n",
    "// Strict equality vs loose equality\n",
    "console.log(`\\nStrict equality (===): 5 === '5' is ${5 === '5'}`);\n",
    "console.log(`Loose equality (==): 5 == '5' is ${5 == '5'}`);\n",
    "\n",
    "// Truthy and falsy values\n",
    "let falsyValues = [false, 0, -0, 0n, '', null, undefined, NaN];\n",
    "console.log('\\nFalsy values:');\n",
    "falsyValues.forEach(val => {\n",
    "    console.log(`${val} is ${Boolean(val) ? 'truthy' : 'falsy'}`);\n",
    "});"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "71c71fe2",
   "metadata": {},
   "source": [
    "# Control Flow and Loops {#control-flow}\n",
    "\n",
    "JavaScript provides various structures for controlling program flow: conditional statements, loops, and flow control statements."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "ff42471f",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Conditional statements\n",
    "let score = 85;\n",
    "let grade;\n",
    "\n",
    "if (score >= 90) {\n",
    "    grade = 'A';\n",
    "} else if (score >= 80) {\n",
    "    grade = 'B';\n",
    "} else if (score >= 70) {\n",
    "    grade = 'C';\n",
    "} else {\n",
    "    grade = 'F';\n",
    "}\n",
    "\n",
    "console.log(`Score: ${score}, Grade: ${grade}`);\n",
    "\n",
    "// Ternary operator\n",
    "let status = score >= 70 ? 'Pass' : 'Fail';\n",
    "console.log(`Status: ${status}`);\n",
    "\n",
    "// Switch statement\n",
    "let day = 3;\n",
    "let dayName;\n",
    "\n",
    "switch (day) {\n",
    "    case 1:\n",
    "        dayName = 'Monday';\n",
    "        break;\n",
    "    case 2:\n",
    "        dayName = 'Tuesday';\n",
    "        break;\n",
    "    case 3:\n",
    "        dayName = 'Wednesday';\n",
    "        break;\n",
    "    default:\n",
    "        dayName = 'Unknown';\n",
    "}\n",
    "console.log(`\\nDay ${day} is ${dayName}`);\n",
    "\n",
    "// For loop\n",
    "console.log('\\nFor loop:');\n",
    "for (let i = 0; i < 5; i++) {\n",
    "    console.log(`Iteration ${i}`);\n",
    "}\n",
    "\n",
    "// For...of loop (for arrays)\n",
    "console.log('\\nFor...of loop:');\n",
    "let fruits = ['apple', 'banana', 'cherry'];\n",
    "for (let fruit of fruits) {\n",
    "    console.log(fruit);\n",
    "}\n",
    "\n",
    "// For...in loop (for object properties)\n",
    "console.log('\\nFor...in loop:');\n",
    "let person = { name: 'Alice', age: 30, city: 'New York' };\n",
    "for (let key in person) {\n",
    "    console.log(`${key}: ${person[key]}`);\n",
    "}\n",
    "\n",
    "// While loop with break and continue\n",
    "console.log('\\nWhile loop with break and continue:');\n",
    "let counter = 0;\n",
    "while (true) {\n",
    "    counter++;\n",
    "    if (counter === 3) {\n",
    "        continue; // Skip iteration when counter is 3\n",
    "    }\n",
    "    if (counter > 5) {\n",
    "        break;    // Exit loop when counter exceeds 5\n",
    "    }\n",
    "    console.log(`Counter: ${counter}`);\n",
    "}\n",
    "\n",
    "// Do...while loop\n",
    "console.log('\\nDo...while loop:');\n",
    "let num = 1;\n",
    "do {\n",
    "    console.log(`Number: ${num}`);\n",
    "    num++;\n",
    "} while (num <= 3);\n",
    "\n",
    "// Array methods for iteration\n",
    "console.log('\\nArray iteration methods:');\n",
    "let numbers = [1, 2, 3, 4, 5];\n",
    "\n",
    "// forEach\n",
    "numbers.forEach((num, index) => {\n",
    "    console.log(`Index ${index}: ${num}`);\n",
    "});\n",
    "\n",
    "// map\n",
    "let doubled = numbers.map(num => num * 2);\n",
    "console.log(`Doubled: ${doubled}`);\n",
    "\n",
    "// filter\n",
    "let evenNumbers = numbers.filter(num => num % 2 === 0);\n",
    "console.log(`Even numbers: ${evenNumbers}`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "b3ad4049",
   "metadata": {},
   "source": [
    "# Functions and Arrow Functions {#functions}\n",
    "\n",
    "Functions are reusable blocks of code. JavaScript supports function declarations, expressions, arrow functions, and various parameter patterns."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "4adc818d",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Function declaration\n",
    "function greet(name, greeting = \"Hello\") {\n",
    "    /**\n",
    "     * A simple greeting function with a default parameter.\n",
    "     * @param {string} name - The name to greet\n",
    "     * @param {string} greeting - The greeting to use (default: \"Hello\")\n",
    "     * @returns {string} The complete greeting message\n",
    "     */\n",
    "    return `${greeting}, ${name}!`;\n",
    "}\n",
    "\n",
    "// Function calls\n",
    "console.log(greet(\"Alice\"));\n",
    "console.log(greet(\"Bob\", \"Hi\"));\n",
    "\n",
    "// Function expression\n",
    "const multiply = function(a, b) {\n",
    "    return a * b;\n",
    "};\n",
    "\n",
    "console.log(`Multiply: ${multiply(5, 3)}`);\n",
    "\n",
    "// Arrow functions\n",
    "const square = x => x ** 2;\n",
    "const cube = x => x ** 3;\n",
    "const add = (a, b) => a + b;\n",
    "const complexFunction = (x, y) => {\n",
    "    const sum = x + y;\n",
    "    const product = x * y;\n",
    "    return { sum, product };\n",
    "};\n",
    "\n",
    "console.log(`\\nSquare of 5: ${square(5)}`);\n",
    "console.log(`Cube of 3: ${cube(3)}`);\n",
    "console.log(`Add 2 + 3: ${add(2, 3)}`);\n",
    "console.log(`Complex function result:`, complexFunction(4, 5));\n",
    "\n",
    "// Rest parameters\n",
    "function sum(...numbers) {\n",
    "    return numbers.reduce((total, num) => total + num, 0);\n",
    "}\n",
    "\n",
    "console.log(`\\nSum of multiple numbers: ${sum(1, 2, 3, 4, 5)}`);\n",
    "\n",
    "// Destructuring parameters\n",
    "function displayPerson({ name, age, city = \"Unknown\" }) {\n",
    "    console.log(`Name: ${name}, Age: ${age}, City: ${city}`);\n",
    "}\n",
    "\n",
    "displayPerson({ name: \"Alice\", age: 30, city: \"New York\" });\n",
    "displayPerson({ name: \"Bob\", age: 25 });\n",
    "\n",
    "// Higher-order functions\n",
    "function createMultiplier(factor) {\n",
    "    return function(x) {\n",
    "        return x * factor;\n",
    "    };\n",
    "}\n",
    "\n",
    "const double = createMultiplier(2);\n",
    "const triple = createMultiplier(3);\n",
    "\n",
    "console.log(`\\nDouble 5: ${double(5)}`);\n",
    "console.log(`Triple 5: ${triple(5)}`);\n",
    "\n",
    "// Function as callback\n",
    "function processArray(arr, callback) {\n",
    "    return arr.map(callback);\n",
    "}\n",
    "\n",
    "const numbers = [1, 2, 3, 4, 5];\n",
    "const squared = processArray(numbers, x => x ** 2);\n",
    "console.log(`\\nSquared numbers: ${squared}`);\n",
    "\n",
    "// Immediately Invoked Function Expression (IIFE)\n",
    "(function() {\n",
    "    console.log(\"\\nThis function runs immediately!\");\n",
    "})();\n",
    "\n",
    "// Arrow function IIFE\n",
    "(() => {\n",
    "    console.log(\"Arrow function IIFE!\");\n",
    "})();\n",
    "\n",
    "// Function hoisting demonstration\n",
    "console.log(`\\nHoisted function result: ${hoistedFunction(5)}`);\n",
    "\n",
    "function hoistedFunction(x) {\n",
    "    return x * 2;\n",
    "}\n",
    "\n",
    "// Method shorthand in objects\n",
    "const calculator = {\n",
    "    add(a, b) {\n",
    "        return a + b;\n",
    "    },\n",
    "    subtract: (a, b) => a - b, // Arrow function as method\n",
    "    multiply: function(a, b) {\n",
    "        return a * b;\n",
    "    }\n",
    "};\n",
    "\n",
    "console.log(`\\nCalculator methods:`);\n",
    "console.log(`Add: ${calculator.add(10, 5)}`);\n",
    "console.log(`Subtract: ${calculator.subtract(10, 5)}`);\n",
    "console.log(`Multiply: ${calculator.multiply(10, 5)}`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "fa36a827",
   "metadata": {},
   "source": [
    "# Objects and Prototypes {#objects}\n",
    "\n",
    "JavaScript is a prototype-based language. Objects are collections of key-value pairs and can inherit from other objects through the prototype chain."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "56c306d3",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Object creation methods\n",
    "console.log(\"=== Object Creation ===\");\n",
    "\n",
    "// Object literal\n",
    "const person = {\n",
    "    name: \"Alice\",\n",
    "    age: 30,\n",
    "    city: \"New York\",\n",
    "    greet() {\n",
    "        return `Hello, I'm ${this.name}`;\n",
    "    }\n",
    "};\n",
    "\n",
    "console.log(person.greet());\n",
    "console.log(`Person:`, person);\n",
    "\n",
    "// Object.create()\n",
    "const personPrototype = {\n",
    "    greet() {\n",
    "        return `Hello, I'm ${this.name}`;\n",
    "    },\n",
    "    introduce() {\n",
    "        return `I'm ${this.name}, ${this.age} years old`;\n",
    "    }\n",
    "};\n",
    "\n",
    "const john = Object.create(personPrototype);\n",
    "john.name = \"John\";\n",
    "john.age = 25;\n",
    "\n",
    "console.log(`\\n${john.introduce()}`);\n",
    "\n",
    "// Constructor function\n",
    "function Person(name, age) {\n",
    "    this.name = name;\n",
    "    this.age = age;\n",
    "}\n",
    "\n",
    "Person.prototype.greet = function() {\n",
    "    return `Hello, I'm ${this.name}`;\n",
    "};\n",
    "\n",
    "Person.prototype.getAge = function() {\n",
    "    return this.age;\n",
    "};\n",
    "\n",
    "const bob = new Person(\"Bob\", 28);\n",
    "console.log(`\\n${bob.greet()}`);\n",
    "console.log(`Bob's age: ${bob.getAge()}`);\n",
    "\n",
    "// Property access and manipulation\n",
    "console.log(\"\\n=== Property Operations ===\");\n",
    "\n",
    "// Dot notation vs bracket notation\n",
    "console.log(`Dot notation: ${person.name}`);\n",
    "console.log(`Bracket notation: ${person['age']}`);\n",
    "\n",
    "// Dynamic property access\n",
    "const prop = \"city\";\n",
    "console.log(`Dynamic access: ${person[prop]}`);\n",
    "\n",
    "// Adding and deleting properties\n",
    "person.email = \"<EMAIL>\";\n",
    "console.log(`After adding email:`, person);\n",
    "\n",
    "delete person.city;\n",
    "console.log(`After deleting city:`, person);\n",
    "\n",
    "// Object methods\n",
    "console.log(\"\\n=== Object Methods ===\");\n",
    "\n",
    "const user = {\n",
    "    name: \"Charlie\",\n",
    "    age: 35,\n",
    "    skills: [\"JavaScript\", \"Python\", \"React\"]\n",
    "};\n",
    "\n",
    "console.log(`Keys: ${Object.keys(user)}`);\n",
    "console.log(`Values:`, Object.values(user));\n",
    "console.log(`Entries:`, Object.entries(user));\n",
    "\n",
    "// Object.assign() for copying/merging\n",
    "const userCopy = Object.assign({}, user);\n",
    "const extendedUser = Object.assign({}, user, { country: \"USA\", active: true });\n",
    "\n",
    "console.log(`\\nExtended user:`, extendedUser);\n",
    "\n",
    "// Spread operator for objects\n",
    "const spreadCopy = { ...user };\n",
    "const spreadExtended = { ...user, country: \"Canada\", age: 36 };\n",
    "\n",
    "console.log(`Spread extended:`, spreadExtended);\n",
    "\n",
    "// Property descriptors\n",
    "console.log(\"\\n=== Property Descriptors ===\");\n",
    "\n",
    "const product = {};\n",
    "\n",
    "Object.defineProperty(product, 'name', {\n",
    "    value: 'Laptop',\n",
    "    writable: false,\n",
    "    enumerable: true,\n",
    "    configurable: true\n",
    "});\n",
    "\n",
    "console.log(`Product:`, product);\n",
    "console.log(`Name descriptor:`, Object.getOwnPropertyDescriptor(product, 'name'));\n",
    "\n",
    "// Getters and setters\n",
    "const temperature = {\n",
    "    _celsius: 0,\n",
    "    \n",
    "    get celsius() {\n",
    "        return this._celsius;\n",
    "    },\n",
    "    \n",
    "    set celsius(value) {\n",
    "        if (value < -273.15) {\n",
    "            throw new Error(\"Temperature below absolute zero!\");\n",
    "        }\n",
    "        this._celsius = value;\n",
    "    },\n",
    "    \n",
    "    get fahrenheit() {\n",
    "        return (this._celsius * 9/5) + 32;\n",
    "    }\n",
    "};\n",
    "\n",
    "temperature.celsius = 25;\n",
    "console.log(`\\nTemperature: ${temperature.celsius}°C = ${temperature.fahrenheit}°F`);\n",
    "\n",
    "// Prototype chain\n",
    "console.log(\"\\n=== Prototype Chain ===\");\n",
    "\n",
    "console.log(`bob instanceof Person: ${bob instanceof Person}`);\n",
    "console.log(`bob.__proto__ === Person.prototype: ${bob.__proto__ === Person.prototype}`);\n",
    "console.log(`Person.prototype.constructor === Person: ${Person.prototype.constructor === Person}`);\n",
    "\n",
    "// Checking prototype chain\n",
    "console.log(`bob.hasOwnProperty('name'): ${bob.hasOwnProperty('name')}`);\n",
    "console.log(`bob.hasOwnProperty('greet'): ${bob.hasOwnProperty('greet')}`);\n",
    "console.log(`'greet' in bob: ${'greet' in bob}`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "dfa02e04",
   "metadata": {},
   "source": [
    "# Arrays and Array Methods {#arrays}\n",
    "\n",
    "Arrays are ordered collections of elements. JavaScript provides many powerful methods for array manipulation, iteration, and transformation."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "9440a6fd",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Array creation and basic operations\n",
    "console.log(\"=== Array Creation ===\");\n",
    "\n",
    "// Array literal\n",
    "const fruits = ['apple', 'banana', 'cherry'];\n",
    "console.log(`Original array: ${fruits}`);\n",
    "\n",
    "// Array constructor\n",
    "const numbers = new Array(1, 2, 3, 4, 5);\n",
    "const emptyArray = new Array(5); // Creates array with 5 empty slots\n",
    "console.log(`Numbers: ${numbers}`);\n",
    "console.log(`Empty array length: ${emptyArray.length}`);\n",
    "\n",
    "// Array.from() and Array.of()\n",
    "const fromString = Array.from('hello');\n",
    "const fromRange = Array.from({length: 5}, (_, i) => i * 2);\n",
    "const ofNumbers = Array.of(1, 2, 3, 4, 5);\n",
    "\n",
    "console.log(`From string: ${fromString}`);\n",
    "console.log(`From range: ${fromRange}`);\n",
    "console.log(`Array.of: ${ofNumbers}`);\n",
    "\n",
    "// Array modification methods\n",
    "console.log(\"\\n=== Array Modification ===\");\n",
    "\n",
    "// Adding elements\n",
    "fruits.push('date');           // Add to end\n",
    "fruits.unshift('apricot');     // Add to beginning\n",
    "console.log(`After push/unshift: ${fruits}`);\n",
    "\n",
    "// Removing elements\n",
    "const lastFruit = fruits.pop();     // Remove from end\n",
    "const firstFruit = fruits.shift();  // Remove from beginning\n",
    "console.log(`Removed: ${firstFruit}, ${lastFruit}`);\n",
    "console.log(`After pop/shift: ${fruits}`);\n",
    "\n",
    "// Splice method (add/remove at any position)\n",
    "const removed = fruits.splice(1, 1, 'blueberry', 'grape');\n",
    "console.log(`Removed by splice: ${removed}`);\n",
    "console.log(`After splice: ${fruits}`);\n",
    "\n",
    "// Array iteration methods\n",
    "console.log(\"\\n=== Array Iteration ===\");\n",
    "\n",
    "const nums = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n",
    "\n",
    "// forEach - execute function for each element\n",
    "console.log('forEach:');\n",
    "nums.forEach((num, index) => {\n",
    "    if (index < 3) console.log(`Index ${index}: ${num}`);\n",
    "});\n",
    "\n",
    "// map - transform each element\n",
    "const squared = nums.map(num => num ** 2);\n",
    "console.log(`\\nSquared: ${squared.slice(0, 5)}...`);\n",
    "\n",
    "// filter - select elements that match condition\n",
    "const evenNums = nums.filter(num => num % 2 === 0);\n",
    "console.log(`Even numbers: ${evenNums}`);\n",
    "\n",
    "// reduce - accumulate values\n",
    "const sum = nums.reduce((acc, num) => acc + num, 0);\n",
    "const product = nums.reduce((acc, num) => acc * num, 1);\n",
    "console.log(`Sum: ${sum}, Product: ${product}`);\n",
    "\n",
    "// find and findIndex\n",
    "const found = nums.find(num => num > 5);\n",
    "const foundIndex = nums.findIndex(num => num > 5);\n",
    "console.log(`\\nFirst number > 5: ${found} at index ${foundIndex}`);\n",
    "\n",
    "// some and every\n",
    "const hasEven = nums.some(num => num % 2 === 0);\n",
    "const allPositive = nums.every(num => num > 0);\n",
    "console.log(`Has even numbers: ${hasEven}, All positive: ${allPositive}`);\n",
    "\n",
    "// Array searching and sorting\n",
    "console.log(\"\\n=== Array Searching and Sorting ===\");\n",
    "\n",
    "// includes and indexOf\n",
    "console.log(`Includes 5: ${nums.includes(5)}`);\n",
    "console.log(`Index of 5: ${nums.indexOf(5)}`);\n",
    "console.log(`Last index of 5: ${nums.lastIndexOf(5)}`);\n",
    "\n",
    "// Sorting\n",
    "const words = ['banana', 'apple', 'cherry', 'date'];\n",
    "const sortedWords = [...words].sort(); // Create copy before sorting\n",
    "console.log(`\\nOriginal: ${words}`);\n",
    "console.log(`Sorted: ${sortedWords}`);\n",
    "\n",
    "// Custom sort\n",
    "const people = [\n",
    "    { name: 'Alice', age: 30 },\n",
    "    { name: 'Bob', age: 25 },\n",
    "    { name: 'Charlie', age: 35 }\n",
    "];\n",
    "\n",
    "const sortedByAge = [...people].sort((a, b) => a.age - b.age);\n",
    "console.log('\\nSorted by age:', sortedByAge.map(p => `${p.name}(${p.age})`));\n",
    "\n",
    "// Array utility methods\n",
    "console.log(\"\\n=== Array Utilities ===\");\n",
    "\n",
    "// join and split\n",
    "const joined = fruits.join(' | ');\n",
    "console.log(`Joined: ${joined}`);\n",
    "\n",
    "// slice (non-mutating)\n",
    "const sliced = nums.slice(2, 5);\n",
    "console.log(`Sliced (2-5): ${sliced}`);\n",
    "\n",
    "// concat\n",
    "const moreNums = [11, 12, 13];\n",
    "const combined = nums.concat(moreNums);\n",
    "console.log(`Combined length: ${combined.length}`);\n",
    "\n",
    "// flat and flatMap\n",
    "const nested = [[1, 2], [3, 4], [5, [6, 7]]];\n",
    "const flattened = nested.flat();\n",
    "const deepFlattened = nested.flat(2);\n",
    "console.log(`\\nNested: ${JSON.stringify(nested)}`);\n",
    "console.log(`Flattened: ${flattened}`);\n",
    "console.log(`Deep flattened: ${deepFlattened}`);\n",
    "\n",
    "// Array destructuring\n",
    "console.log(\"\\n=== Array Destructuring ===\");\n",
    "\n",
    "const [first, second, ...rest] = nums;\n",
    "console.log(`First: ${first}, Second: ${second}, Rest length: ${rest.length}`);\n",
    "\n",
    "// Swapping variables\n",
    "let a = 1, b = 2;\n",
    "[a, b] = [b, a];\n",
    "console.log(`After swap: a=${a}, b=${b}`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "bc9108b2",
   "metadata": {},
   "source": [
    "# DOM Manipulation and Events {#dom}\n",
    "\n",
    "The Document Object Model (DOM) allows JavaScript to interact with HTML elements. This section covers element selection, manipulation, and event handling."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e5eb596c",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Note: These examples work in a browser environment\n",
    "// For Node.js, you would need a DOM library like jsdom\n",
    "\n",
    "// Element selection\n",
    "console.log(\"=== DOM Element Selection ===\");\n",
    "\n",
    "// Basic selectors (browser only)\n",
    "/*\n",
    "// By ID\n",
    "const elementById = document.getElementById('myId');\n",
    "\n",
    "// By class name\n",
    "const elementsByClass = document.getElementsByClassName('myClass');\n",
    "\n",
    "// By tag name\n",
    "const elementsByTag = document.getElementsByTagName('div');\n",
    "\n",
    "// Query selectors (more flexible)\n",
    "const element = document.querySelector('.myClass');\n",
    "const elements = document.querySelectorAll('div.myClass');\n",
    "\n",
    "// Advanced selectors\n",
    "const firstChild = document.querySelector('ul > li:first-child');\n",
    "const dataElements = document.querySelectorAll('[data-type=\"example\"]');\n",
    "*/\n",
    "\n",
    "// Creating a mock DOM environment for demonstration\n",
    "class MockElement {\n",
    "    constructor(tagName) {\n",
    "        this.tagName = tagName;\n",
    "        this.textContent = '';\n",
    "        this.innerHTML = '';\n",
    "        this.style = {};\n",
    "        this.classList = new Set();\n",
    "        this.attributes = new Map();\n",
    "        this.children = [];\n",
    "        this.eventListeners = new Map();\n",
    "    }\n",
    "    \n",
    "    setAttribute(name, value) {\n",
    "        this.attributes.set(name, value);\n",
    "    }\n",
    "    \n",
    "    getAttribute(name) {\n",
    "        return this.attributes.get(name);\n",
    "    }\n",
    "    \n",
    "    addEventListener(event, handler) {\n",
    "        if (!this.eventListeners.has(event)) {\n",
    "            this.eventListeners.set(event, []);\n",
    "        }\n",
    "        this.eventListeners.get(event).push(handler);\n",
    "    }\n",
    "    \n",
    "    removeEventListener(event, handler) {\n",
    "        if (this.eventListeners.has(event)) {\n",
    "            const handlers = this.eventListeners.get(event);\n",
    "            const index = handlers.indexOf(handler);\n",
    "            if (index > -1) handlers.splice(index, 1);\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    appendChild(child) {\n",
    "        this.children.push(child);\n",
    "    }\n",
    "    \n",
    "    removeChild(child) {\n",
    "        const index = this.children.indexOf(child);\n",
    "        if (index > -1) this.children.splice(index, 1);\n",
    "    }\n",
    "}\n",
    "\n",
    "// Element manipulation examples\n",
    "console.log(\"\\n=== DOM Element Manipulation ===\");\n",
    "\n",
    "const element = new MockElement('div');\n",
    "\n",
    "// Text content\n",
    "element.textContent = 'Hello, World!';\n",
    "console.log(`Text content: ${element.textContent}`);\n",
    "\n",
    "// HTML content\n",
    "element.innerHTML = '<span>Hello, <strong>World!</strong></span>';\n",
    "console.log(`HTML content: ${element.innerHTML}`);\n",
    "\n",
    "// Attributes\n",
    "element.setAttribute('id', 'myElement');\n",
    "element.setAttribute('data-value', '123');\n",
    "console.log(`ID attribute: ${element.getAttribute('id')}`);\n",
    "console.log(`Data attribute: ${element.getAttribute('data-value')}`);\n",
    "\n",
    "// CSS classes\n",
    "element.classList.add('active');\n",
    "element.classList.add('highlight');\n",
    "console.log(`Classes: ${Array.from(element.classList).join(', ')}`);\n",
    "\n",
    "// Styles\n",
    "element.style.color = 'blue';\n",
    "element.style.fontSize = '16px';\n",
    "element.style.backgroundColor = 'lightgray';\n",
    "console.log(`Styles:`, element.style);\n",
    "\n",
    "// Event handling examples\n",
    "console.log(\"\\n=== Event Handling ===\");\n",
    "\n",
    "// Event listener functions\n",
    "function handleClick(event) {\n",
    "    console.log('Element clicked!');\n",
    "}\n",
    "\n",
    "function handleMouseOver(event) {\n",
    "    console.log('Mouse over element!');\n",
    "}\n",
    "\n",
    "// Adding event listeners\n",
    "element.addEventListener('click', handleClick);\n",
    "element.addEventListener('mouseover', handleMouseOver);\n",
    "\n",
    "// Arrow function event listener\n",
    "element.addEventListener('keydown', (event) => {\n",
    "    console.log(`Key pressed: ${event.key}`);\n",
    "});\n",
    "\n",
    "console.log(`Event listeners added: ${element.eventListeners.size} types`);\n",
    "\n",
    "// Event delegation example (conceptual)\n",
    "const parentElement = new MockElement('ul');\n",
    "const listItem1 = new MockElement('li');\n",
    "const listItem2 = new MockElement('li');\n",
    "\n",
    "listItem1.textContent = 'Item 1';\n",
    "listItem2.textContent = 'Item 2';\n",
    "\n",
    "parentElement.appendChild(listItem1);\n",
    "parentElement.appendChild(listItem2);\n",
    "\n",
    "// Event delegation - handle clicks on parent\n",
    "parentElement.addEventListener('click', (event) => {\n",
    "    if (event.target.tagName === 'LI') {\n",
    "        console.log(`Clicked on: ${event.target.textContent}`);\n",
    "    }\n",
    "});\n",
    "\n",
    "console.log(`\\nParent element has ${parentElement.children.length} children`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "b1d7dad6",
   "metadata": {},
   "source": [
    "# Asynchronous JavaScript (Promises, Async/Await) {#async}\n",
    "\n",
    "JavaScript handles asynchronous operations through callbacks, Promises, and async/await syntax. This enables non-blocking code execution."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "6dbdc6d7",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Callbacks (traditional approach)\n",
    "console.log(\"=== Callbacks ===\");\n",
    "\n",
    "function fetchDataCallback(callback) {\n",
    "    setTimeout(() => {\n",
    "        const data = { id: 1, name: \"John Doe\" };\n",
    "        callback(null, data); // null for error, data for success\n",
    "    }, 1000);\n",
    "}\n",
    "\n",
    "console.log(\"Fetching data with callback...\");\n",
    "fetchDataCallback((error, data) => {\n",
    "    if (error) {\n",
    "        console.error(\"Error:\", error);\n",
    "    } else {\n",
    "        console.log(\"Callback data:\", data);\n",
    "    }\n",
    "});\n",
    "\n",
    "// Promises\n",
    "console.log(\"\\n=== Promises ===\");\n",
    "\n",
    "// Creating a Promise\n",
    "function fetchDataPromise() {\n",
    "    return new Promise((resolve, reject) => {\n",
    "        setTimeout(() => {\n",
    "            const success = Math.random() > 0.3; // 70% success rate\n",
    "            if (success) {\n",
    "                resolve({ id: 2, name: \"Jane Doe\" });\n",
    "            } else {\n",
    "                reject(new Error(\"Failed to fetch data\"));\n",
    "            }\n",
    "        }, 1000);\n",
    "    });\n",
    "}\n",
    "\n",
    "// Using Promises with .then() and .catch()\n",
    "console.log(\"Fetching data with Promise...\");\n",
    "fetchDataPromise()\n",
    "    .then(data => {\n",
    "        console.log(\"Promise data:\", data);\n",
    "        return data.id; // Return value for next .then()\n",
    "    })\n",
    "    .then(id => {\n",
    "        console.log(\"User ID:\", id);\n",
    "    })\n",
    "    .catch(error => {\n",
    "        console.error(\"Promise error:\", error.message);\n",
    "    })\n",
    "    .finally(() => {\n",
    "        console.log(\"Promise completed\");\n",
    "    });\n",
    "\n",
    "// Promise.all() - wait for all promises\n",
    "const promise1 = Promise.resolve(\"First\");\n",
    "const promise2 = Promise.resolve(\"Second\");\n",
    "const promise3 = new Promise(resolve => {\n",
    "    setTimeout(() => resolve(\"Third\"), 500);\n",
    "});\n",
    "\n",
    "Promise.all([promise1, promise2, promise3])\n",
    "    .then(values => {\n",
    "        console.log(\"\\nPromise.all results:\", values);\n",
    "    });\n",
    "\n",
    "// Promise.race() - first to complete\n",
    "const fastPromise = new Promise(resolve => setTimeout(() => resolve(\"Fast\"), 100));\n",
    "const slowPromise = new Promise(resolve => setTimeout(() => resolve(\"Slow\"), 500));\n",
    "\n",
    "Promise.race([fastPromise, slowPromise])\n",
    "    .then(value => {\n",
    "        console.log(\"Promise.race winner:\", value);\n",
    "    });\n",
    "\n",
    "// Async/Await\n",
    "console.log(\"\\n=== Async/Await ===\");\n",
    "\n",
    "// Async function declaration\n",
    "async function fetchUserData(userId) {\n",
    "    try {\n",
    "        console.log(`Fetching user ${userId}...`);\n",
    "        \n",
    "        // Simulate API call\n",
    "        const userData = await new Promise((resolve, reject) => {\n",
    "            setTimeout(() => {\n",
    "                if (userId > 0) {\n",
    "                    resolve({ id: userId, name: `User ${userId}`, email: `user${userId}@example.com` });\n",
    "                } else {\n",
    "                    reject(new Error(\"Invalid user ID\"));\n",
    "                }\n",
    "            }, 800);\n",
    "        });\n",
    "        \n",
    "        console.log(\"User data:\", userData);\n",
    "        return userData;\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error(\"Error fetching user:\", error.message);\n",
    "        throw error; // Re-throw if needed\n",
    "    }\n",
    "}\n",
    "\n",
    "// Using async function\n",
    "fetchUserData(1)\n",
    "    .then(user => {\n",
    "        console.log(\"Received user:\", user.name);\n",
    "    })\n",
    "    .catch(error => {\n",
    "        console.error(\"Failed to get user:\", error.message);\n",
    "    });\n",
    "\n",
    "// Async arrow function\n",
    "const processMultipleUsers = async (userIds) => {\n",
    "    console.log(\"\\nProcessing multiple users...\");\n",
    "    \n",
    "    try {\n",
    "        // Sequential processing\n",
    "        const users = [];\n",
    "        for (const id of userIds) {\n",
    "            const user = await fetchUserData(id);\n",
    "            users.push(user);\n",
    "        }\n",
    "        \n",
    "        console.log(\"All users processed:\", users.length);\n",
    "        return users;\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error(\"Error processing users:\", error.message);\n",
    "    }\n",
    "};\n",
    "\n",
    "// Parallel processing with Promise.all\n",
    "const processUsersParallel = async (userIds) => {\n",
    "    console.log(\"\\nProcessing users in parallel...\");\n",
    "    \n",
    "    try {\n",
    "        const userPromises = userIds.map(id => fetchUserData(id));\n",
    "        const users = await Promise.all(userPromises);\n",
    "        \n",
    "        console.log(\"All users processed in parallel:\", users.length);\n",
    "        return users;\n",
    "        \n",
    "    } catch (error) {\n",
    "        console.error(\"Error in parallel processing:\", error.message);\n",
    "    }\n",
    "};\n",
    "\n",
    "// Error handling with async/await\n",
    "async function demonstrateErrorHandling() {\n",
    "    try {\n",
    "        await fetchUserData(-1); // This will fail\n",
    "    } catch (error) {\n",
    "        console.log(\"\\nCaught error in async function:\", error.message);\n",
    "    }\n",
    "}\n",
    "\n",
    "demonstrateErrorHandling();\n",
    "\n",
    "// Promise utilities\n",
    "console.log(\"\\n=== Promise Utilities ===\");\n",
    "\n",
    "// Creating resolved/rejected promises\n",
    "const resolvedPromise = Promise.resolve(\"Already resolved\");\n",
    "const rejectedPromise = Promise.reject(new Error(\"Already rejected\"));\n",
    "\n",
    "resolvedPromise.then(value => console.log(\"Resolved:\", value));\n",
    "rejectedPromise.catch(error => console.log(\"Rejected:\", error.message));\n",
    "\n",
    "// Promise.allSettled() - wait for all, regardless of outcome\n",
    "const mixedPromises = [\n",
    "    Promise.resolve(\"Success 1\"),\n",
    "    Promise.reject(new Error(\"Error 1\")),\n",
    "    Promise.resolve(\"Success 2\")\n",
    "];\n",
    "\n",
    "Promise.allSettled(mixedPromises)\n",
    "    .then(results => {\n",
    "        console.log(\"\\nPromise.allSettled results:\");\n",
    "        results.forEach((result, index) => {\n",
    "            if (result.status === 'fulfilled') {\n",
    "                console.log(`  ${index}: Success - ${result.value}`);\n",
    "            } else {\n",
    "                console.log(`  ${index}: Failed - ${result.reason.message}`);\n",
    "            }\n",
    "        });\n",
    "    });"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "09b7097e",
   "metadata": {},
   "source": [
    "# ES6+ Features (Destructuring, Spread, Rest) {#es6}\n",
    "\n",
    "ES6 (ES2015) and later versions introduced many powerful features that make JavaScript more expressive and concise."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "bdaf0635",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Destructuring Assignment\n",
    "console.log(\"=== Destructuring ===\");\n",
    "\n",
    "// Array destructuring\n",
    "const colors = ['red', 'green', 'blue', 'yellow'];\n",
    "const [primary, secondary, ...otherColors] = colors;\n",
    "\n",
    "console.log(`Primary: ${primary}`);\n",
    "console.log(`Secondary: ${secondary}`);\n",
    "console.log(`Other colors: ${otherColors}`);\n",
    "\n",
    "// Skipping elements\n",
    "const [first, , third] = colors;\n",
    "console.log(`First: ${first}, Third: ${third}`);\n",
    "\n",
    "// Default values\n",
    "const [a, b, c, d, e = 'default'] = colors;\n",
    "console.log(`Fifth color: ${e}`);\n",
    "\n",
    "// Object destructuring\n",
    "const person = {\n",
    "    name: 'Alice',\n",
    "    age: 30,\n",
    "    city: 'New York',\n",
    "    country: 'USA'\n",
    "};\n",
    "\n",
    "const { name, age, ...address } = person;\n",
    "console.log(`\\nName: ${name}, Age: ${age}`);\n",
    "console.log(`Address:`, address);\n",
    "\n",
    "// Renaming variables\n",
    "const { name: fullName, age: years } = person;\n",
    "console.log(`Full name: ${fullName}, Years: ${years}`);\n",
    "\n",
    "// Nested destructuring\n",
    "const user = {\n",
    "    id: 1,\n",
    "    profile: {\n",
    "        name: 'Bob',\n",
    "        contact: {\n",
    "            email: '<EMAIL>',\n",
    "            phone: '************'\n",
    "        }\n",
    "    }\n",
    "};\n",
    "\n",
    "const { profile: { name: userName, contact: { email } } } = user;\n",
    "console.log(`\\nUser: ${userName}, Email: ${email}`);\n",
    "\n",
    "// Spread Operator\n",
    "console.log(\"\\n=== Spread Operator ===\");\n",
    "\n",
    "// Array spread\n",
    "const arr1 = [1, 2, 3];\n",
    "const arr2 = [4, 5, 6];\n",
    "const combined = [...arr1, ...arr2];\n",
    "const withExtra = [0, ...arr1, 3.5, ...arr2, 7];\n",
    "\n",
    "console.log(`Combined: ${combined}`);\n",
    "console.log(`With extra: ${withExtra}`);\n",
    "\n",
    "// Array copying\n",
    "const original = [1, 2, 3];\n",
    "const copy = [...original];\n",
    "copy.push(4);\n",
    "console.log(`Original: ${original}, Copy: ${copy}`);\n",
    "\n",
    "// Object spread\n",
    "const obj1 = { a: 1, b: 2 };\n",
    "const obj2 = { c: 3, d: 4 };\n",
    "const mergedObj = { ...obj1, ...obj2 };\n",
    "const extendedObj = { ...obj1, b: 20, e: 5 }; // Override and add\n",
    "\n",
    "console.log(`\\nMerged object:`, mergedObj);\n",
    "console.log(`Extended object:`, extendedObj);\n",
    "\n",
    "// Function arguments spread\n",
    "function sum(a, b, c) {\n",
    "    return a + b + c;\n",
    "}\n",
    "\n",
    "const numbers = [1, 2, 3];\n",
    "console.log(`Sum using spread: ${sum(...numbers)}`);\n",
    "\n",
    "// Rest Parameters\n",
    "console.log(\"\\n=== Rest Parameters ===\");\n",
    "\n",
    "// Function with rest parameters\n",
    "function multiply(multiplier, ...numbers) {\n",
    "    return numbers.map(num => num * multiplier);\n",
    "}\n",
    "\n",
    "console.log(`Multiply by 2: ${multiply(2, 1, 2, 3, 4, 5)}`);\n",
    "\n",
    "// Rest in destructuring\n",
    "const [head, ...tail] = [1, 2, 3, 4, 5];\n",
    "console.log(`Head: ${head}, Tail: ${tail}`);\n",
    "\n",
    "const { x, ...remaining } = { x: 1, y: 2, z: 3 };\n",
    "console.log(`x: ${x}, Remaining:`, remaining);\n",
    "\n",
    "// Template Literals\n",
    "console.log(\"\\n=== Template Literals ===\");\n",
    "\n",
    "const product = 'laptop';\n",
    "const price = 999;\n",
    "const discount = 0.1;\n",
    "\n",
    "// Multi-line strings\n",
    "const description = `\n",
    "Product: ${product}\n",
    "Price: $${price}\n",
    "Discount: ${discount * 100}%\n",
    "Final Price: $${price * (1 - discount)}\n",
    "`;\n",
    "\n",
    "console.log(description);\n",
    "\n",
    "// Tagged template literals\n",
    "function highlight(strings, ...values) {\n",
    "    return strings.reduce((result, string, i) => {\n",
    "        const value = values[i] ? `**${values[i]}**` : '';\n",
    "        return result + string + value;\n",
    "    }, '');\n",
    "}\n",
    "\n",
    "const highlighted = highlight`The price is ${price} with ${discount * 100}% discount`;\n",
    "console.log(`Highlighted: ${highlighted}`);\n",
    "\n",
    "// Enhanced Object Literals\n",
    "console.log(\"\\n=== Enhanced Object Literals ===\");\n",
    "\n",
    "const propName = 'dynamicProp';\n",
    "const value = 'dynamic value';\n",
    "\n",
    "const enhancedObj = {\n",
    "    // Shorthand property\n",
    "    name,\n",
    "    age,\n",
    "    \n",
    "    // Computed property names\n",
    "    [propName]: value,\n",
    "    [`${propName}2`]: 'another value',\n",
    "    \n",
    "    // Method shorthand\n",
    "    greet() {\n",
    "        return `Hello, I'm ${this.name}`;\n",
    "    },\n",
    "    \n",
    "    // Getter and setter\n",
    "    get info() {\n",
    "        return `${this.name} (${this.age})`;\n",
    "    },\n",
    "    \n",
    "    set info(value) {\n",
    "        [this.name, this.age] = value.split(' ');\n",
    "    }\n",
    "};\n",
    "\n",
    "console.log(`Enhanced object:`, enhancedObj);\n",
    "console.log(`Greeting: ${enhancedObj.greet()}`);\n",
    "console.log(`Info: ${enhancedObj.info}`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "ded1ffb4",
   "metadata": {},
   "source": [
    "# Classes and Inheritance {#classes}\n",
    "\n",
    "ES6 introduced class syntax that provides a cleaner way to create objects and handle inheritance, built on top of JavaScript's prototype system."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "b35c08d6",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Basic class definition\n",
    "console.log(\"=== Basic Classes ===\");\n",
    "\n",
    "class Animal {\n",
    "    constructor(name, species) {\n",
    "        this.name = name;\n",
    "        this.species = species;\n",
    "    }\n",
    "    \n",
    "    // Instance method\n",
    "    makeSound() {\n",
    "        return `${this.name} makes a sound`;\n",
    "    }\n",
    "    \n",
    "    // Getter\n",
    "    get description() {\n",
    "        return `${this.name} is a ${this.species}`;\n",
    "    }\n",
    "    \n",
    "    // Setter\n",
    "    set name(newName) {\n",
    "        if (newName.length > 0) {\n",
    "            this._name = newName;\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    get name() {\n",
    "        return this._name;\n",
    "    }\n",
    "    \n",
    "    // Static method\n",
    "    static getKingdom() {\n",
    "        return 'Animalia';\n",
    "    }\n",
    "}\n",
    "\n",
    "// Creating instances\n",
    "const animal = new Animal('Generic', 'Unknown');\n",
    "console.log(animal.description);\n",
    "console.log(animal.makeSound());\n",
    "console.log(`Kingdom: ${Animal.getKingdom()}`);\n",
    "\n",
    "// Inheritance\n",
    "console.log(\"\\n=== Inheritance ===\");\n",
    "\n",
    "class Dog extends Animal {\n",
    "    constructor(name, breed) {\n",
    "        super(name, 'Dog'); // Call parent constructor\n",
    "        this.breed = breed;\n",
    "    }\n",
    "    \n",
    "    // Override parent method\n",
    "    makeSound() {\n",
    "        return `${this.name} barks: Woof!`;\n",
    "    }\n",
    "    \n",
    "    // New method specific to Dog\n",
    "    fetch(item) {\n",
    "        return `${this.name} fetches the ${item}`;\n",
    "    }\n",
    "    \n",
    "    // Override getter\n",
    "    get description() {\n",
    "        return `${super.description} (${this.breed} breed)`;\n",
    "    }\n",
    "}\n",
    "\n",
    "class Cat extends Animal {\n",
    "    constructor(name, color) {\n",
    "        super(name, 'Cat');\n",
    "        this.color = color;\n",
    "    }\n",
    "    \n",
    "    makeSound() {\n",
    "        return `${this.name} meows: Meow!`;\n",
    "    }\n",
    "    \n",
    "    scratch() {\n",
    "        return `${this.name} scratches with claws`;\n",
    "    }\n",
    "}\n",
    "\n",
    "// Using inherited classes\n",
    "const dog = new Dog('Rex', 'German Shepherd');\n",
    "const cat = new Cat('Whiskers', 'Orange');\n",
    "\n",
    "console.log(dog.description);\n",
    "console.log(dog.makeSound());\n",
    "console.log(dog.fetch('ball'));\n",
    "\n",
    "console.log(`\\n${cat.description}`);\n",
    "console.log(cat.makeSound());\n",
    "console.log(cat.scratch());\n",
    "\n",
    "// Polymorphism\n",
    "console.log(\"\\n=== Polymorphism ===\");\n",
    "\n",
    "const animals = [dog, cat, new Animal('Bird', 'Sparrow')];\n",
    "\n",
    "animals.forEach(animal => {\n",
    "    console.log(animal.makeSound());\n",
    "});\n",
    "\n",
    "// Private fields and methods (ES2022)\n",
    "console.log(\"\\n=== Private Fields ===\");\n",
    "\n",
    "class BankAccount {\n",
    "    #balance = 0; // Private field\n",
    "    #accountNumber; // Private field\n",
    "    \n",
    "    constructor(accountNumber, initialBalance = 0) {\n",
    "        this.#accountNumber = accountNumber;\n",
    "        this.#balance = initialBalance;\n",
    "    }\n",
    "    \n",
    "    // Private method\n",
    "    #validateAmount(amount) {\n",
    "        return amount > 0 && typeof amount === 'number';\n",
    "    }\n",
    "    \n",
    "    deposit(amount) {\n",
    "        if (this.#validateAmount(amount)) {\n",
    "            this.#balance += amount;\n",
    "            return true;\n",
    "        }\n",
    "        return false;\n",
    "    }\n",
    "    \n",
    "    withdraw(amount) {\n",
    "        if (this.#validateAmount(amount) && amount <= this.#balance) {\n",
    "            this.#balance -= amount;\n",
    "            return true;\n",
    "        }\n",
    "        return false;\n",
    "    }\n",
    "    \n",
    "    get balance() {\n",
    "        return this.#balance;\n",
    "    }\n",
    "    \n",
    "    get accountInfo() {\n",
    "        return `Account ${this.#accountNumber}: $${this.#balance}`;\n",
    "    }\n",
    "}\n",
    "\n",
    "const account = new BankAccount('12345', 1000);\n",
    "console.log(account.accountInfo);\n",
    "\n",
    "account.deposit(500);\n",
    "console.log(`After deposit: ${account.accountInfo}`);\n",
    "\n",
    "account.withdraw(200);\n",
    "console.log(`After withdrawal: ${account.accountInfo}`);\n",
    "\n",
    "// Static blocks (ES2022)\n",
    "console.log(\"\\n=== Static Blocks ===\");\n",
    "\n",
    "class Configuration {\n",
    "    static #config = {};\n",
    "    \n",
    "    // Static block for initialization\n",
    "    static {\n",
    "        this.#config = {\n",
    "            apiUrl: 'https://api.example.com',\n",
    "            timeout: 5000,\n",
    "            retries: 3\n",
    "        };\n",
    "        console.log('Configuration initialized');\n",
    "    }\n",
    "    \n",
    "    static getConfig(key) {\n",
    "        return this.#config[key];\n",
    "    }\n",
    "    \n",
    "    static getAllConfig() {\n",
    "        return { ...this.#config };\n",
    "    }\n",
    "}\n",
    "\n",
    "console.log('API URL:', Configuration.getConfig('apiUrl'));\n",
    "console.log('All config:', Configuration.getAllConfig());\n",
    "\n",
    "// Mixins pattern\n",
    "console.log(\"\\n=== Mixins ===\");\n",
    "\n",
    "// Mixin functions\n",
    "const Flyable = {\n",
    "    fly() {\n",
    "        return `${this.name} is flying!`;\n",
    "    }\n",
    "};\n",
    "\n",
    "const Swimmable = {\n",
    "    swim() {\n",
    "        return `${this.name} is swimming!`;\n",
    "    }\n",
    "};\n",
    "\n",
    "// Mixin helper function\n",
    "function mixin(target, ...sources) {\n",
    "    Object.assign(target.prototype, ...sources);\n",
    "}\n",
    "\n",
    "class Duck extends Animal {\n",
    "    constructor(name) {\n",
    "        super(name, 'Duck');\n",
    "    }\n",
    "    \n",
    "    makeSound() {\n",
    "        return `${this.name} quacks: Quack!`;\n",
    "    }\n",
    "}\n",
    "\n",
    "// Apply mixins\n",
    "mixin(Duck, Flyable, Swimmable);\n",
    "\n",
    "const duck = new Duck('Donald');\n",
    "console.log(duck.makeSound());\n",
    "console.log(duck.fly());\n",
    "console.log(duck.swim());"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "70e66b75",
   "metadata": {},
   "source": [
    "# Error Handling and Debugging {#errors}\n",
    "\n",
    "JavaScript provides robust error handling mechanisms and debugging tools to help identify and resolve issues in your code."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "109dc761",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Basic try-catch-finally\n",
    "console.log(\"=== Error Handling ===\");\n",
    "\n",
    "function divide(a, b) {\n",
    "    try {\n",
    "        if (b === 0) {\n",
    "            throw new Error('Division by zero is not allowed');\n",
    "        }\n",
    "        return a / b;\n",
    "    } catch (error) {\n",
    "        console.error('Error:', error.message);\n",
    "        return null;\n",
    "    } finally {\n",
    "        console.log('Division operation completed');\n",
    "    }\n",
    "}\n",
    "\n",
    "console.log('Result:', divide(10, 2));\n",
    "console.log('Result:', divide(10, 0));\n",
    "\n",
    "// Custom error types\n",
    "class ValidationError extends Error {\n",
    "    constructor(message, field) {\n",
    "        super(message);\n",
    "        this.name = 'ValidationError';\n",
    "        this.field = field;\n",
    "    }\n",
    "}\n",
    "\n",
    "function validateAge(age) {\n",
    "    if (typeof age !== 'number') {\n",
    "        throw new ValidationError('Age must be a number', 'age');\n",
    "    }\n",
    "    if (age < 0 || age > 150) {\n",
    "        throw new ValidationError('Age must be between 0 and 150', 'age');\n",
    "    }\n",
    "    return true;\n",
    "}\n",
    "\n",
    "try {\n",
    "    validateAge('25');\n",
    "} catch (error) {\n",
    "    if (error instanceof ValidationError) {\n",
    "        console.log(`\\nValidation Error in ${error.field}: ${error.message}`);\n",
    "    }\n",
    "}\n",
    "\n",
    "// Debugging techniques\n",
    "console.log(\"\\n=== Debugging ===\");\n",
    "\n",
    "// Console methods\n",
    "console.log('Basic log message');\n",
    "console.warn('Warning message');\n",
    "console.error('Error message');\n",
    "console.info('Info message');\n",
    "\n",
    "// Console.table for objects/arrays\n",
    "const users = [\n",
    "    { name: 'Alice', age: 30, city: 'New York' },\n",
    "    { name: 'Bob', age: 25, city: 'London' },\n",
    "    { name: 'Charlie', age: 35, city: 'Tokyo' }\n",
    "];\n",
    "console.table(users);\n",
    "\n",
    "// Console.group for organized logging\n",
    "console.group('User Processing');\n",
    "users.forEach(user => {\n",
    "    console.log(`Processing ${user.name}`);\n",
    "    console.log(`  Age: ${user.age}`);\n",
    "    console.log(`  City: ${user.city}`);\n",
    "});\n",
    "console.groupEnd();\n",
    "\n",
    "// Performance timing\n",
    "console.time('Array Processing');\n",
    "const largeArray = Array.from({length: 100000}, (_, i) => i);\n",
    "const processed = largeArray.map(x => x * 2).filter(x => x % 100 === 0);\n",
    "console.timeEnd('Array Processing');\n",
    "console.log(`Processed ${processed.length} items`);"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "3ef56820",
   "metadata": {},
   "source": [
    "# Best Practices and Code Style {#best-practices}\n",
    "\n",
    "Following best practices and consistent code style makes JavaScript code more readable, maintainable, and less prone to errors."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "b8426e5b",
   "metadata": {},
   "outputs": [],
   "source": [
    "// Variable declarations\n",
    "console.log(\"=== Best Practices ===\");\n",
    "\n",
    "// Use const for values that don't change\n",
    "const API_URL = 'https://api.example.com';\n",
    "const MAX_RETRIES = 3;\n",
    "\n",
    "// Use let for variables that will change\n",
    "let currentUser = null;\n",
    "let attemptCount = 0;\n",
    "\n",
    "// Avoid var (function-scoped, can cause issues)\n",
    "// var oldStyle = 'avoid this'; // Don't use\n",
    "\n",
    "// Meaningful variable names\n",
    "const userAccountBalance = 1000; // Good\n",
    "// const bal = 1000; // Avoid abbreviations\n",
    "\n",
    "// Function best practices\n",
    "// Pure functions (no side effects)\n",
    "function calculateTax(amount, rate) {\n",
    "    return amount * rate;\n",
    "}\n",
    "\n",
    "// Single responsibility\n",
    "function validateEmail(email) {\n",
    "    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n",
    "    return emailRegex.test(email);\n",
    "}\n",
    "\n",
    "function formatEmail(email) {\n",
    "    return email.toLowerCase().trim();\n",
    "}\n",
    "\n",
    "// Error handling patterns\n",
    "function safeParseJSON(jsonString) {\n",
    "    try {\n",
    "        return { success: true, data: JSON.parse(jsonString) };\n",
    "    } catch (error) {\n",
    "        return { success: false, error: error.message };\n",
    "    }\n",
    "}\n",
    "\n",
    "// Object and array best practices\n",
    "// Use object shorthand\n",
    "const name = 'Alice';\n",
    "const age = 30;\n",
    "const user = { name, age }; // Instead of { name: name, age: age }\n",
    "\n",
    "// Use array methods instead of loops when possible\n",
    "const numbers = [1, 2, 3, 4, 5];\n",
    "const doubled = numbers.map(n => n * 2); // Good\n",
    "// const doubled = []; for(let i = 0; i < numbers.length; i++) { doubled.push(numbers[i] * 2); } // Verbose\n",
    "\n",
    "// Async/await best practices\n",
    "async function fetchUserData(userId) {\n",
    "    try {\n",
    "        const response = await fetch(`/api/users/${userId}`);\n",
    "        if (!response.ok) {\n",
    "            throw new Error(`HTTP error! status: ${response.status}`);\n",
    "        }\n",
    "        return await response.json();\n",
    "    } catch (error) {\n",
    "        console.error('Failed to fetch user data:', error);\n",
    "        throw error; // Re-throw to let caller handle\n",
    "    }\n",
    "}\n",
    "\n",
    "// Code organization\n",
    "console.log(\"\\n=== Code Organization ===\");\n",
    "\n",
    "// Use modules to organize code\n",
    "// utils.js\n",
    "const utils = {\n",
    "    formatCurrency(amount, currency = 'USD') {\n",
    "        return new Intl.NumberFormat('en-US', {\n",
    "            style: 'currency',\n",
    "            currency\n",
    "        }).format(amount);\n",
    "    },\n",
    "    \n",
    "    debounce(func, wait) {\n",
    "        let timeout;\n",
    "        return function executedFunction(...args) {\n",
    "            const later = () => {\n",
    "                clearTimeout(timeout);\n",
    "                func(...args);\n",
    "            };\n",
    "            clearTimeout(timeout);\n",
    "            timeout = setTimeout(later, wait);\n",
    "        };\n",
    "    }\n",
    "};\n",
    "\n",
    "console.log('Formatted currency:', utils.formatCurrency(1234.56));\n",
    "\n",
    "// Performance tips\n",
    "console.log(\"\\n=== Performance Tips ===\");\n",
    "\n",
    "// Use object lookup instead of multiple if/else\n",
    "const statusMessages = {\n",
    "    200: 'Success',\n",
    "    404: 'Not Found',\n",
    "    500: 'Server Error'\n",
    "};\n",
    "\n",
    "function getStatusMessage(code) {\n",
    "    return statusMessages[code] || 'Unknown Status';\n",
    "}\n",
    "\n",
    "// Use Set for unique values\n",
    "const uniqueIds = new Set([1, 2, 3, 2, 1, 4]);\n",
    "console.log('Unique IDs:', Array.from(uniqueIds));\n",
    "\n",
    "// Use Map for key-value pairs with non-string keys\n",
    "const userPreferences = new Map();\n",
    "userPreferences.set(user, { theme: 'dark', language: 'en' });\n",
    "\n",
    "console.log('\\nJavaScript Manual Complete!');\n",
    "console.log('This manual covers the essential JavaScript concepts.');\n",
    "console.log('For more advanced topics, consider exploring:');\n",
    "console.log('- Web APIs (Fetch, WebSockets, Service Workers)');\n",
    "console.log('- Testing frameworks (Jest, Mocha, Cypress)');\n",
    "console.log('- Build tools (Webpack, Vite, Rollup)');\n",
    "console.log('- Frameworks (React, Vue, Angular)');\n",
    "console.log('- TypeScript for type safety');\n",
    "console.log('- Node.js for server-side development');"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "JavaScript (Node.js)",
   "language": "javascript",
   "name": "javascript"
  },
  "language_info": {
   "file_extension": ".js",
   "mimetype": "application/javascript",
   "name": "javascript",
   "version": "18.0.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
 "metadata": {
  "kernelspec": {
   "display_name": "JavaScript (Node.js)",
   "language": "javascript",
   "name": "javascript"
  },
  "language_info": {
   "file_extension": ".js",
   "mimetype": "application/javascript",
   "name": "javascript",
   "version": "18.0.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
