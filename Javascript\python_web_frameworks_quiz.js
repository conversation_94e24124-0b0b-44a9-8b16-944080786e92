/**
 * Python Web Frameworks Quiz
 * Comprehensive quiz covering Django, Flask, and web development concepts
 */

const quizzes = {
  "Web Framework Fundamentals": [
    { 
      question: "What is WSGI and why is it important?", 
      answer: "Web Server Gateway Interface - the standard interface between Python web applications and web servers, enabling portability across different servers." 
    },
    { 
      question: "What's the difference between Flask and Django?", 
      answer: "Flask is a microframework (minimalist, flexible); Django is full-stack (batteries included, convention over configuration)." 
    },
    { 
      question: "What are the main components of the HTTP request/response cycle?", 
      answer: "Client request → Web server → Python application → Framework routing → View processing → Database interaction → Response generation → Client." 
    },
    { 
      question: "What is the MVC pattern and how does it apply to web frameworks?", 
      answer: "Model-View-Controller separates data (Model), presentation (View), and logic (Controller). Django uses MTV (Model-Template-View)." 
    },
    { 
      question: "What are the common HTTP methods and their purposes?", 
      answer: "GET (retrieve), POST (create), PUT (update/replace), PATCH (partial update), DELETE (remove), HEAD (headers only), OPTIONS (allowed methods)." 
    },
    { 
      question: "What are HTTP status codes and why are they important?", 
      answer: "Standardized codes indicating request results: 2xx (success), 3xx (redirection), 4xx (client error), 5xx (server error)." 
    }
  ],

  "Flask Essentials": [
    { 
      question: "How do you create a basic Flask application?", 
      answer: "from flask import Flask; app = Flask(__name__); @app.route('/') def home(): return 'Hello'; app.run()" 
    },
    { 
      question: "What are Flask Blueprints and when should you use them?", 
      answer: "Blueprints organize Flask applications into modular components, useful for large apps with multiple features or API versions." 
    },
    { 
      question: "How do you handle URL parameters in Flask?", 
      answer: "Use route decorators with angle brackets: @app.route('/user/<username>') or @app.route('/post/<int:id>')" 
    },
    { 
      question: "What is the Flask application factory pattern?", 
      answer: "A function that creates and configures Flask app instances, enabling multiple configurations and easier testing." 
    },
    { 
      question: "How do you handle different HTTP methods in Flask?", 
      answer: "@app.route('/api/users', methods=['GET', 'POST']) then check request.method inside the function." 
    },
    { 
      question: "What are Flask extensions and name some important ones?", 
      answer: "Add-on packages that extend Flask functionality: Flask-SQLAlchemy (ORM), Flask-Login (authentication), Flask-WTF (forms)." 
    },
    { 
      question: "How do you handle errors in Flask?", 
      answer: "Use @app.errorhandler(404) decorators or try/except blocks, return appropriate status codes and error messages." 
    }
  ],

  "Django Fundamentals": [
    { 
      question: "What is Django's 'batteries included' philosophy?", 
      answer: "Django provides built-in solutions for common web development needs: ORM, admin interface, authentication, forms, etc." 
    },
    { 
      question: "How do you create a Django project and app?", 
      answer: "django-admin startproject myproject; cd myproject; python manage.py startapp myapp" 
    },
    { 
      question: "What are Django models and how do they work?", 
      answer: "Python classes that define database structure and behavior, using Django's ORM to map to database tables." 
    },
    { 
      question: "What's the difference between Django views and templates?", 
      answer: "Views contain business logic and process requests; templates define HTML structure and presentation logic." 
    },
    { 
      question: "How does Django's URL routing work?", 
      answer: "URLconf maps URL patterns to view functions using path() or re_path(), supporting parameters and namespaces." 
    },
    { 
      question: "What are Django migrations and why are they important?", 
      answer: "Version control for database schema changes, allowing safe deployment and rollback of database modifications." 
    },
    { 
      question: "What is Django's admin interface?", 
      answer: "Automatically generated admin panel for managing database content, customizable for different user roles and permissions." 
    }
  ],

  "Database Integration": [
    { 
      question: "How do you configure database connections in Django?", 
      answer: "In settings.py DATABASES dict, specify ENGINE, NAME, USER, PASSWORD, HOST, PORT for your database." 
    },
    { 
      question: "What is an ORM and what are its benefits?", 
      answer: "Object-Relational Mapping translates between database tables and Python objects, providing abstraction and preventing SQL injection." 
    },
    { 
      question: "How do you perform database queries in Django?", 
      answer: "Model.objects.all(), filter(), get(), create(), update(), delete() methods, plus QuerySet chaining and annotations." 
    },
    { 
      question: "What are Django model relationships?", 
      answer: "ForeignKey (many-to-one), ManyToManyField (many-to-many), OneToOneField (one-to-one) define table relationships." 
    },
    { 
      question: "How do you handle database transactions in Django?", 
      answer: "Use @transaction.atomic decorator or with transaction.atomic(): context manager for ACID compliance." 
    },
    { 
      question: "What is Flask-SQLAlchemy and how does it work?", 
      answer: "Flask extension that integrates SQLAlchemy ORM, providing database models, sessions, and query capabilities." 
    }
  ],

  "Authentication and Security": [
    { 
      question: "How does Django's built-in authentication work?", 
      answer: "Provides User model, login/logout views, password hashing, session management, and permission system." 
    },
    { 
      question: "How do you implement authentication in Flask?", 
      answer: "Use Flask-Login extension with UserMixin, login_manager, @login_required decorator, and session management." 
    },
    { 
      question: "What are CSRF attacks and how do frameworks prevent them?", 
      answer: "Cross-Site Request Forgery attacks; prevented with CSRF tokens in forms that validate request authenticity." 
    },
    { 
      question: "How do you hash passwords securely?", 
      answer: "Use bcrypt, scrypt, or Argon2 with salt; Django uses PBKDF2 by default; never store plain text passwords." 
    },
    { 
      question: "What is SQL injection and how do ORMs prevent it?", 
      answer: "Malicious SQL code injection; ORMs use parameterized queries and escape user input automatically." 
    },
    { 
      question: "How do you implement role-based access control?", 
      answer: "Use Django's groups/permissions system or Flask-Principal; check user roles in views with decorators." 
    }
  ],

  "API Development": [
    { 
      question: "How do you create REST APIs with Django?", 
      answer: "Use Django REST Framework (DRF) with serializers, viewsets, and routers for full-featured APIs." 
    },
    { 
      question: "How do you create APIs with Flask?", 
      answer: "Use Flask-RESTful or plain Flask with jsonify(), handle different HTTP methods, implement proper status codes." 
    },
    { 
      question: "What is API serialization and why is it important?", 
      answer: "Converting Python objects to JSON/XML format for API responses; ensures consistent data format and validation." 
    },
    { 
      question: "How do you handle API authentication?", 
      answer: "Token-based (JWT), API keys, OAuth2, or session-based authentication depending on use case and security needs." 
    },
    { 
      question: "What are API versioning strategies?", 
      answer: "URL versioning (/api/v1/), header versioning, parameter versioning, or content negotiation." 
    },
    { 
      question: "How do you implement API rate limiting?", 
      answer: "Use Flask-Limiter or Django-ratelimit to prevent abuse; implement per-user, per-IP, or per-endpoint limits." 
    }
  ],

  "Testing and Deployment": [
    { 
      question: "How do you test Django applications?", 
      answer: "Use Django's TestCase class, test client for views, fixtures for data, and separate test database." 
    },
    { 
      question: "How do you test Flask applications?", 
      answer: "Use pytest with Flask test client, create test fixtures, mock external dependencies, test routes and functions." 
    },
    { 
      question: "What are the key considerations for deploying web applications?", 
      answer: "Environment configuration, static files, database setup, security settings, monitoring, and scalability." 
    },
    { 
      question: "How do you serve static files in production?", 
      answer: "Use web server (Nginx/Apache) or CDN for static files; Django collectstatic; Flask static folder configuration." 
    },
    { 
      question: "What is WSGI server and name some examples?", 
      answer: "Production server that runs Python web apps; examples: Gunicorn, uWSGI, mod_wsgi, Waitress." 
    },
    { 
      question: "How do you handle environment-specific configuration?", 
      answer: "Use environment variables, separate settings files, or configuration management tools like python-decouple." 
    }
  ]
};

/**
 * Prints quiz questions for a given topic
 * @param {string} topic - The topic name (must match a key in quizzes object)
 * @param {boolean} withAnswers - Whether to include answers (default: false)
 */
function printQuiz(topic, withAnswers = false) {
  if (!quizzes[topic]) {
    console.log(`❌ Topic "${topic}" not found. Available topics:`);
    console.log(Object.keys(quizzes).map((t, i) => `${i + 1}. ${t}`).join('\n'));
    return;
  }

  console.log(`\n🐍 ${topic.toUpperCase()} QUIZ`);
  console.log('='.repeat(50));

  quizzes[topic].forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run printQuiz("${topic}", true) to see answers`);
  }
}

/**
 * Prints all available quiz topics
 */
function listTopics() {
  console.log('\n📋 Available Python Web Frameworks Quiz Topics:');
  console.log('='.repeat(48));
  Object.keys(quizzes).forEach((topic, index) => {
    const questionCount = quizzes[topic].length;
    console.log(`${index + 1}. ${topic} (${questionCount} questions)`);
  });
  console.log('\n💡 Use printQuiz("Topic Name") to start a quiz');
}

/**
 * Runs a random quiz from all topics
 * @param {number} questionCount - Number of random questions to ask (default: 10)
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function randomQuiz(questionCount = 10, withAnswers = false) {
  const allQuestions = [];
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      allQuestions.push({ ...q, topic });
    });
  });

  const shuffled = allQuestions.sort(() => Math.random() - 0.5);
  const selectedQuestions = shuffled.slice(0, questionCount);

  console.log(`\n🎲 RANDOM PYTHON WEB FRAMEWORKS QUIZ (${questionCount} questions)`);
  console.log('='.repeat(50));

  selectedQuestions.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });

  if (!withAnswers) {
    console.log(`\n💡 Run randomQuiz(${questionCount}, true) to see answers`);
  }
}

/**
 * Search for questions containing specific keywords
 * @param {string} keyword - Keyword to search for
 * @param {boolean} withAnswers - Whether to show answers (default: false)
 */
function searchQuestions(keyword, withAnswers = false) {
  const results = [];
  const searchTerm = keyword.toLowerCase();
  
  Object.entries(quizzes).forEach(([topic, questions]) => {
    questions.forEach(q => {
      if (q.question.toLowerCase().includes(searchTerm) || 
          q.answer.toLowerCase().includes(searchTerm)) {
        results.push({ ...q, topic });
      }
    });
  });
  
  if (results.length === 0) {
    console.log(`\n❌ No questions found containing "${keyword}"`);
    return;
  }
  
  console.log(`\n🔍 Found ${results.length} questions containing "${keyword}"`);
  console.log('='.repeat(50));
  
  results.forEach((item, index) => {
    console.log(`\n${index + 1}. [${item.topic}] ${item.question}`);
    
    if (withAnswers) {
      console.log(`   ✅ Answer: ${item.answer}`);
    }
  });
  
  if (!withAnswers) {
    console.log(`\n💡 Run searchQuestions("${keyword}", true) to see answers`);
  }
}

// Export for use in other files (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    quizzes, 
    printQuiz, 
    listTopics, 
    randomQuiz, 
    searchQuestions 
  };
}

// Example usage and help
console.log('🐍 Python Web Frameworks Quiz System Loaded!');
console.log('📖 Available Functions:');
console.log('='.repeat(40));
console.log('📋 listTopics() - Show all available topics');
console.log('🎯 printQuiz("Topic Name") - Start a quiz');
console.log('✅ printQuiz("Topic Name", true) - Quiz with answers');
console.log('🎲 randomQuiz(10) - Random questions from all topics');
console.log('🔍 searchQuestions("keyword") - Search for questions');

console.log('\n💡 Example: printQuiz("Flask Essentials")');
console.log('💡 Example: randomQuiz(5, true) - 5 random questions with answers');
console.log('💡 Example: searchQuestions("django", true) - Find Django-related questions');

// Auto-show available topics on load
console.log('\n📚 Quick Start - Available Topics:');
Object.keys(quizzes).forEach((topic, index) => {
  console.log(`${index + 1}. ${topic}`);
});
